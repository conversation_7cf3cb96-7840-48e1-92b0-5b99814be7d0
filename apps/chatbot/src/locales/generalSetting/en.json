{"botImage": "Avatar", "botName": "Bot name", "botNamePlaceholder": "<PERSON><PERSON><PERSON>", "botSetting": "Bot setting", "description": "Description", "descriptionPlaceholder": "AI assistant", "globalFinishChatbotText": "Global Chatbot Text - <PERSON><PERSON> Ended", "globalFinishChatbotTextDesc": "When turned on, this text will be appeared when the bot conversation is finished", "globalFinishChatbotTextPlaceholder": "The chat has ended.", "globalNoMatch": "Global Default Message - No Intent Matched Found", "globalNoMatchDesc": "The fallback response that will trigger if the user fails to match any intent.", "globalNoMatchPlaceholder": "Edit text", "globalSetting": "Global setting", "pageTitle": "General Setting", "save": "Save", "saveSettingSuccess": "Save setting successfully", "systemImage": "System image", "timeoutFlow": "Timeout flow", "timeoutFlowDescOff": "If the session time out, please proceed to the start flow.", "timeoutFlowDescOn": "If the session time out, please proceed to the timeout flow."}