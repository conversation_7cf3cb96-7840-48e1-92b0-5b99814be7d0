import { TABLES_WEBSOCKET_EVENTS } from '@/constants/event';
import { CommonAPI } from '@/services/api';
import { IWebsocketResponse } from '@/types';
import { createContext, useCallback, useContext, useEffect, useRef, useState } from 'react';
import { useLocation, useParams } from 'react-router-dom';
import { sendCustomEvent } from '@resola-ai/utils';
import { Centrifuge } from 'centrifuge';
import { AppConfig } from '@/configs';
import { useTableFieldsMutation } from '@/hooks';

type EventHandler = (
  data: IWebsocketResponse<TABLES_WEBSOCKET_EVENTS>,
  baseId: string,
  tableId: string
) => Promise<void>;

const useWebSocket = () => {
  const [wsToken, setWsToken] = useState<string>('');
  const [orgId, setOrgId] = useState<string>('');
  const location = useLocation();
  const routeRef = useRef<string>(location.pathname);
  const eventHandlerRef = useRef<EventHandler>();
  const { baseId, tableId } = useParams();

  const retrieveWsToken = useCallback(async () => {
    const res = await CommonAPI.getWsToken();

    if (res.isOk()) {
      const { wsAccessToken, orgId } = res.value;
      setWsToken(wsAccessToken);
      setOrgId(orgId);
    }
  }, []);

  const renewWsToken = useCallback(async () => {
    const res = await CommonAPI.getWsToken();
    if (res.isOk()) {
      const { wsAccessToken } = res.value;
      return wsAccessToken;
    }
    return '';
  }, []);

  const getChannelName = useCallback((orgId: string, baseId: string, tableId: string) => {
    return `tables:ORG_${orgId}|${baseId}-${tableId}`;
  }, []);

  const { handleTableFieldUpdateEvent, handleTableFieldCreateEvent, handleTableFieldDeleteEvent } =
    useTableFieldsMutation();

  const eventHandler: EventHandler = useCallback(
    async (data: IWebsocketResponse<any>, baseId: string, tableId: string) => {
      // console.log('eventHandler', data);

      switch (data.type) {
        case TABLES_WEBSOCKET_EVENTS.FIELDS_CREATE: {
          //handle fields create event
          const response = data;
          const updatedField = response.data.props;
          handleTableFieldCreateEvent(baseId, tableId, updatedField);
          break;
        }
        case TABLES_WEBSOCKET_EVENTS.FIELDS_UPDATE: {
          const response = data;
          const updatedField = response.data.props;
          handleTableFieldUpdateEvent(baseId, tableId, updatedField);
          break;
        }
        case TABLES_WEBSOCKET_EVENTS.FIELDS_DELETE: {
          //handle fields delete event
          const response = data;
          const id = response.data.id;
          handleTableFieldDeleteEvent(baseId, tableId, id);
          break;
        }
        case TABLES_WEBSOCKET_EVENTS.RECORDS_INSERT: {
          //handle records create event
          const response = data;
          sendCustomEvent(TABLES_WEBSOCKET_EVENTS.RECORDS_INSERT, response);
          break;
        }
        case TABLES_WEBSOCKET_EVENTS.RECORDS_UPDATE: {
          //handle records update event
          const response = data;
          sendCustomEvent(TABLES_WEBSOCKET_EVENTS.RECORDS_UPDATE, response);
          break;
        }
        case TABLES_WEBSOCKET_EVENTS.RECORDS_DELETE: {
          //handle records delete event
          const response = data;
          sendCustomEvent(TABLES_WEBSOCKET_EVENTS.RECORDS_DELETE, response);
          break;
        }
        case TABLES_WEBSOCKET_EVENTS.DATA_IMPORT: {
          //handle data import event
          const response = data;
          sendCustomEvent(TABLES_WEBSOCKET_EVENTS.DATA_IMPORT, response);
          break;
        }
        case TABLES_WEBSOCKET_EVENTS.DATA_CLEAR: {
          //handle data clear event
          const response = data;
          sendCustomEvent(TABLES_WEBSOCKET_EVENTS.DATA_CLEAR, response);
          break;
        }
        default:
          break;
      }
    },
    [handleTableFieldUpdateEvent, handleTableFieldDeleteEvent, handleTableFieldCreateEvent, tableId]
  );

  useEffect(() => {
    retrieveWsToken();
  }, []);

  useEffect(() => {
    const eventHandler = eventHandlerRef.current;

    if (!wsToken || !orgId || !baseId || !tableId || !eventHandler) {
      console.log('waiting to connect with centrifugo websocket');
      return;
    }

    const centrifuge = new Centrifuge(AppConfig.WEBSOCKET_URL, {
      debug: !AppConfig.IS_PRODUCTION,
      token: wsToken,
      getToken: renewWsToken,
    });

    const channel = getChannelName(orgId, baseId, tableId);

    const subscription = centrifuge.newSubscription(channel);
    subscription.on('publication', ctx => {
      // console.log('centrifugo answer', ctx.channel, ctx.data);
      eventHandler(ctx.data, baseId, tableId);
    });
    subscription.subscribe();
    centrifuge.connect();
    // console.log('subscribe and connect to centrifugo server');

    centrifuge.on('connected', function (_) {
      // now client connected to Centrifugo and authenticated.
      console.log('centrifugo connected');
    });
    centrifuge.on('error', function (err) {
      const errorCode = err?.error?.code;
      // Reference here : https://github.com/centrifugal/centrifuge/blob/master/errors.go
      if ([101, 109, 110].includes(errorCode)) {
        console.log('renew ws token', errorCode);
        retrieveWsToken();
      }
    });
    return () => {
      subscription.unsubscribe();
      centrifuge.disconnect();
      console.log('unsubscribe & disconnect from centrifugo server');
    };
  }, [wsToken, renewWsToken, retrieveWsToken, baseId, tableId, orgId, getChannelName]);

  useEffect(() => {
    eventHandlerRef.current = eventHandler;
  }, [eventHandler]);

  useEffect(() => {
    routeRef.current = location.pathname;
  }, [location]);

  return {};
};

export type WebSocketContextType = ReturnType<typeof useWebSocket>;

const context = createContext<WebSocketContextType | null>(null);

export const WebSocketContextProvider = ({ children }: { children: React.ReactNode }) => {
  const value = useWebSocket();

  return <context.Provider value={value}>{children}</context.Provider>;
};

export const useWebsocketContext = () => {
  const value = useContext(context);

  if (!value) {
    throw new Error('useWebsocketContext must be used inside WebSocketContextProvider');
  }

  return value;
};
