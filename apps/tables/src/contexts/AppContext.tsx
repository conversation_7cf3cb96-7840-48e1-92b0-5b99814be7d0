import { ConfirmModal, ConfirmModalProps } from '@/components/Common/TableModals';
import { TableLayoutType } from '@/constants/table-base';
import { useDisclosure } from '@mantine/hooks';
import { createContext, useCallback, useContext, useState } from 'react';

type ConfirmModalState = Omit<ConfirmModalProps, 'onCancel'>;
type ConfirmModalOptions = Omit<ConfirmModalState, 'opened'>;

const useApp = () => {
  const [showSidebar, setShowSidebar] = useState(true);
  const [tableDisplayLayout, setTableDisplayLayout] = useState<TableLayoutType>(
    TableLayoutType.TABLE
  );
  const [showCreateNewItems, setShowCreateNewItems] = useState(true);
  const [tablePage, setTablePage] = useState<number>(1);

  const [confirmModal, setConfirmModal] = useState<ConfirmModalState>({
    onConfirm: () => Promise.resolve(),
    opened: false,
    showCancelButton: true,
    confirmButtonType: 'delete',
    content: '',
    title: '',
    cancelText: '',
    confirmText: '',
  });

  const [
    tableTrashViewModalOpened,
    { open: openTableTrashViewModal, close: closeTableTrashViewModal },
  ] = useDisclosure();
  const [pendingClearTableIds, setPendingClearTableIds] = useState<string[]>([]);

  const toggleSidebar = useCallback(() => {
    setShowSidebar(s => !s);
  }, []);

  const toggleCreateNewItems = useCallback(() => {
    setShowCreateNewItems(s => !s);
  }, []);

  const closeConfirmModal = useCallback(() => {
    setConfirmModal(s => ({ ...s, opened: false }));
  }, []);

  const openConfirmModal = useCallback((options: ConfirmModalOptions) => {
    setConfirmModal(s => ({ ...s, ...options, opened: true }));
  }, []);

  const addPendingClearTableId = useCallback((tableId: string) => {
    setPendingClearTableIds(s => [...s, tableId]);
  }, []);

  const removePendingClearTableId = useCallback((tableId: string) => {
    setPendingClearTableIds(s => s.filter(id => id !== tableId));
  }, []);

  return {
    confirmModal,
    openConfirmModal,
    closeConfirmModal,
    showSidebar,
    toggleSidebar,
    tableDisplayLayout,
    setTableDisplayLayout,
    showCreateNewItems,
    toggleCreateNewItems,
    tableTrashViewModalOpened,
    openTableTrashViewModal,
    closeTableTrashViewModal,
    tablePage,
    setTablePage,
    pendingClearTableIds,
    addPendingClearTableId,
    removePendingClearTableId,
  };
};

export type AppContextType = ReturnType<typeof useApp>;

const context = createContext<AppContextType | null>(null);

export const AppContextProvider = ({ children }: { children: React.ReactNode }) => {
  const value = useApp();

  return (
    <context.Provider value={value}>
      {children}
      <ConfirmModal {...value.confirmModal} onCancel={value.closeConfirmModal} />
    </context.Provider>
  );
};

export const useAppContext = () => {
  const value = useContext(context);

  if (!value) {
    throw new Error('useAppContext must be used inside AppContextProvider');
  }

  return value;
};
