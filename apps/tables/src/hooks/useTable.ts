import { TableAPI } from '@/services/api/table';
import { Table } from '@/types';
import { ISuccessResponse } from '@resola-ai/models';
import { useRef } from 'react';
import useSWR from 'swr';

const createTableKey = (baseId: string, tableId: string, random: string) => [
  'bases/tables/',
  baseId,
  tableId,
  random,
];

const useTableQuery = (
  baseId?: string,
  tableId?: string,
  options: { onSuccess?: (data: ISuccessResponse<Table>) => void } = {}
) => {
  const random = useRef(Date.now().toString());
  const tableKey = baseId && tableId ? createTableKey(baseId, tableId, random.current) : null;

  const { data, error, isLoading, isValidating, mutate } = useSWR(
    tableKey,
    async () => {
      const res = await TableAPI.get(baseId!, tableId!);
      if (res.isOk()) {
        return res.value;
      }
      if (res.isErr()) {
        throw new Error(res.error);
      }
      return undefined;
    },
    {
      keepPreviousData: true,
      onSuccess: data => {
        if (!data) return;
        options.onSuccess?.(data);
      },
    }
  );

  return {
    table: data?.data || null,
    isLoading,
    isValidating,
    mutate,
    error,
    key: tableKey,
  };
};

export { useTableQuery };
