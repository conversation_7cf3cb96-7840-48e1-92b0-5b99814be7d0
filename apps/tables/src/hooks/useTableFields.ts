import { useCallback } from 'react';
import { FieldAPI, FieldAPICreatePayload, FieldAPIUpdatePayload } from '@/services/api';
import { Field } from '@/types';
import { useTablesMutation } from './useTables';

type TableFieldUpdateOptions = {
  updateOtherFields?: (field: Field) => Partial<Field>;
};

// Custom hook to update the SWR cache specifically for table field updates
// Note: This hook is used only to update the cache, not to directly render the table data
// The actual data for rendering the table comes from the "fields" property in tables from useTablesQuery
const useTableFieldsMutation = () => {
  const { updateQueryCache } = useTablesMutation();

  const handleTableFieldUpdateEvent = useCallback(
    (baseId: string, tableId: string, updatedField: Field, options?: TableFieldUpdateOptions) => {
      updateQueryCache(baseId, currentData => ({
        ...currentData,
        data: currentData.data.map(table => {
          if (table.id !== tableId) return table;

          const updatedFields = table.fields.map(field => {
            if (field.id === updatedField.id) {
              return {
                ...field,
                ...updatedField,
              };
            }

            if (options?.updateOtherFields) {
              const updates = options.updateOtherFields(field);
              return {
                ...field,
                ...updates,
              };
            }

            return field;
          });

          return {
            ...table,
            fields: updatedFields,
          };
        }),
      }));
    },
    [updateQueryCache]
  );

  const handleTableFieldCreateEvent = useCallback(
    (baseId: string, tableId: string, newField: Field) => {
      updateQueryCache(baseId, currentData => ({
        ...currentData,
        data: currentData.data.map(table => {
          if (table.id !== tableId) return table;
          const updatedFields = [...table.fields, newField];
          return { ...table, fields: updatedFields };
        }),
      }));
    },
    [updateQueryCache]
  );

  const handleTableFieldDeleteEvent = useCallback(
    (baseId: string, tableId: string, fieldId: string) => {
      updateQueryCache(baseId, currentData => ({
        ...currentData,
        data: currentData.data.map(table => {
          if (table.id !== tableId) return table;
          const updatedFields = table.fields.filter(field => field.id !== fieldId);
          return { ...table, fields: updatedFields };
        }),
      }));
    },
    [updateQueryCache]
  );

  const createField = useCallback(
    async (
      baseId: string,
      tableId: string,
      payload: FieldAPICreatePayload,
      options: {
        updateCacheOnSuccess?: boolean;
      } = {
        updateCacheOnSuccess: true,
      }
    ) => {
      const { updateCacheOnSuccess } = options;
      // @TODO: Ask BE if isPrimary needs to be set to false when creating a field
      const res = await FieldAPI.create(baseId, tableId, { ...payload, isPrimary: false });

      if (res.isOk() && updateCacheOnSuccess) {
        handleTableFieldCreateEvent(baseId, tableId, res.value.data);
      }

      return {
        res,
        updateQueryCache: handleTableFieldCreateEvent,
      };
    },
    [handleTableFieldCreateEvent]
  );

  const updateField = useCallback(
    async (baseId: string, tableId: string, fieldId: string, payload: FieldAPIUpdatePayload) => {
      const res = await FieldAPI.update(baseId, tableId, fieldId, payload);
      if (res.isOk()) {
        handleTableFieldUpdateEvent(baseId, tableId, res.value.data);
      }
      return res;
    },
    [handleTableFieldUpdateEvent]
  );

  const removeField = useCallback(
    async (baseId: string, tableId: string, fieldId: string) => {
      const res = await FieldAPI.remove(baseId, tableId, fieldId);
      if (res.isOk()) {
        handleTableFieldDeleteEvent(baseId, tableId, fieldId);
      }
      return res;
    },
    [handleTableFieldDeleteEvent]
  );

  const setPrimaryField = useCallback(
    async (baseId: string, tableId: string, fieldId: string, payload: FieldAPIUpdatePayload) => {
      const res = await FieldAPI.update(baseId, tableId, fieldId, {
        ...payload,
        isPrimary: true,
      });
      if (res.isOk()) {
        handleTableFieldUpdateEvent(baseId, tableId, res.value.data, {
          updateOtherFields: () => ({
            isPrimary: false,
          }),
        });
      }
      return res;
    },
    [handleTableFieldUpdateEvent]
  );

  return {
    createField,
    updateField,
    removeField,
    setPrimaryField,
    handleTableFieldCreateEvent,
    handleTableFieldUpdateEvent,
    handleTableFieldDeleteEvent,
  };
};

export { useTableFieldsMutation };
