import useSWR, { mutate as globalMutate } from 'swr';
import { useCallback, useMemo } from 'react';
import {
  TableAPI,
  TableAPICreatePayload,
  TableAPIGetListPayload,
  TableAPIUpdatePayload,
} from '@/services/api';
import { useTranslate } from '@tolgee/react';
import { ISuccessListPaginationResponse, Table } from '@/types';
import { notifications } from '@/components/Common';
import { ISuccessListResponse } from '@resola-ai/models';
import { getErrorTranslation } from '@/utils';
import { DEFAULT_TABLE_LIMIT } from '@/constants/api';

const AUTO_REVALIDATE_IN_MS = 1000 * 60 * 20; // 20 mins

const DEFAULT_USE_TABLE_QUERY_PARAMS: TableAPIGetListPayload = {
  page: 1,
  limit: DEFAULT_TABLE_LIMIT,
} as const;

export const createTablesKey = (baseId: string, params?: TableAPIGetListPayload) => [
  'bases/tables/',
  baseId,
  params,
];

export const isTablesKey = (key: unknown, baseId?: string) =>
  Array.isArray(key) &&
  key.length === 3 &&
  key[0] === 'bases/tables/' &&
  key[1] === baseId &&
  typeof key[2] === 'object';

const useTablesQuery = (
  baseId?: string,
  params: Partial<TableAPIGetListPayload> = {},
  options: {
    onSuccess?: (data: ISuccessListPaginationResponse<Table>) => void;
  } = {}
) => {
  const { t } = useTranslate('common');

  const mergedParams = useMemo(() => ({ ...DEFAULT_USE_TABLE_QUERY_PARAMS, ...params }), [params]);

  const { data, error, isLoading, isValidating } = useSWR(
    baseId ? createTablesKey(baseId, mergedParams) : null,
    async () => {
      const res = await TableAPI.getList(baseId!, mergedParams);

      if (res.isOk()) {
        return res.value;
      }
      if (res.isErr()) {
        throw new Error(res.error);
      }
      return undefined;
    },
    {
      onSuccess: data => {
        if (!data) return;
        options.onSuccess?.(data);
      },
      onError: (error: Error) => {
        notifications.show({
          message: getErrorTranslation(error.message, t('error.table.getList')),
        });
      },
      // keep previous data when loading and revalidate
      keepPreviousData: true,
      // config cache and revalidation
      refreshInterval: AUTO_REVALIDATE_IN_MS,
    }
  );

  return {
    tables: data?.data ?? [],
    pagination: data?.pagination ?? null,
    isLoading,
    isValidating,
    isInitialLoading: isLoading && !data?.data?.length,
    error,
  };
};

type TableQueryData = ISuccessListResponse<Table>;

const useTablesMutation = () => {
  const { t } = useTranslate('common');

  const revalidateQuery = useCallback((baseId: string) => {
    globalMutate(key => isTablesKey(key, baseId), undefined, true);
  }, []);

  // only update the cache for existing table query, no revalidation
  const updateQueryCache = useCallback(
    (baseId: string, updateFn: (data: TableQueryData) => TableQueryData) => {
      globalMutate(
        key => isTablesKey(key, baseId),
        (currentData?: TableQueryData) => {
          if (!currentData) return currentData;
          return updateFn(currentData);
        },
        false
      );
    },
    []
  );

  const createTable = useCallback(
    async (baseId: string, payload: TableAPICreatePayload) => {
      const res = await TableAPI.create(baseId, payload);
      if (res.isOk()) {
        revalidateQuery(baseId);
      }
      if (res.isErr()) {
        notifications.show({
          message: getErrorTranslation(res.error, t('error.table.create')),
        });
      }
      return res;
    },
    [revalidateQuery, t]
  );

  const duplicateTable = useCallback(
    async (baseId: string, tableId: string, payload: TableAPICreatePayload) => {
      const res = await TableAPI.create(baseId, { ...payload, duplicateFromId: tableId });
      if (res.isOk()) {
        revalidateQuery(baseId);
      }
      if (res.isErr()) {
        notifications.show({
          message: getErrorTranslation(res.error, t('error.table.duplicate')),
        });
      }
      return res;
    },
    [revalidateQuery, t]
  );

  const updateTable = useCallback(
    async (baseId: string, tableId: string, payload: TableAPIUpdatePayload) => {
      const res = await TableAPI.update(baseId, tableId, payload);

      if (res.isOk()) {
        const updatedTable = res.value.data;
        updateQueryCache(baseId, currentData => ({
          ...currentData,
          data: currentData.data.map(table =>
            table.id === tableId ? { ...table, ...updatedTable } : table
          ),
        }));
      }

      return res;
    },
    [updateQueryCache]
  );

  const removeTable = useCallback(
    async (baseId: string, tableId: string) => {
      const res = await TableAPI.remove(baseId, tableId);
      if (res.isOk()) {
        revalidateQuery(baseId);
      }
      if (res.isErr()) {
        notifications.show({ message: getErrorTranslation(res.error, t('error.table.remove')) });
      }
      return res;
    },
    [revalidateQuery, t]
  );

  return {
    revalidateQuery,
    updateQueryCache,
    createTable,
    updateTable,
    removeTable,
    duplicateTable,
  };
};

/* Query to get deleted tables list */
export const createDeletedTablesKey = (baseId: string) => ['bases/tables/deleted', baseId];

const isDeletedTablesKey = (key: unknown, baseId?: string) =>
  Array.isArray(key) && key.length === 2 && key[0] === 'bases/tables/deleted' && key[1] === baseId;

const useDeletedTablesQuery = (baseId?: string, deleted?: boolean) => {
  const { t } = useTranslate('common');

  const { data, error, isLoading, isValidating } = useSWR(
    baseId ? createDeletedTablesKey(baseId) : null,
    async () => {
      const res = await TableAPI.getDeletedList(baseId!, deleted);

      if (res.isOk()) {
        return res.value;
      }
      if (res.isErr()) {
        throw new Error(res.error);
      }
      return undefined;
    },
    {
      onError: (error: Error) => {
        notifications.show({
          message: getErrorTranslation(error.message, t('error.table.getList')),
        });
      },
      // keep previous data when loading and revalidate
      keepPreviousData: true,
      // config cache and revalidation
      refreshInterval: AUTO_REVALIDATE_IN_MS,
    }
  );

  return {
    tables: data?.data ?? [],
    pagination: data?.pagination ?? null,
    isLoading,
    isValidating,
    isInitialLoading: isLoading && !data?.data?.length,
    error,
  };
};

const useDeletedTablesMutation = () => {
  const { t } = useTranslate('common');

  const revalidateQuery = useCallback((baseId: string) => {
    globalMutate(key => isDeletedTablesKey(key, baseId), undefined, true);
  }, []);

  const updateDeletedTableQueryCache = useCallback(
    (baseId: string, updateFn: (data: TableQueryData) => TableQueryData) => {
      globalMutate(
        key => isDeletedTablesKey(key, baseId),
        (currentData?: TableQueryData) => {
          if (!currentData) return currentData;
          return updateFn(currentData);
        },
        false
      );
    },
    []
  );

  const restoreTable = useCallback(
    async (baseId: string, tableId: string) => {
      const res = await TableAPI.restore(tableId);
      if (res.isOk()) {
        updateDeletedTableQueryCache(baseId, currentData => ({
          ...currentData,
          data: currentData.data.filter(table => table.id !== tableId),
        }));
      }
      if (res.isErr()) {
        notifications.show({ message: getErrorTranslation(res.error, t('error.table.remove')) });
      }
      return res;
    },
    [updateDeletedTableQueryCache, t]
  );

  return {
    revalidateQuery,
    updateDeletedTableQueryCache,
    restoreTable,
  };
};

export { useTablesQuery, useTablesMutation, useDeletedTablesQuery, useDeletedTablesMutation };
