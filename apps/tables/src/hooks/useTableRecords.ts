import useSWR, { mutate as globalMutate } from 'swr';
import { useCallback, useState, useRef } from 'react';
import {
  RecordAPI,
  TableRecordAPICreatePayload,
  TableRecordAPIUpdatePayload,
} from '@/services/api';
import { useTranslate } from '@tolgee/react';
import { TableRecord, TableRecordStream } from '@/types';
import { notifications } from '@/components/Common';
import { ISuccessListNextPreviousResponse } from '@resola-ai/models';

export const createTableRecordsKey = (baseId: string, tableId: string) => [
  'bases/tables/records',
  baseId,
  tableId,
];

export const isTableRecordsKey = (key: unknown, baseId?: string, tableId?: string) =>
  Array.isArray(key) &&
  key.length === 3 &&
  key[0] === 'bases/tables/records' &&
  key[1] === baseId &&
  key[2] === tableId;

type TableRecordQueryData = ISuccessListNextPreviousResponse<TableRecord>;

type UseTableRecordsStreamOptions = {
  enabled?: boolean;
  onSuccess?: (data: TableRecordStream) => void;
};

const useTableRecordsStream = (
  baseId?: string,
  tableId?: string,
  options?: UseTableRecordsStreamOptions
) => {
  const { enabled = false, onSuccess = () => {} } = options || {};
  const abortRef = useRef<() => void>(() => {});
  const { data, error, isLoading, isValidating, mutate } = useSWR(
    baseId && tableId && enabled ? createTableRecordsKey(baseId, tableId) : null,
    async () => {
      const stream = RecordAPI.stream(baseId!, tableId!);
      abortRef.current = stream.abort;
      stream.onComplete(data => {
        onSuccess?.(data);
      });
      stream.onError(error => {
        throw new Error(error);
      });
    },
    {
      onError: error => {
        notifications.show({ message: error.message });
      },
    }
  );

  return {
    data,
    error,
    isLoading,
    isValidating,
    mutate,
    abort: abortRef.current,
  };
};

const useTableRecordsMutation = () => {
  const { t } = useTranslate('common');
  const [isMutating, setIsMutating] = useState(false);

  const revalidateQuery = useCallback((baseId: string, tableId: string) => {
    globalMutate(key => isTableRecordsKey(key, baseId, tableId), undefined, true);
  }, []);

  // only update the cache for existing records query, no revalidation
  const updateQueryCache = useCallback(
    (
      baseId: string,
      tableId: string,
      updateFn: (data: TableRecordQueryData) => TableRecordQueryData
    ) => {
      globalMutate(
        key => isTableRecordsKey(key, baseId, tableId),
        (currentData?: TableRecordQueryData) => {
          if (!currentData) return currentData;
          return updateFn(currentData);
        },
        false
      );
    },
    []
  );

  const createRecords = useCallback(
    async (baseId: string, tableId: string, payload: TableRecordAPICreatePayload) => {
      setIsMutating(true);
      const res = await RecordAPI.create(baseId, tableId, payload);
      if (res.isOk()) {
        const updatedRecord = res.value.data;
        updateQueryCache(baseId, tableId, currentData => ({
          ...currentData,
          data: [...currentData.data, updatedRecord],
        }));
      }
      if (res.isErr()) {
        notifications.show({ message: t(res.error || 'error.record.create') });
      }
      setIsMutating(false);
      return res;
    },
    [t, updateQueryCache]
  );

  const duplicateRecord = useCallback(
    async (
      baseId: string,
      tableId: string,
      recordId: string,
      payload: Pick<TableRecord, 'sortId' | 'records'>
    ) => {
      setIsMutating(true);
      const res = await RecordAPI.create(baseId, tableId, {
        ...payload,
        duplicateFromId: recordId,
      });
      if (res.isOk()) {
        const updatedRecord = res.value.data;
        updateQueryCache(baseId, tableId, currentData => ({
          ...currentData,
          data: [...currentData.data, updatedRecord],
        }));
      }
      if (res.isErr()) {
        notifications.show({ message: t(res.error || 'error.record.duplicate') });
      }
      setIsMutating(false);
      return res;
    },
    [t, updateQueryCache]
  );

  const updateRecord = useCallback(
    async (
      baseId: string,
      tableId: string,
      recordId: string,
      payload: TableRecordAPIUpdatePayload
    ) => {
      setIsMutating(true);
      const res = await RecordAPI.update(baseId, tableId, recordId, payload);
      if (res.isOk()) {
        const updatedRecord = res.value.data;
        updateQueryCache(baseId, tableId, currentData => ({
          ...currentData,
          data: currentData.data.map(record =>
            record.id === recordId ? { ...record, ...updatedRecord } : record
          ),
        }));
      }
      if (res.isErr()) {
        notifications.show({ message: t(res.error || 'error.record.update') });
      }
      setIsMutating(false);
      return res;
    },
    [t, updateQueryCache]
  );

  const removeRecord = useCallback(
    async (baseId: string, tableId: string, recordId: string) => {
      setIsMutating(true);
      const res = await RecordAPI.remove(baseId, tableId, recordId);
      if (res.isOk()) {
        updateQueryCache(baseId, tableId, currentData => ({
          ...currentData,
          data: currentData.data.filter(record => record.id !== recordId),
        }));
      }
      if (res.isErr()) {
        notifications.show({ message: t(res.error || 'error.record.remove') });
      }
      setIsMutating(false);
      return res;
    },
    [t, updateQueryCache]
  );

  const removeRecords = useCallback(
    async (baseId: string, tableId: string, recordIds: string[]) => {
      setIsMutating(true);
      const res = await RecordAPI.removeMany(baseId, tableId, recordIds);
      if (res.isOk()) {
        updateQueryCache(baseId, tableId, currentData => ({
          ...currentData,
          data: currentData.data.filter(record => !recordIds.includes(record.id)),
        }));
      }
      setIsMutating(false);
      return res;
    },
    [t, updateQueryCache]
  );

  const clearRecords = useCallback(
    async (baseId: string, tableId: string) => {
      setIsMutating(true);
      const res = await RecordAPI.clearData(baseId, tableId);
      if (res.isOk()) {
        revalidateQuery(baseId, tableId);
      }
      if (res.isErr()) {
        notifications.show({ message: t(res.error || 'error.record.clearData') });
      }
      setIsMutating(false);
      return res;
    },
    [t, revalidateQuery]
  );

  return {
    isMutating,
    revalidateQuery,
    updateQueryCache,
    createRecords,
    updateRecord,
    removeRecord,
    duplicateRecord,
    clearRecords,
    removeRecords,
  };
};

export { useTableRecordsStream, useTableRecordsMutation };
