import useSWR, { mutate as globalMutate } from 'swr';
import { useCallback } from 'react';
import { ViewAPI } from '@/services/api';
import { useTranslate } from '@tolgee/react';
import { ISuccessListPaginationResponse, View } from '@/types';
import { notifications } from '@/components/Common';
import { Err } from 'ts-results-es';

const AUTO_REVALIDATE_IN_MS = 1000 * 60 * 20; // 20 mins

export const createViewsKey = (baseId: string, tableId: string) => [
  'bases/tables/views',
  baseId,
  tableId,
];

export const isViewsKey = (key: unknown, baseId?: string, tableId?: string) =>
  Array.isArray(key) &&
  key.length === 3 &&
  key[0] === 'bases/tables/views' &&
  key[1] === baseId &&
  key[2] === tableId;

type ViewQueryData = ISuccessListPaginationResponse<View>;

type UseTableViewsQueryOptions = {
  onSuccess?: (data: ISuccessListPaginationResponse<View>) => void;
};

const useTableViewsQuery = (
  baseId?: string,
  tableId?: string,
  options?: UseTableViewsQueryOptions
) => {
  const { t } = useTranslate('common');

  const { data, error, isLoading, isValidating } = useSWR(
    baseId && tableId ? createViewsKey(baseId, tableId) : null,
    async () => {
      const res = await ViewAPI.getList(baseId!, tableId!);
      if (res.isOk()) {
        return res.value;
      }
      if (res.isErr()) {
        throw new Error(res.error ? String(res.error) : 'error.view.getList');
      }
      return undefined;
    },
    {
      onError: (error: Error) => {
        notifications.show({ message: t(error.message) });
      },
      refreshInterval: AUTO_REVALIDATE_IN_MS,
      onSuccess: data => {
        if (!data) return data;
        options?.onSuccess?.(data);
      },
    }
  );

  return {
    views: data?.data ?? [],
    isLoading,
    isValidating,
    isInitialLoading: isLoading && isValidating,
    error,
  };
};

const useTableViewsMutation = () => {
  const updateQueryCache = useCallback(
    (baseId: string, tableId: string, updateFn: (data: ViewQueryData) => ViewQueryData) => {
      globalMutate(
        key => isViewsKey(key, baseId, tableId),
        (currentData?: ViewQueryData) => {
          if (!currentData) return currentData;
          return updateFn(currentData);
        },
        false
      );
    },
    []
  );

  const createView = useCallback(
    async (baseId: string, tableId: string, payload: Partial<View>) => {
      const res = await ViewAPI.create(baseId, tableId, payload);
      if (res.isOk()) {
        const updatedView = res.value.data;
        updateQueryCache(baseId, tableId, currentData => ({
          ...currentData,
          data: [updatedView, ...currentData.data],
        }));
      }
      return res;
    },
    [updateQueryCache]
  );

  const updateView = useCallback(
    async (
      baseId: string,
      tableId: string,
      viewId: string,
      payload: Partial<View>,
      options: {
        optimisticUpdate?: boolean;
      } = {}
    ) => {
      const { optimisticUpdate = false } = options;

      const updateView = (
        previousData: ViewQueryData,
        updatedView: Partial<View>
      ): ViewQueryData => {
        return {
          ...previousData,
          data: previousData.data.map(view =>
            view.id === viewId
              ? {
                  ...view,
                  ...updatedView,
                  filter: 'filter' in updatedView ? updatedView.filter : {},
                }
              : view
          ),
        };
      };

      try {
        let res: Awaited<ReturnType<typeof ViewAPI.update>> = Err(undefined);

        await globalMutate(
          key => isViewsKey(key, baseId, tableId),
          async (currentData?: ViewQueryData) => {
            if (!currentData) return currentData;
            res = await ViewAPI.update(baseId, tableId, viewId, payload);

            if (res.isOk()) {
              const updatedView = res.value.data;
              return updateView(currentData, updatedView);
            }

            // for useSWR handle rollback
            if (res.isErr()) {
              throw new Error(res.error ? String(res.error) : 'error.view.update');
            }
          },
          {
            revalidate: false,
            rollbackOnError: true,
            optimisticData: currentData => {
              if (!optimisticUpdate || !currentData) return currentData;
              return updateView(currentData, payload);
            },
          }
        );
        return res;
      } catch (error) {
        console.error('Failed to update view:', error);
        return Err(error);
      }
    },
    [updateQueryCache]
  );

  const removeView = useCallback(
    async (baseId: string, tableId: string, viewId: string) => {
      const res = await ViewAPI.remove(baseId, tableId, viewId);
      if (res.isOk()) {
        updateQueryCache(baseId, tableId, currentData => ({
          ...currentData,
          data: currentData.data.filter(view => view.id !== viewId),
        }));
      }
      return res;
    },
    [updateQueryCache]
  );

  return {
    createView,
    updateView,
    removeView,
  };
};

export { useTableViewsQuery, useTableViewsMutation };
