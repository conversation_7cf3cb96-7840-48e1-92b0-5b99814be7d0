import { useMemo, useState } from 'react';
import { rem, Flex, LoadingOverlay } from '@mantine/core';
import { createStyles } from '@mantine/emotion';
import { useParams } from 'react-router-dom';
import { useDeletedTablesQuery } from '@/hooks/useTables';
import SearchControl from './Search';
import TableItem from './TableItem';
import lunr from 'lunr';

const useStyles = createStyles(theme => ({
  bodyContent: {
    borderTop: `1px solid ${theme.colors.decaLight[2]}`,
    padding: `${rem(16)} ${rem(24)}`,
    gap: rem(16),
  },
  deleteTableRoot: {
    display: 'flex',
    flexDirection: 'column',
    gap: rem(16),
    maxHeight: rem(700),
    overflow: 'auto',
    scrollbarWidth: 'thin',
    padding: rem(16),
  },
}));

const MainContainer = () => {
  const [searchText, setSearchText] = useState<string>('');
  const { classes } = useStyles();
  const { baseId } = useParams();
  const { tables, isInitialLoading } = useDeletedTablesQuery(baseId, true);

  // Create lunr index
  const idx = useMemo(() => {
    return lunr(function (this: lunr.Builder) {
      this.field('name');
      this.ref('id');

      tables.forEach(table => {
        this.add({
          id: table.id,
          name: table.name,
        });
      });
    });
  }, [tables]);

  const filteredTables = useMemo(() => {
    if (!searchText) return tables;

    const results = idx.search(searchText);
    return results.map(result => tables.find(table => table.id === result.ref)).filter(Boolean);
  }, [tables, searchText, idx]);

  return (
    <>
      <Flex direction='column' className={classes.bodyContent}>
        <LoadingOverlay visible={isInitialLoading} />
        <SearchControl searchText={searchText} setSearchText={setSearchText} />
        <div className={classes.deleteTableRoot}>
          {filteredTables.map(table => (
            <TableItem
              key={table?.id}
              deletedAt={table?.deletedAt}
              name={table?.name}
              id={table?.id}
              baseId={table?.baseId}
            />
          ))}
        </div>
      </Flex>
    </>
  );
};

export default MainContainer;
