import { Container, Group, rem, <PERSON>roll<PERSON><PERSON>, SimpleGrid, <PERSON>ack, Tabs, Text } from '@mantine/core';
import { <PERSON><PERSON><PERSON>ider, SubmitHandler, useForm } from 'react-hook-form';
import { useTranslate } from '@tolgee/react';
import { DecaButton } from '@resola-ai/ui';
import { FieldMappingControl } from '../FieldMappingControl';
import { PreviewTable } from '../PreviewTable';
import { zodResolver } from '@hookform/resolvers/zod';
import { useMemo, useState } from 'react';
import {
  FieldMappingAction,
  MODAL_FOOTER_HEIGHT,
  MODAL_HEADER_HEIGHT,
  MODAL_HEIGHT,
  SCROLL_AREA_MAX_HEIGHT,
  MODAL_TABS_HEADER_HEIGHT,
} from '../constants';
import { CommonAPI, DataAPI } from '@/services/api';
import { Field, FieldType, ImportTableDataDto } from '@/types';
import { FIELD_OPTIONS_DEFAULT_VALUES } from '@/constants/table';
import partition from 'lodash/partition';
import { useTablesMutation, useTablesQuery } from '@/hooks';
import { FieldMappingFormData } from './schema';
import { fieldMappingFormSchema } from './schema';
import { useTableImportContext } from '../TableImportContext';
import { useNavigate, useParams } from 'react-router-dom';
import { notifications } from '@/components/Common';
import { getTablePath } from '@/utils';

const TAB_HEIGHT = `min(calc(${MODAL_HEIGHT} - ${MODAL_HEADER_HEIGHT} - ${MODAL_FOOTER_HEIGHT} - ${MODAL_TABS_HEADER_HEIGHT}), ${SCROLL_AREA_MAX_HEIGHT})`;

enum FieldMappingScreenTab {
  FIELD_MAPPING = 'fieldMapping',
  PREVIEW = 'preview',
}

export type TableColumn = Record<string, any>;

export type ImportData = {
  isCreate: boolean;
  table: {
    id?: string;
    name?: string;
  };
  uploadFile: {
    file: File;
    columns: string[];
    rows: Record<string, any>[];
  };
  options: {
    useFirstRowAsHeader: boolean;
    createNewFieldsFromMissingOptions: boolean;
  };
};

const extractFieldTypes = (
  fields: Record<string, any>
): { name: string; id?: string; type?: FieldType }[] => {
  return Object.entries(fields)
    .map(([fieldName, fieldData]) => {
      const fieldId = fieldData?.id !== FieldMappingAction.CREATE ? fieldData?.id : undefined;
      const fieldType = fieldData?.type;
      return { id: fieldId, name: fieldName, type: fieldType };
    })
    .filter(field => ![field.id, field.type].includes(FieldMappingAction.EXCLUDE));
};

const mergeTableFieldsById = (
  current: Field[],
  existing: Field[],
  newlyCreated: any[]
): Field[] => {
  const fieldMap = new Map<string, Field>();
  current.forEach(field => fieldMap.set(field.id, field));
  existing.forEach(field => fieldMap.set(field.id, field));
  return Array.from(fieldMap.values()).concat(newlyCreated);
};

const getFieldsToImport = (
  allFields: Field[],
  currentFields: Field[],
  existingFields: any[]
): Field[] => {
  const fieldMap = new Map<string, Field>();
  allFields.forEach(field => fieldMap.set(field.id, field));
  currentFields.forEach(field => fieldMap.delete(field.id));
  existingFields.forEach(field => fieldMap.set(field.id, field));
  return Array.from(fieldMap.values());
};

const FieldMappingScreen = () => {
  const { t } = useTranslate('common');
  const { baseId } = useParams();
  const { openLoadingOverlay, closeLoadingOverlay, onClose, importData } = useTableImportContext();
  const [tableColumns, setTableColumns] = useState<TableColumn[]>([]);
  const methods = useForm<FieldMappingFormData>({
    mode: 'onChange',
    resolver: zodResolver(fieldMappingFormSchema),
  });
  const {
    watch,
    handleSubmit,
    formState: { isSubmitting },
  } = methods;

  const isCreateNewTable = importData?.isCreate;
  const tableId = importData?.table?.id;
  const excelDataColumns = importData?.uploadFile?.columns || [];
  const excelDataRows = importData?.uploadFile?.rows || [];
  const uploadedFile = importData?.uploadFile.file;
  const { tables } = useTablesQuery(baseId);
  const { createTable, updateTable } = useTablesMutation();
  const navigate = useNavigate();

  const tableFields = useMemo(() => {
    if (!tableId) return [];

    return tables
      .find(table => table.id === tableId)
      ?.fields.map(field => ({ id: field.id, name: field.name, type: field.type }));
  }, [tableId, tables]);

  const sortedExcelColumns = useMemo(() => {
    if (!tableFields?.length || !excelDataColumns?.length) return excelDataColumns;

    return [...excelDataColumns].sort((a, b) => {
      const aFieldMatch = tableFields.some(field => field.name.toLowerCase() === a.toLowerCase());
      const bFieldMatch = tableFields.some(field => field.name.toLowerCase() === b.toLowerCase());

      if (aFieldMatch && !bFieldMatch) return -1;
      if (!aFieldMatch && bFieldMatch) return 1;
      return 0;
    });
  }, [excelDataColumns, tableFields]);

  const updateTableOnTabChange = (tab: string | null) => {
    if (tab === FieldMappingScreenTab.FIELD_MAPPING) return;

    const fields = watch('fields', {});

    const updatedColumns = extractFieldTypes(fields).map(({ name, id, type }) => {
      const colType = tableFields.find(item => item.id === id)?.type || type;
      return {
        [name]: colType,
      };
    });

    setTableColumns(updatedColumns);
  };

  const onSubmit: SubmitHandler<FieldMappingFormData> = async data => {
    if (!baseId || !uploadedFile) return;

    const payload: ImportTableDataDto = { fields: {}, filePath: '', fileType: '' };
    openLoadingOverlay();

    const mapFieldsToPayload = (fields: any[]) => {
      fields.forEach(field => {
        payload['fields'][field.id] = field.name;
      });
    };

    try {
      const fields = extractFieldTypes(data.fields);
      const [existingFields, newFields] = partition(fields, 'id');

      const newFieldsPayload = newFields.map(field => ({
        name: field.name,
        type: field.type!,
        options: FIELD_OPTIONS_DEFAULT_VALUES[field.type!],
      }));

      let tableId: string;
      let tableFields;

      if (isCreateNewTable) {
        const newTable = await createTable(baseId, {
          name: importData.table.name || '',
          fields: newFieldsPayload,
        });
        if (newTable.isOk()) {
          tableId = newTable.value.data.id;
          tableFields = newTable.value.data.fields;
          mapFieldsToPayload(tableFields);
        } else {
          return;
        }
      } else {
        tableId = importData?.table?.id as string;
        const currentTable = tables.find(table => table.id === importData?.table?.id);
        const currentTableFields = currentTable?.fields || [];

        const existingFieldsPayload = existingFields
          .map(field => {
            const table = tables.find(table => table.fields.some(f => f.id === field.id));
            return table?.fields.find(f => f.id === field.id);
          })
          .filter(Boolean);

        const newTableFields = mergeTableFieldsById(
          currentTableFields,
          existingFieldsPayload,
          newFieldsPayload
        );

        const newTable = await updateTable(baseId, tableId, {
          ...importData?.table,
          fields: newTableFields,
        });
        if (newTable.isOk()) {
          const allFields = newTable.value.data.fields;
          const fieldsToImport = getFieldsToImport(allFields, currentTableFields, existingFields);

          mapFieldsToPayload(fieldsToImport);
        } else {
          return;
        }
      }
      // navigate to the imported table
      navigate(getTablePath(baseId, tableId), { replace: true });

      const presignUrlRes = await CommonAPI.presignUrl({
        baseId: baseId,
        tableId: tableId,
        mimeType: importData.uploadFile.file.type,
        name: importData.uploadFile.file.name,
        size: importData.uploadFile.file.size,
        source: 'imports',
        feature: 'attachments',
      });

      if (presignUrlRes.isOk()) {
        const presignUrl = presignUrlRes.value;
        payload['filePath'] = presignUrl.file.path;
        payload['fileType'] = 'csv';
        await CommonAPI.uploadFile(presignUrl.uploadUrl, uploadedFile);
        const res = await DataAPI.importTableData(baseId, tableId, payload);

        if (res.isOk()) {
          onClose?.();
        }
      }
    } catch (error) {
      console.error(error);
      notifications.show({
        message: t('csvImport.notification.importError'),
        status: 'error',
      });
      closeLoadingOverlay();
    }
  };

  return (
    <FormProvider {...methods}>
      <Stack gap={0}>
        <Tabs
          keepMounted={false}
          onChange={updateTableOnTabChange}
          defaultValue={FieldMappingScreenTab.FIELD_MAPPING}
          styles={theme => ({
            tab: {
              height: MODAL_TABS_HEADER_HEIGHT,
              fontSize: rem(14),
              fontWeight: 500,
              color: theme.colors.decaDark[4],
              '&[data-active]': {
                color: theme.colors.decaNavy[4],
              },
            },
          })}>
          <Tabs.List>
            <Tabs.Tab value={FieldMappingScreenTab.FIELD_MAPPING}>
              {t('csvImport.fieldMapping.tabFieldMapping')}
            </Tabs.Tab>
            <Tabs.Tab value={FieldMappingScreenTab.PREVIEW}>
              {t('csvImport.fieldMapping.tabPreview')}
            </Tabs.Tab>
          </Tabs.List>

          <Tabs.Panel value={FieldMappingScreenTab.FIELD_MAPPING}>
            <ScrollArea py='xl' h={TAB_HEIGHT} type='hover'>
              <Container size={rem(960)} p={rem(12)}>
                <Stack gap='lg'>
                  <SimpleGrid cols={2} spacing={rem(40)}>
                    <Stack gap='xs'>
                      <Text c='decaGrey.9' fw='500'>
                        {t('csvImport.fieldMapping.fileTitle')}
                      </Text>
                      <Text c='decaGrey.4' fz='sm'>
                        {t('csvImport.fieldMapping.tableSubtitleFieldCount', {
                          count: sortedExcelColumns.length,
                        })}
                        <br />
                        {t('csvImport.fieldMapping.tableSubtitlePrimaryField')}
                      </Text>
                    </Stack>

                    <Stack gap='xs'>
                      <Text c='decaGrey.9' fw='500'>
                        {t('csvImport.fieldMapping.tableTitle')}
                      </Text>
                      <Text c='decaGrey.4' fz='sm'>
                        {t(
                          isCreateNewTable
                            ? 'csvImport.fieldMapping.tableSubtitleCreateNew'
                            : 'csvImport.fieldMapping.tableSubtitleExisting'
                        )}
                      </Text>
                    </Stack>
                  </SimpleGrid>

                  {sortedExcelColumns.map((column, index) => {
                    if (isCreateNewTable) {
                      return (
                        <FieldMappingControl
                          fields={tableFields}
                          key={index}
                          name={column}
                          type='typeMapping'
                        />
                      );
                    }

                    return (
                      <FieldMappingControl
                        key={index}
                        name={column}
                        fields={tableFields}
                        defaultCreateState={index !== 0}
                        type='tableMapping'
                      />
                    );
                  })}
                </Stack>
              </Container>
            </ScrollArea>
          </Tabs.Panel>
          <Tabs.Panel value={FieldMappingScreenTab.PREVIEW} h={TAB_HEIGHT}>
            <PreviewTable columns={tableColumns} data={excelDataRows} />
          </Tabs.Panel>
        </Tabs>
        <Group
          justify='flex-end'
          gap='md'
          p='md'
          pos='sticky'
          bg='#fff'
          bottom={0}
          h={MODAL_FOOTER_HEIGHT}
          sx={theme => ({
            borderTop: `1px solid ${theme.colors.decaLight[2]}`,
            zIndex: 100,
          })}>
          <Text truncate>
            {t('csvImport.fieldMapping.importingRecords', {
              count: importData?.uploadFile?.rows?.length || 0,
              file: uploadedFile?.name,
              table: importData?.table?.name,
            })}
          </Text>
          <DecaButton onClick={onClose} variant='neutral' ml='auto'>
            {t('csvImport.cancelBtn')}
          </DecaButton>
          <DecaButton loading={isSubmitting} onClick={handleSubmit(onSubmit)}>
            {t('csvImport.importBtn')}
          </DecaButton>
        </Group>
      </Stack>
    </FormProvider>
  );
};

export default FieldMappingScreen;
