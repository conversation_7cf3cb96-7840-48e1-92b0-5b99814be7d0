import { useAppContext } from '@/contexts/AppContext';
import { Divider, ScrollArea, Skeleton, Stack, rem, useMantineTheme } from '@mantine/core';
import {
  IconChevronDown,
  IconChevronRight,
  IconChevronsRight,
  IconFileTypeCsv,
  IconTable,
} from '@tabler/icons-react';
import { MouseEvent, useCallback, useEffect, useMemo, useState } from 'react';
import { useTranslate } from '@tolgee/react';
import { NavigationItem, NavigationItemTitle } from './NavigationItem';
import NavigationMenuIcon, {
  MenuActionCallback,
  NavigationMenuPosition,
} from './NavigationMenuIcon';
import { IconChevronsLeft } from '@tabler/icons-react';
import { useNavigate, useParams } from 'react-router-dom';
import { useDisclosure } from '@mantine/hooks';
import NavbarWrapper from './NavbarWrapper';
import TableImportModal from '@/components/TableImport';
import { useTablesMutation, useTablesQuery } from '@/hooks';
import {
  CreateFormSubmitCallback,
  CreateModal,
  notifications,
  Pagination,
} from '@/components/Common';
import { Table } from '@/types';
import { getTablePath, getValidPage } from '@/utils';

const NUMBER_OF_SKELETON_ITEMS = 8;

interface TableNavigationItemProps {
  table: Table;
  index: number;
  handleMenuChange?: MenuActionCallback;
}

const TableNavigationItem = (props: TableNavigationItemProps) => {
  const { table, index, handleMenuChange } = props;
  const [menuOpened, { open: openMenu, close: closeMenu }] = useDisclosure(false);
  const [position, setPosition] = useState<NavigationMenuPosition | undefined>(undefined);

  const path = useMemo(() => {
    return getTablePath(table.baseId, table.id);
  }, [table.baseId, table.id]);

  const handleContextMenu = useCallback(
    (e: MouseEvent<HTMLDivElement>) => {
      e.preventDefault();
      setPosition({ x: e.clientX, y: e.clientY });
      openMenu();
    },
    [openMenu]
  );

  return (
    <NavigationItem
      key={table.id}
      as='link'
      path={path}
      text={table.name}
      minimizeIcon={index + 1}
      onContextMenu={handleContextMenu}
      rightIcon={
        <NavigationMenuIcon
          table={table}
          onChange={handleMenuChange}
          opened={menuOpened}
          onOpen={openMenu}
          onClose={closeMenu}
          position={position}
        />
      }
    />
  );
};

const BaseNavigationControls = () => {
  const { t } = useTranslate('common');
  const {
    showCreateNewItems,
    showSidebar,
    toggleCreateNewItems,
    toggleSidebar,
    tablePage,
    setTablePage,
  } = useAppContext();
  const theme = useMantineTheme();

  const { baseId, tableId } = useParams();
  const navigate = useNavigate();

  const { tables, pagination, isLoading, isValidating, isInitialLoading } = useTablesQuery(
    baseId,
    {
      page: tablePage,
    },
    {
      onSuccess: data => {
        const total = data?.pagination.total ?? 0;
        const limit = data?.pagination.limit ?? 0;
        const validPage = getValidPage(tablePage, total, limit);
        setTablePage(validPage);
      },
    }
  );
  const { createTable } = useTablesMutation();

  const [createTableModalOpened, { open: openCreateTableModal, close: closeCreateTableModal }] =
    useDisclosure();
  const [tableImportModalOpened, { open: openTableImportModal, close: closeTableImportModal }] =
    useDisclosure();

  const isShowPagination = pagination && pagination.totalPage > 1;

  const shouldNavigateToFirstTable = useMemo(() => {
    if (isLoading || isValidating) return false;

    const hasLoadedTables = tables?.length > 0;
    const hasValidTableSelected = tableId && tables?.some(table => table.id === tableId);

    return hasLoadedTables && !hasValidTableSelected;
  }, [isLoading, isValidating, tables, tableId]);

  const navigateToFirstTable = useCallback(() => {
    if (!shouldNavigateToFirstTable) return;
    const firstTablePath = getTablePath(tables[0].baseId, tables[0].id);
    navigate(firstTablePath, { replace: true });
  }, [shouldNavigateToFirstTable, navigate, tables]);

  const handleCreateTable: CreateFormSubmitCallback = useCallback(
    async ({ formData, closeModal }) => {
      if (!baseId) return;

      const res = await createTable(baseId, { name: formData.name, fields: [] });

      if (res.isOk()) {
        const table = res.value.data;
        notifications.show({
          message: t('success.table.create', { tableName: table.name }),
          status: 'success',
        });
        if (!table.id || !table.baseId) return;
        closeModal();
      }
    },
    [baseId, createTable]
  );

  const renderTableItems = useMemo(() => {
    if (isInitialLoading) {
      return Array.from({ length: NUMBER_OF_SKELETON_ITEMS }).map((_, index) => (
        <Skeleton key={index} h={40} w='100%' radius='md' />
      ));
    }

    return tables?.map((table, index) => (
      <TableNavigationItem key={table.id} table={table} index={index} />
    ));
  }, [isInitialLoading, tables]);

  useEffect(() => {
    navigateToFirstTable();
  }, [navigateToFirstTable]);

  useEffect(() => {
    setTablePage(1);
  }, [baseId]);

  return (
    <NavbarWrapper>
      <NavigationItemTitle
        text={t('navigation.title.table')}
        rightIcon={showSidebar ? <IconChevronsLeft /> : <IconChevronsRight />}
        onIconClick={toggleSidebar}
      />

      <ScrollArea
        scrollHideDelay={100}
        offsetScrollbars={false}
        mt={rem(6)}
        mb={rem(16)}
        px={rem(8)}
        mx={rem(-8)}
        sx={{ flex: 1 }}
        scrollbarSize={4}
        styles={{
          viewport: {
            '&>div': {
              display: 'block !important',
            },
          },
        }}>
        <Stack gap={rem(6)}>{renderTableItems}</Stack>
      </ScrollArea>

      {isShowPagination ? (
        <Pagination
          isExpanded={showSidebar}
          disabled={isLoading}
          totalPages={pagination.totalPage}
          currentPage={tablePage}
          onPageChange={setTablePage}
        />
      ) : null}

      <Divider w='100%' my={rem(20)} />

      <NavigationItemTitle
        text={t('navigation.title.createNew')}
        rightIcon={
          showCreateNewItems ? (
            <IconChevronDown strokeWidth={3} />
          ) : (
            <IconChevronRight strokeWidth={3} />
          )
        }
        onIconClick={toggleCreateNewItems}
      />

      <Stack gap={rem(6)} mt={rem(6)}>
        {showCreateNewItems ? (
          <>
            <NavigationItem
              as='button'
              onClick={openCreateTableModal}
              text={t('navigation.createNew.table')}
              leftIcon={<IconTable color={theme.colors.blue[4]} />}
            />
            <NavigationItem
              as='button'
              onClick={openTableImportModal}
              text={t('navigation.createNew.importCSV')}
              leftIcon={<IconFileTypeCsv color={theme.colors.decaGreen[4]} />}
            />
          </>
        ) : null}
      </Stack>

      <CreateModal
        opened={createTableModalOpened}
        onCancel={closeCreateTableModal}
        onSubmit={handleCreateTable}
        inputLabel={t('modal.createTableLabel')}
        submitText={t('modal.createBtn')}
        cancelText={t('modal.cancelBtn')}
      />

      <TableImportModal opened={tableImportModalOpened} onClose={closeTableImportModal} />
    </NavbarWrapper>
  );
};

export default BaseNavigationControls;
