import { ActionIcon, Menu, rem } from '@mantine/core';
import { IconCopy, IconDotsVertical, IconEdit, IconTrash, IconX } from '@tabler/icons-react';
import { createStyles } from '@mantine/emotion';
import { ReactNode, useCallback, useState } from 'react';
import { useTranslate } from '@tolgee/react';
import { Table } from '@/types';
import { useDeletedTablesMutation, useTablesMutation, useTableRecordsMutation } from '@/hooks';
import {
  DuplicateModal,
  DuplicateFormSubmitCallback,
  ConfirmForm,
  ConfirmFormSubmitCallback,
  RenameForm,
  RenameFormSubmitCallback,
  notifications,
} from '@/components/Common';
import { useAppContext } from '@/contexts/AppContext';

const useStyles = createStyles(theme => ({
  menuDropdown: {
    zIndex: '150 !important' as any,
    borderColor: theme.colors.decaLight[4],
    padding: rem(4),
  },
  menuItem: {
    '--menu-item-label-color': theme.colors.decaGrey[9],
    '--menu-item-icon-color': theme.colors.decaGrey[6],
    '--menu-item-hover-color': theme.colors.decaLight[1],
    minWidth: rem(152),
    maxWidth: rem(180),
    padding: `${rem(8)} ${rem(12)}`,
    '&:hover': {
      backgroundColor: 'var(--menu-item-hover-color)',
    },
    '&.red': {
      '--menu-item-label-color': theme.colors.decaRed[5],
      '--menu-item-icon-color': theme.colors.decaRed[5],
      '--menu-item-hover-color': theme.colors.decaRed[0],
    },
  },
  itemLabel: {
    fontSize: theme.fontSizes.md,
    lineHeight: 1.55,
    fontWeight: 400,
    color: 'var(--menu-item-label-color)',
  },
  itemIcon: {
    width: rem(16),
    height: rem(16),
    marginRight: rem(12),
    color: 'var(--menu-item-icon-color)',
  },
  menuDivider: {
    margin: 0,
  },
  menuArrow: {
    borderColor: theme.colors.decaLight[4],
  },
  goToTrashLabel: {
    paddingLeft: rem(8),
    cursor: 'pointer',
    color: theme.colors.decaBlue[3],
    fontWeight: 500,
    fontSize: rem(14),
    '&:hover': {
      color: theme.colors.decaBlue[5],
    },
  },
}));

const NAVIGATION_MENU_AXIS_OFFSET = {
  alignmentAxis: -8,
  mainAxis: 16,
};

export enum MenuAction {
  RENAME_TABLE = 'renameTable',
  CLEAR_DATA = 'clearData',
  DELETE_TABLE = 'deleteTable',
  DUPLICATE_TABLE = 'duplicateTable',
  NO_ACTION = 'noAction',
}

export type MenuActionCallback = (action: MenuAction, table: Table) => void;

export type NavigationMenuPosition = { x: number; y: number };
export interface NavigationMenuIconProps {
  icon?: ReactNode;
  table: Table;
  onChange?: MenuActionCallback;
  opened?: boolean;
  onOpen?: () => void;
  onClose?: () => void;
  position?: NavigationMenuPosition;
  onPositionChange?: (position?: NavigationMenuPosition) => void;
}

const NavigationMenuIcon = (props: NavigationMenuIconProps) => {
  const {
    icon = <IconDotsVertical />,
    table,
    onChange,
    opened: menuOpened = false,
    onOpen: openMenu = () => {},
    onClose: closeMenu = () => {},
    position,
    onPositionChange = () => {},
  } = props;
  const { t } = useTranslate('common');
  const { classes } = useStyles();
  const { openTableTrashViewModal, addPendingClearTableId } = useAppContext();

  const [action, setAction] = useState<MenuAction>(MenuAction.NO_ACTION);
  const showArrow = action === MenuAction.RENAME_TABLE;

  const { duplicateTable, removeTable, updateTable } = useTablesMutation();
  const { clearRecords } = useTableRecordsMutation();
  const { revalidateQuery: revalidateDeletedTable } = useDeletedTablesMutation();

  const resetAction = useCallback(() => {
    setAction(MenuAction.NO_ACTION);
  }, []);

  const handleMenuExited = useCallback(() => {
    onPositionChange(undefined);
    resetAction();
  }, [onPositionChange, resetAction]);

  const handleMenuChange = useCallback(() => {
    // check for modal render outside of menu
    const shouldKeepOpen = action === MenuAction.DUPLICATE_TABLE;
    if (!shouldKeepOpen) {
      closeMenu();
    }
  }, [closeMenu, action]);

  const handleDuplicateTable: DuplicateFormSubmitCallback = useCallback(
    async ({ formData }) => {
      const payload = {
        name: formData.name,
        fields: table.fields || [],
        duplicateRecord: formData?.duplicateRecord,
      };

      const res = await duplicateTable(table.baseId, table.id, payload);
      if (res.isOk()) {
        onChange?.(MenuAction.DUPLICATE_TABLE, res.value.data);
        notifications.show({
          message: t('success.table.duplicate', { tableName: table.name }),
          status: 'success',
        });
        closeMenu();
      }
    },
    [duplicateTable, table, onChange, t, closeMenu]
  );

  const handleOpenTrash = useCallback(() => {
    openTableTrashViewModal();
  }, [openTableTrashViewModal]);

  const handleDeleteTable: ConfirmFormSubmitCallback = useCallback(async () => {
    const res = await removeTable(table.baseId, table.id);
    if (res.isOk()) {
      onChange?.(MenuAction.DELETE_TABLE, table);
      notifications.show({
        message: (
          <div>
            {t('success.table.remove', { tableName: table.name })}
            <span onClick={handleOpenTrash} className={classes.goToTrashLabel}>
              {t('success.table.goToTrash')}
            </span>
          </div>
        ),
        status: 'success',
      });
      revalidateDeletedTable(table.baseId);
      closeMenu();
    }
  }, [removeTable, table, onChange, t, closeMenu, revalidateDeletedTable]);

  const handleClearData = useCallback(async () => {
    addPendingClearTableId(table.id);
    onChange?.(MenuAction.CLEAR_DATA, table);
    closeMenu();
    await clearRecords(table.baseId, table.id);
  }, [clearRecords, table, onChange, closeMenu, addPendingClearTableId]);

  const handleRenameTable: RenameFormSubmitCallback = useCallback(
    async ({ formData }) => {
      const payload = {
        name: formData.name,
        fields: table.fields || [],
      };
      const res = await updateTable(table.baseId, table.id, payload);
      if (res.isOk()) {
        onChange?.(MenuAction.RENAME_TABLE, table);
        notifications.show({
          message: t('success.table.rename', { tableName: table.name }),
          status: 'success',
        });
        closeMenu();
      }
    },
    [updateTable, table, onChange, t, closeMenu]
  );

  const dropdownPositionStyles = position
    ? {
        left: position.x + NAVIGATION_MENU_AXIS_OFFSET.mainAxis,
        top: position.y,
      }
    : {};

  return (
    <>
      <Menu
        opened={menuOpened}
        onClose={handleMenuChange}
        position='right-start'
        withinPortal
        withArrow={showArrow}
        closeOnItemClick={false}
        transitionProps={{ onExited: handleMenuExited }}
        arrowOffset={8}
        offset={NAVIGATION_MENU_AXIS_OFFSET}
        classNames={{
          arrow: classes.menuArrow,
          dropdown: classes.menuDropdown,
          item: classes.menuItem,
          divider: classes.menuDivider,
          itemSection: classes.itemIcon,
          itemLabel: classes.itemLabel,
        }}
        styles={{
          dropdown: dropdownPositionStyles,
        }}>
        <Menu.Target>
          <ActionIcon variant='subtle' size={rem(24)} onClick={openMenu}>
            {icon}
          </ActionIcon>
        </Menu.Target>
        <Menu.Dropdown p={action === MenuAction.NO_ACTION ? rem(4) : 0}>
          {action === MenuAction.RENAME_TABLE && (
            <RenameForm
              onSubmit={handleRenameTable}
              onCancel={resetAction}
              defaultValues={{ name: table.name }}
              cancelText={t('navigation.renameForm.noBtn')}
              submitText={t('navigation.renameForm.yesBtn')}
              inputLabel={t('navigation.renameForm.title')}
            />
          )}

          {action === MenuAction.DELETE_TABLE && (
            <ConfirmForm
              confirmText={t('navigation.deleteTable.yesBtn')}
              cancelText={t('navigation.deleteTable.noBtn')}
              title={t('navigation.deleteTable.title')}
              warningText={t('navigation.deleteTable.warnText')}
              content={t('navigation.alertText')}
              onCancel={resetAction}
              onConfirm={handleDeleteTable}
            />
          )}

          {action === MenuAction.CLEAR_DATA && (
            <ConfirmForm
              confirmText={t('navigation.clearData.yesBtn')}
              cancelText={t('navigation.clearData.noBtn')}
              title={t('navigation.clearData.title')}
              warningText={t('navigation.clearData.warnText')}
              content={t('navigation.alertText')}
              onCancel={resetAction}
              onConfirm={handleClearData}
            />
          )}

          {action === MenuAction.NO_ACTION && (
            <>
              <Menu.Item
                leftSection={<IconEdit />}
                onClick={() => setAction(MenuAction.RENAME_TABLE)}>
                {t('navigation.tableMenu.rename')}
              </Menu.Item>
              <Menu.Item
                leftSection={<IconCopy />}
                onClick={() => setAction(MenuAction.DUPLICATE_TABLE)}>
                {t('navigation.tableMenu.duplicate')}
              </Menu.Item>
              <Menu.Divider />
              <Menu.Item
                className='red'
                leftSection={<IconX />}
                onClick={() => setAction(MenuAction.CLEAR_DATA)}>
                {t('navigation.tableMenu.clearData')}
              </Menu.Item>
              <Menu.Item
                className='red'
                leftSection={<IconTrash />}
                onClick={() => setAction(MenuAction.DELETE_TABLE)}>
                {t('navigation.tableMenu.deleteTable')}
              </Menu.Item>
            </>
          )}
        </Menu.Dropdown>
      </Menu>

      <DuplicateModal
        opened={action === MenuAction.DUPLICATE_TABLE}
        onSubmit={handleDuplicateTable}
        onCancel={resetAction}
        defaultValues={{ name: table.name }}
        title={t('navigation.duplicateTable.title', { tableName: table.name })}
        inputLabel={t('navigation.duplicateTable.inputLabel')}
        checkboxLabel={t('navigation.duplicateTable.checkboxLabel')}
        checkboxDesc={null}
        cancelText={t('navigation.duplicateTable.noBtn')}
        submitText={t('navigation.duplicateTable.yesBtn')}
      />
    </>
  );
};

export default NavigationMenuIcon;
