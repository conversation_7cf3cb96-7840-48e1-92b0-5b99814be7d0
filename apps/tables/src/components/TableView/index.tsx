import { UIEvent, useCallback, useEffect, useMemo, useRef, useState } from 'react';
import { useParams } from 'react-router-dom';
import { Anchor, Box, Divider, Flex, Group, LoadingOverlay, rem, Text } from '@mantine/core';
import { useDisclosure, useElementSize } from '@mantine/hooks';
import {
  Field,
  FieldType,
  IWebsocketResponse,
  RecordPagination,
  TableRecord,
  View,
  ViewRowHeight,
} from '@/types';
import {
  IconArrowUp,
  IconTrash,
  IconCopy,
  IconArrowDown,
  IconEdit,
  IconEyeOff,
  IconArrowLeft,
  IconArrowRight,
  IconSortAscendingLetters,
  IconSortDescendingLetters,
  IconLock,
  IconSpeakerphone,
  IconTable,
} from '@tabler/icons-react';
import {
  AddNewColumnButton,
  ColumnForm,
  DecaTable,
  OnSavingCell,
  OnViewChange,
  TableField,
  View as DecaTableView,
  ViewColumnFields,
  SortOrder,
  GetSortOrderOptionsCallback,
  ClipboardCallback,
} from '@resola-ai/ui/components';
import { DECA_TABLE_CLASSES } from '@resola-ai/ui/components/DecaTable/constants';
import { EmptyState } from './EmptyState';
import { notifications } from '@/components/Common';
import { useTranslate } from '@tolgee/react';
import {
  convertFromTableFieldOptionsPayload,
  convertToTableFieldOptionsPayload,
} from './TableFieldComponents';
import { SearchMatchedFields, useMinimongo } from './MiniMongo';
import {
  calculateColumnOrder,
  calculateInsertSortIds,
  generateFieldOptionsId,
  generateTempId,
  jsonToCsv,
  orderByIds,
  randomId,
  sortPinnedRecords,
} from '@/utils';
import {
  FIELD_TYPES,
  MAX_ROW_INSERT_LIMIT,
  UNSORTABLE_FIELD_TYPES,
  UNFILTERABLE_FIELD_TYPES,
  TEMP_RECORD_ID_PREFIX,
  MAX_IMAGE_FILE_SIZE,
  UNEDIT_FIELD_TYPES,
  MAX_COLUMN_WIDTH,
  MIN_COLUMN_WIDTH,
  DEFAULT_COLUMN_WIDTH,
  SUPPORTED_PRIMARY_FIELD_TYPES,
  DEFAULT_ROW_HEIGHT,
  RIGHT_UNORDERED_COLUMN_IDS,
  LEFT_UNORDERED_COLUMN_IDS,
  SCROLL_BOTTOM_THRESHOLD,
  BOTTOM_POSITION,
  TABLE_WRAPPER_ID,
} from '@/constants/table';
import { createCustomEventListener } from '@resola-ai/utils';
import { TABLES_WEBSOCKET_EVENTS } from '@/constants/event';
import { HEIGHT_OF_FIXED_STATUS_BAR, HEIGHT_OF_HEADER } from '@/constants';
import {
  CommonAPI,
  FieldAPICreatePayload,
  FieldAPIUpdatePayload,
  TableRecordAPICreatePayload,
} from '@/services/api';
import {
  useTableRecordsMutation,
  useTableFieldsMutation,
  useTableRecordsStream,
  useTableQuery,
  useTablesQuery,
  useTableViewsMutation,
  useTableViewsQuery,
} from '@/hooks';
import kebabCase from 'lodash/kebabCase';
import {
  CustomFieldsToolbarItem,
  SelectViewToolbarItem,
  TableHeightToolbarItem,
  TableSortToolbarItem,
  TableFilterToolbarItem,
  TableActionMenu,
  DefaultTableToolbarChangeTypes,
  TableActionMenuChangeTypes,
  TableSelectViewChangeTypes,
  TableCustomFieldsChangeTypes,
  TableFilterChangeTypes,
  TableSearch,
  TableAddRow,
  TableMenuViewToolbarItem,
} from '@resola-ai/ui/components/DecaTable/components/Toolbar';
import {
  ConfirmRowContextMenuItem,
  RowContextMenuItem,
  RowContextMenuSeparator,
  NumberInputRowContextMenuItem,
  HeaderContextMenuRenderProps,
  HeaderContextMenuItem,
  HeaderContextMenuSeparator,
  ConfirmHeaderContextMenuItem,
  RowContextMenuRenderProps,
  AddFieldFormProps,
  EditFieldFormProps,
} from '@resola-ai/ui/components/DecaTable/components/ContextMenu';
import { TableMenuViewChangeTypes } from '@resola-ai/ui/components/DecaTable/constants';
import { createStyles } from '@mantine/emotion';
import { getDateTime } from '@resola-ai/ui/components/DecaTable/utils';
import TableTrashViewModal from '../TableTrashView';
import { useAppContext } from '@/contexts';
import { MRT_ColumnSizingState, MRT_RowVirtualizer } from 'mantine-react-table';
import FixedStatusBar from './FixedStatusBar';
import {
  getViewPayload,
  getVisibleColumns,
  getOrderedColumns,
  convertViewSortOrderToSort,
  getSortTranslationKey,
  getFieldTypeFromMRTHeader,
  removeUnsortableFields,
  removeUnfilterableFields,
  getFieldFromViewColumnField,
} from './helper';
import { PrimaryFieldModal, PrimaryFieldFormSubmitCallback } from './PrimaryField';
import TableDataClearOverlay from './LoadingOverlay';
import { useSWRConfig } from 'swr';

const normalizeOptionValues = (value: string | string[]) => {
  if (Array.isArray(value)) {
    return value.filter(v => v !== '');
  }
  if (typeof value === 'string') {
    return value.includes(',')
      ? value
          .split(',')
          .map(v => v.trim())
          .filter(v => v !== '')
      : [value.trim()];
  }

  return [];
};

const useStyles = createStyles(theme => ({
  tableWrapper: {
    position: 'relative',
    display: 'flex',
    flexDirection: 'column',
    height: `calc(100vh - ${rem(HEIGHT_OF_HEADER)})`,
    paddingBottom: rem(HEIGHT_OF_FIXED_STATUS_BAR),

    [`${DECA_TABLE_CLASSES.DISABLE_ROW} > td`]: {
      pointerEvents: 'none',
    },
    [`&[data-table-empty="true"] .${DECA_TABLE_CLASSES.TABLE_CONTAINER}`]: {
      display: 'none',
    },
    [`.${DECA_TABLE_CLASSES.TABLE_PAPER}`]: {
      overflow: 'hidden',
      display: 'flex',
      flexDirection: 'column',
    },
    [`.${DECA_TABLE_CLASSES.TABLE_CONTAINER}`]: {
      flex: 1,
      scrollbarWidth: 'thin',
      contain: 'paint',
      willChange: 'transform',
    },
    [`.${DECA_TABLE_CLASSES.HEADER_EDIT_MENU}`]: {
      '--contexify-itemContent-padding': '0px',
      '--contexify-menu-padding': theme.spacing.md,
      '--contexify-menu-shadow': theme.shadows.md,
      '--contexify-menu-radius': theme.radius.sm,
      border: `1px solid ${theme.colors.decaLight[1]}`,
    },
  },
  loadingOverlay: {
    zIndex: '150',
  },
  customNotification: {
    '--notification-bg': theme.colors.decaBlue[0],
    '--notification-border': 'transparent',
    '--notification-icon-bg-color': theme.colors.decaBlue[6],
    '--notification-icon-color': 'transparent',
    '--notification-text-color': theme.colors.decaBlue[6],
    '--notification-close-button-color': theme.colors.decaBlue[6],
    '--notification-close-button-hover-bg': 'transparent',
    '--notification-radius': rem(16),
  },
  tableViewIcon: {
    color: theme.colors.decaBlue[5],
  },
}));

const TableView = () => {
  const { t } = useTranslate('table');

  const { classes } = useStyles();
  const { baseId, tableId } = useParams();
  const [columnSizing, setColumnSizing] = useState<MRT_ColumnSizingState>({});
  const [activeViewId, setActiveViewId] = useState<string>();
  const [isShowEmptyState, setIsShowEmptyState] = useState<boolean>(true);
  const [openedFormColumn, { close: closeFormColumn, toggle: toggleFormColumn }] =
    useDisclosure(false);
  const [openedFilterMenu, { close: closeFilterMenu, open: openFilterMenu }] = useDisclosure(false);
  const [openedSortMenu, { close: closeSortMenu, open: openSortMenu }] = useDisclosure(false);
  const [filterValue, setFilterValue] = useState<Record<string, any>>();
  const [rowSelection, setRowSelection] = useState<Record<string, boolean>>({});
  const [scrollPosition, setScrollPosition] = useState<'top' | 'bottom' | 'middle'>('top');
  const { ref: tableWrapperRef, height: tableWrapperHeight } = useElementSize();
  const [virtualizerHeight, setVirtualizerHeight] = useState(0);
  const [recordsPagination, setRecordsPagination] = useState<RecordPagination>();
  const [primaryFieldModalOpened, { open: openPrimaryFieldModal, close: closePrimaryFieldModal }] =
    useDisclosure();
  const [searchMatchedFields, setSearchMatchedFields] = useState<SearchMatchedFields>({});
  const [pinnedRecords, setPinnedRecords] = useState<TableRecord[]>([]);
  const {
    tableTrashViewModalOpened,
    openTableTrashViewModal,
    closeTableTrashViewModal,
    tablePage,
    pendingClearTableIds,
    removePendingClearTableId,
    addPendingClearTableId,
  } = useAppContext();

  const pinnedRecordIds = useMemo(() => {
    return pinnedRecords.map(record => record.id);
  }, [pinnedRecords]);

  const isCurrentTableClearing = useMemo(() => {
    if (!baseId || !tableId) return false;
    return pendingClearTableIds.includes(tableId);
  }, [baseId, tableId, pendingClearTableIds]);

  const searchResultCount = useMemo(() => {
    return Object.values(searchMatchedFields)
      .map(field => Object.keys(field).length)
      .reduce((a, b) => a + b, 0);
  }, [searchMatchedFields]);

  const rowIds = useMemo(() => {
    return Object.entries(rowSelection)
      .filter(([_, selected]) => selected)
      .map(([id]) => id);
  }, [rowSelection]);

  const tableContainerRef = useRef<HTMLDivElement>(null);
  const rowVirtualizerInstanceRef =
    useRef<MRT_RowVirtualizer<HTMLDivElement, HTMLTableRowElement>>(null);

  const {
    isLoading: isMinimongoLoading,
    data,
    totalDataCount,
    filter,
    updateFilter,
    updatePageIndex,
    upsert,
    updateOne,
    removeOne,
    initCollection,
    removeMany,
    findByField,
    findOne,
    findAll,
    isFetching,
    searchData,
    subscribe,
    find,
  } = useMinimongo<TableRecord>({
    collectionName: baseId && tableId ? `tables/${baseId}/${tableId}` : undefined,
  });
  const totalFetched = data.length;

  const { cache } = useSWRConfig();
  const { tables } = useTablesQuery(baseId, {
    page: tablePage,
  });
  const { views, isInitialLoading: isViewInitialLoading } = useTableViewsQuery(baseId, tableId);
  const { isLoading: isTableLoading, key: tableKey } = useTableQuery(baseId, tableId, {
    onSuccess: res => {
      const isCleaningData = res?.data?.system?.isCleaningData;
      if (isCleaningData) {
        addPendingClearTableId(tableId!);
      } else {
        removePendingClearTableId(tableId!);
      }
    },
  });

  useEffect(() => {
    return () => {
      cache.delete(tableKey as any);
    };
  }, []);

  useEffect(() => {
    const handleRemovePinnedRecord = async (docs: readonly TableRecord[]) => {
      const isRemovePinnedRecord = docs.some(doc => pinnedRecordIds.includes(doc.id));
      if (isRemovePinnedRecord) {
        const records = await find(pinnedRecordIds);
        setPinnedRecords(orderByIds(records, pinnedRecordIds));
      }
    };

    const handleUpdatePinnedRecord = async (docs: readonly TableRecord[]) => {
      const updatedRecord = docs[0];
      const isUpdatePinnedRecord = pinnedRecordIds.includes(updatedRecord.id);
      if (isUpdatePinnedRecord) {
        const records = await find(pinnedRecordIds);
        setPinnedRecords(orderByIds(records, pinnedRecordIds));
      }
    };

    const unsubscribe = subscribe(event => {
      const { type, docs } = event;
      switch (type) {
        case 'remove': {
          handleRemovePinnedRecord(docs);
          break;
        }
        case 'update': {
          handleUpdatePinnedRecord(docs);
        }
      }
    });
    return () => unsubscribe();
  }, [subscribe, pinnedRecordIds, find]);

  const { abort } = useTableRecordsStream(baseId, tableId, {
    enabled: !!activeViewId && !isTableLoading,
    onSuccess: async data => {
      const records = data.data;

      const autoIdFieldIds = tableFields
        .filter(field => field.type === FieldType.AUTOID)
        .map(field => field.id);

      if (autoIdFieldIds.length) {
        records.forEach((record, index) => {
          // @TODO: fallback value for autoId, remove this after BE updated
          const autoId = typeof record['autoId'] === 'undefined' ? index + 1 : record['autoId'];

          for (const fieldId of autoIdFieldIds) {
            if (typeof record[fieldId] === 'undefined') {
              record[fieldId] = autoId;
            }
          }
        });
      }

      await initCollection();
      await upsert(records);
      setRecordsPagination(data.pagination);
    },
  });

  useEffect(() => {
    return () => {
      abort?.();
    };
  }, [abort]);

  const {
    createRecords,
    removeRecord,
    removeRecords,
    updateRecord,
    duplicateRecord,
    isMutating: isRecordMutating,
    clearRecords,
  } = useTableRecordsMutation();
  const { createView, removeView, updateView } = useTableViewsMutation();
  const { createField, updateField, removeField, setPrimaryField } = useTableFieldsMutation();

  const activeTable = useMemo(() => {
    return tables?.find(table => table.id === tableId) || null;
  }, [tables, tableId]);

  const primaryFieldId = useMemo(() => {
    return activeTable?.fields.find(field => field.isPrimary)?.id;
  }, [activeTable]);

  const tableFields = useMemo(() => {
    if (!activeTable || !activeTable.fields) return [];
    const uniqueFields = new Set<string>();
    const fields: Field[] = [];
    for (const field of activeTable.fields) {
      if (!uniqueFields.has(field.id)) {
        uniqueFields.add(field.id);
        fields.push(field);
      }
    }
    return fields;
  }, [activeTable]);

  const tableFieldIds = useMemo(() => {
    return tableFields.map(f => f.id);
  }, [tableFields]);

  const tableViews: View[] = useMemo(() => {
    return views?.map(view => ({
      ...view,
      filter: removeUnfilterableFields(view.filter, tableFields, UNFILTERABLE_FIELD_TYPES),
      sort: removeUnsortableFields(view.sort, tableFields, UNSORTABLE_FIELD_TYPES),
    }));
  }, [views, tableFields]);

  const activeTableView = useMemo(() => {
    return tableViews.find(view => view.id === activeViewId) || tableViews[0] || null;
  }, [tableViews, activeViewId]);

  const isEmpty = isShowEmptyState && activeTable?.fields?.length === 0;
  const showTable = tableId && baseId && tables?.length > 0;

  useEffect(() => {
    const recordDeleteHandler = async (
      event: CustomEvent<IWebsocketResponse<{ recordIds: string[] }>>
    ) => {
      const recordIds = event.detail.data.recordIds || [];
      if (Array.isArray(recordIds) && recordIds.length > 0) {
        await removeMany(recordIds);
      }
    };

    const recordInsertHandler = async (
      event: CustomEvent<IWebsocketResponse<Record<string, any>[]>>
    ) => {
      const records = event.detail.data.map(d => ({ id: d._id, ...d }) as any);
      await upsert(records);
    };

    const recordUpdateHandler = async (
      event: CustomEvent<IWebsocketResponse<Record<string, any>>>
    ) => {
      // @TODO unify how to get id
      await updateOne(event.detail.data._id, event.detail.data);
    };

    const unregisterRecordDeleteEvent = createCustomEventListener(
      TABLES_WEBSOCKET_EVENTS.RECORDS_DELETE,
      recordDeleteHandler
    );

    const unregisterRecordInsertEvent = createCustomEventListener(
      TABLES_WEBSOCKET_EVENTS.RECORDS_INSERT,
      recordInsertHandler
    );

    const unregisterRecordUpdateEvent = createCustomEventListener(
      TABLES_WEBSOCKET_EVENTS.RECORDS_UPDATE,
      recordUpdateHandler
    );

    return () => {
      unregisterRecordDeleteEvent();
      unregisterRecordInsertEvent();
      unregisterRecordUpdateEvent();
    };
  }, [upsert, removeMany, updateOne]);

  // set default view
  useEffect(() => {
    if (!views || views.length === 0 || activeViewId) return;
    setActiveViewId(views[0].id);
  }, [views, activeViewId]);

  useEffect(() => {
    if (!activeTableView) return;

    updateFilter({
      selectors: activeTableView.filter,
      sort: activeTableView.sort,
    });
  }, [activeTableView, updateFilter]);

  const handleCreateView = useCallback(
    async (view: DecaTableView) => {
      if (!baseId || !tableId) return;
      const res = await createView(baseId, tableId, { name: view.name });
      if (res.isOk()) {
        setActiveViewId(res.value.data.id);
      }
    },
    [baseId, tableId, createView]
  );

  const handleDeleteView = useCallback(
    async (view: DecaTableView) => {
      if (!baseId || !tableId) return;
      await removeView(baseId, tableId, view.id);
      setActiveViewId(undefined);
    },
    [baseId, tableId, removeView]
  );

  const handleDeleteColumn = useCallback(
    async (fieldId: string) => {
      if (!baseId || !tableId || !fieldId) return;

      const removeRes = await removeField(baseId, tableId, fieldId);

      if (removeRes.isOk()) {
        if (tableFields.length > 1) return;
        await clearRecords(baseId, tableId);
        setIsShowEmptyState(true);
      }
    },
    [baseId, tableId, removeField, tableFields.length, clearRecords]
  );

  const handleDuplicateColumn = useCallback(
    async (fieldId: string, view: DecaTableView) => {
      if (!baseId || !tableId || !view) return;
      const field = view.fields.find(f => f.fieldMetaId === fieldId);

      if (field) {
        const payload: FieldAPICreatePayload = {
          name: field.header + ' copy',
          type: field.type as any,
          options: field.options || {},
        };

        await createField(baseId, tableId, payload);
      }
    },
    [baseId, tableId, createField]
  );

  const handleEditColumn = useCallback(
    async (fieldId: string, payload) => {
      if (!baseId || !tableId || !payload) return;

      const isPrimary = primaryFieldId === fieldId;

      await updateField(baseId, tableId, fieldId, {
        ...payload,
        isPrimary,
      });
    },
    [baseId, tableId, updateField, primaryFieldId]
  );

  const handleUpdateRecord: OnSavingCell = useCallback(
    async ({ columnId, id, value }) => {
      if (!baseId || !tableId || !id) return;

      const payload = {
        [columnId]: value,
      };

      const updatedRecord = await updateOne(id, payload);

      const res = await updateRecord(baseId, tableId, id, {
        ...payload,
        sortId: updatedRecord.sortId,
      });

      if (res.isOk()) {
        await updateOne(id, res.value.data);
      }
    },
    [updateRecord, baseId, tableId, updateOne, findOne]
  );

  const handleDeleteRows = useCallback(
    async (ids: string[]) => {
      if (!baseId || !tableId || isRecordMutating) return;

      const currentRecords = await findByField('_id', ids);
      await removeMany(ids);

      const res = await removeRecords(baseId, tableId, ids);

      if (res.isOk()) {
        notifications.show({ message: t('success.deleteRows'), status: 'success' });
      }

      if (res.isErr()) {
        if (currentRecords.length > 0) {
          await upsert(currentRecords);
        }
        notifications.show({ message: res.error, status: 'error' });
      }
    },
    [baseId, tableId, isRecordMutating, removeRecords, removeMany, t, upsert]
  );

  const handleDeleteRow = useCallback(
    async (id: string) => {
      if (!baseId || !tableId || !id || isRecordMutating) return;

      const currentRecords = await findByField('_id', id);
      await removeOne(id);

      const res = await removeRecord(baseId, tableId, id);

      if (res.isOk()) {
        notifications.show({ message: t('success.deleteRow'), status: 'success' });
      }

      if (res.isErr()) {
        if (currentRecords.length > 0) {
          await upsert(currentRecords);
        }
        notifications.show({ message: res.error, status: 'error' });
      }
    },
    [baseId, tableId, isRecordMutating, removeRecord, removeOne, t, findByField]
  );

  const handleDeleteRowSelection = useCallback(
    async (rowIds: string[]) => {
      const isDeleteMultiple = rowIds.length > 1;
      if (isDeleteMultiple) {
        await handleDeleteRows(rowIds);
      } else {
        await handleDeleteRow(rowIds[0]);
      }
    },
    [handleDeleteRows, handleDeleteRow]
  );

  const handleInsertRecords = useCallback(
    async (records: TableRecordAPICreatePayload['records'], isPinned: boolean = false) => {
      if (!baseId || !tableId || isRecordMutating) return;

      const defaultToCurrentDateFields = tableFields.filter(
        f => f.type === FieldType.DATETIME && f.options?.defaultToCurrentDate
      );

      if (defaultToCurrentDateFields.length > 0) {
        const currentDate = new Date().toISOString();

        for (const record of records) {
          for (const field of defaultToCurrentDateFields) {
            record[field.id] = currentDate;
          }
        }
      }

      const res = await createRecords(baseId, tableId, { records });

      if (res.isOk()) {
        const updatedRecords = res.value.data.map(record => ({ id: record._id, ...record }));
        await upsert(updatedRecords);

        if (isPinned) {
          setPinnedRecords(prev => [...updatedRecords, ...prev]);
        }
      }
    },
    [baseId, tableId, isRecordMutating, createRecords, upsert, removeMany, tableFields]
  );

  const handleDuplicateRow = useCallback(
    async (recordId: string) => {
      if (!baseId || !tableId || !recordId) return;

      const recordToDuplicate = data.find(record => record.id === recordId);
      if (!recordToDuplicate) return;
      const { id, sortId, ...restRecord } = recordToDuplicate;

      const newRecordInsertIds = calculateInsertSortIds(
        'below',
        1,
        sortId,
        recordToDuplicate,
        data
      );

      if (newRecordInsertIds.length === 0) return;

      const duplicateRecordPayload = {
        sortId: newRecordInsertIds[0].sortId,
        records: [],
      };

      const tempRecord = {
        id: generateTempId(TEMP_RECORD_ID_PREFIX),
        ...restRecord,
        ...duplicateRecordPayload,
      };

      upsert([tempRecord as TableRecord]);

      const res = await duplicateRecord(baseId, tableId, recordId, duplicateRecordPayload);

      removeMany([tempRecord.id]);

      if (res.isOk()) {
        const updatedRecords = res.value.data.map(record => ({ id: record._id, ...record }));
        upsert(updatedRecords);
      }
    },
    [baseId, tableId, duplicateRecord, upsert, removeMany, data]
  );

  const handleInsertRow = useCallback(
    async (position: 'above' | 'below', recordId: string, numberOfInserts: number = 1) => {
      if (numberOfInserts <= 0) return;

      if (numberOfInserts > MAX_ROW_INSERT_LIMIT) {
        notifications.show({ message: t('error.maxRowInsertLimit'), status: 'error' });
        return;
      }

      const currentRecord = data.find(record => record.id === recordId);
      if (!currentRecord) return;

      if (!currentRecord.sortId) {
        console.log('no sortId found for record', recordId);
      }

      const newRecordInsertIds = calculateInsertSortIds(
        position,
        numberOfInserts,
        currentRecord.sortId,
        currentRecord,
        data
      );

      if (newRecordInsertIds.length) {
        await handleInsertRecords(newRecordInsertIds);
      }
    },
    [data, handleInsertRecords, t]
  );

  const handleAddNewRow = useCallback(async () => {
    const hasFilterOrSort =
      Object.keys(activeTableView?.filter || {}).length > 0 ||
      Object.keys(activeTableView?.sort || {}).length > 0;

    await handleInsertRecords([{}], hasFilterOrSort);

    if (hasFilterOrSort) {
      const notificationId = randomId();
      notifications.show({
        id: notificationId,
        message: (
          <Group align='center' gap={rem(2)}>
            <Text>{t('newRecordAdded')}</Text>
            <Anchor
              component='button'
              c='inherit'
              underline='always'
              onClick={() => {
                setPinnedRecords([]);
                notifications.hide(notificationId);
              }}
            >
              {t('updateView')}
            </Anchor>
          </Group>
        ),
        status: 'custom',
        autoClose: 3000,
        withCloseButton: true,
        position: 'top-right',
        icon: <IconSpeakerphone />,
        offsetY: 24,
        offsetX: -40,
        classNames: {
          root: classes.customNotification,
        },
      });
    }
  }, [handleInsertRecords, data, activeTableView?.filter, activeTableView?.sort, t]);

  const handleFileUpload = useCallback(
    async (file: File) => {
      if (!baseId || !tableId) return;

      if (file.size > MAX_IMAGE_FILE_SIZE) {
        notifications.show({
          message: t('error.fileSizeExceeded'),
          status: 'error',
        });
        return;
      }

      const presignUrlRes = await CommonAPI.presignUrl({
        baseId: baseId,
        tableId: tableId,
        mimeType: file.type,
        name: file.name,
        size: file.size,
        feature: 'images',
        source: 'assets',
      });

      if (presignUrlRes.isOk()) {
        const presignUrl = presignUrlRes.value;
        await CommonAPI.uploadFile(presignUrl.uploadUrl, file);

        const cdnUrl = import.meta.env.VITE_CDN_URL || 'https://cdn.deca-dev.com';
        return `${cdnUrl}/${presignUrl.file.path}`;
      }
    },
    [baseId, tableId, t]
  );

  const columns = useMemo<ViewColumnFields[]>(() => {
    return tableFields.map(field => {
      const currentTableField = FIELD_TYPES[field.type] as TableField;
      const config = convertFromTableFieldOptionsPayload(field.options, field.type);

      return {
        id: field.id,
        fieldMetaId: field.id,
        accessorKey: field.id,
        isVisible: true,
        type: field.type,
        header: field.name,
        options: field.options as any,
        enableColumnOrdering: !field.isPrimary,
        size: currentTableField.columnWidth || DEFAULT_COLUMN_WIDTH,
        minSize: MIN_COLUMN_WIDTH,
        maxSize: MAX_COLUMN_WIDTH,
        enableEditing: !UNEDIT_FIELD_TYPES.includes(field.type as any),
        headerIcon: currentTableField?.icon ? currentTableField.icon : undefined,
        Cell: ({ cell, table }) => {
          const CellComponent = currentTableField?.data?.cell;
          if (!CellComponent) return null;
          return (
            <Flex mih={rem(22)} w='100%' align='center' sx={{ userSelect: 'none' }}>
              <CellComponent
                cell={cell}
                table={table}
                config={config}
                onSavingCell={handleUpdateRecord}
              />
            </Flex>
          );
        },
        Edit: ({ cell, table }) => {
          const EditCellComponent = currentTableField?.data?.editCell;
          if (!EditCellComponent) return null;
          return (
            <Box mih={rem(22)} w='100%' sx={{ userSelect: 'none' }}>
              <EditCellComponent
                cell={cell}
                type={field.type}
                table={table}
                config={config}
                onSavingCell={handleUpdateRecord}
                onSavingColumn={async ({ id, options }) => {
                  if (!id || !options) return;
                  const payload = {
                    name: field.name,
                    options: convertToTableFieldOptionsPayload(options as any, field.type) as any,
                    type: field.type,
                  };
                  await handleEditColumn(id, payload);
                }}
                generateUniqueIdFunction={generateFieldOptionsId}
                onFileUpload={handleFileUpload}
              />
            </Box>
          );
        },
      };
    });
  }, [tableFields, handleUpdateRecord, handleEditColumn, handleFileUpload]);

  const displayViews = useMemo(() => {
    return tableViews?.map(view => {
      const rowHeight = view.options?.rowHeight === ViewRowHeight.SHORT ? 'sm' : 'md';
      const sort = view.sort ? convertViewSortOrderToSort(view.sort) : [];
      const filters = view.filter || {};
      const autoSort = !!view.options?.automaticSorting;

      const sortedFields = view.sortHeader ? getOrderedColumns(columns, view.sortHeader) : columns;
      const columnOrder = sortedFields.map(field => field.id);

      const displayFields = view.project
        ? getVisibleColumns(sortedFields, view.project)
        : sortedFields;

      return {
        id: view.id,
        name: view.name,
        type: view.type,
        description: view.description,
        filters,
        sort,
        fields: displayFields,
        rowHeight,
        columnOrder,
        autoSort,
      } as DecaTableView;
    });
  }, [tableViews, columns]);

  const selectedDisplayView = useMemo(() => {
    if (!Array.isArray(displayViews) || displayViews.length === 0) return null;
    const defaultView = displayViews[0];
    if (!activeViewId) return defaultView;
    return displayViews.find(view => view.id === activeViewId) ?? defaultView;
  }, [displayViews, activeViewId]);

  // @TODO: remove this mapper when API is updated
  const renderData = useMemo(() => {
    if (!activeTable) return data;
    const { fields } = activeTable;

    const sortedRecords = sortPinnedRecords(data, pinnedRecords);

    return sortedRecords.map(record => {
      const updatedRecord = { ...record };

      fields.forEach((field: Field) => {
        const { id, type } = field;

        if (
          [
            FieldType.CREATEDAT,
            FieldType.UPDATEDAT,
            FieldType.CREATEDBY,
            FieldType.UPDATEDBY,
          ].includes(type)
        ) {
          updatedRecord[id] = record[type];
        }
      });
      return updatedRecord;
    });
  }, [activeTable, data, pinnedRecords]);

  const handleExportData = useCallback(async () => {
    if (!activeTable) return;

    const { fields } = activeTable;

    const data = await findAll();

    if (!data || !fields) {
      return;
    }

    const tableName = activeTable?.name ?? 'default_table';
    const viewName = selectedDisplayView?.name ?? 'default_view';
    const exportFileName = kebabCase(`${tableName}-${viewName}`) + '.csv';

    const exportData = data.map(record => {
      const updatedRecord = { ...record };

      fields.forEach((field: Field) => {
        const { id, type } = field;

        switch (type) {
          case FieldType.CREATEDBY:
          case FieldType.UPDATEDBY: {
            updatedRecord[id] = record[type]?.name || record[type];
            break;
          }

          case FieldType.DATETIME: {
            const value = record[id];
            if (value) {
              const options = convertFromTableFieldOptionsPayload(field.options, field.type) as any;

              const date = getDateTime({
                dateFormat: options?.date?.format,
                timeType: options?.time?.format,
                tz: options?.timezone?.format,
                displayTz: options?.displayTimezone,
                date: value,
              });
              updatedRecord[id] = date;
            }
            break;
          }

          case FieldType.CREATEDAT:
          case FieldType.UPDATEDAT: {
            const value = record[type];
            if (value) {
              const options = convertFromTableFieldOptionsPayload(field.options, field.type) as any;

              const date = getDateTime({
                dateFormat: options?.date?.format,
                timeType: options?.time?.format,
                tz: options?.timezone?.format,
                displayTz: options?.displayTimezone,
                date: value,
              });
              updatedRecord[id] = date;
            }
            break;
          }

          case FieldType.MULTISELECT: {
            const options = field.options?.choices || [];
            const defaultValue = normalizeOptionValues(field.options?.defaultValue || '');
            const selectedValues = normalizeOptionValues(record[id]);
            const value = selectedValues.length > 0 ? selectedValues : defaultValue;
            updatedRecord[id] =
              options.filter(option => value.includes(option.id)).map(option => option.label) || [];
            break;
          }

          case FieldType.SELECT: {
            const options = field.options?.choices || [];
            const selectedValue = record[id];
            const defaultValue = field.options?.defaultValue;
            const value = selectedValue || defaultValue;
            updatedRecord[id] = options.find(option => option.id === value)?.label || '';
            break;
          }

          case FieldType.CHECKBOX: {
            updatedRecord[id] = !!record[id];
            break;
          }
        }
      });

      return updatedRecord;
    });

    const res = await jsonToCsv(
      exportData,
      columns.map(column => ({ accessor: column.id, title: column.header })),
      {
        filename: exportFileName,
      }
    );

    if (!res.isOk()) {
      notifications.show({ message: t('errors.failedToExportCSV'), status: 'error' });
    }
  }, [activeTable, selectedDisplayView, findAll, t]);

  const handleUpdateView = useCallback(
    async (view: DecaTableView, columnSizing: Record<string, number> = {}) => {
      if (!baseId || !tableId) return;
      const payload = getViewPayload(view, tableFieldIds, columnSizing);

      if (payload.filter || payload.sort) {
        updateFilter({ selectors: payload.filter, sort: payload.sort });
      }

      await updateView(baseId, tableId, view.id, payload, { optimisticUpdate: true });
    },
    [baseId, tableId, updateView, updateFilter, tableFieldIds]
  );

  const handleOrderColumn = useCallback(
    async (viewId: string, sortedColumnIds: string[], additionalColumnIds?: string[]) => {
      if (!baseId || !tableId) return;

      const orderColumnIds = calculateColumnOrder(
        additionalColumnIds ? [...tableFieldIds, ...additionalColumnIds] : tableFieldIds,
        sortedColumnIds
      );

      const payload = {
        sortHeader: orderColumnIds,
      };

      await updateView(baseId, tableId, viewId, payload, { optimisticUpdate: true });
    },
    [baseId, tableId, tableFieldIds]
  );

  const getTableColumnSizing = useCallback(
    (updatedColumnSizing: Record<string, number>) => {
      const columnSizes = activeTableView?.options?.columnSizes || {};
      return columns.reduce((acc, column) => {
        const columnSize = column.size;
        const columnId = column.id;
        acc[columnId] = updatedColumnSizing[columnId] || columnSizes[columnId] || columnSize;
        return acc;
      }, {});
    },
    [columns, activeTableView]
  );

  const tableColumnSizing = useMemo(() => {
    return getTableColumnSizing(columnSizing);
  }, [columns, columnSizing, getTableColumnSizing]);

  const handleColumnSizingChange = useCallback(
    updateFn => {
      const updatedColumnSizing = updateFn();
      const newColumnSizing = getTableColumnSizing(updatedColumnSizing);

      setColumnSizing(newColumnSizing);

      if (selectedDisplayView) {
        handleUpdateView(selectedDisplayView, newColumnSizing);
      }
    },
    [selectedDisplayView, handleUpdateView, getTableColumnSizing]
  );

  const handleDuplicateView = useCallback(
    async (view: DecaTableView) => {
      if (!baseId || !tableId) return;

      const newColumnSizing = getTableColumnSizing(columnSizing);
      const payload = getViewPayload(view, tableFieldIds, newColumnSizing);
      const res = await createView(baseId, tableId, payload);
      if (res.isOk()) {
        setActiveViewId(res.value.data.id);
      }
    },
    [baseId, tableId, createView, columnSizing, getTableColumnSizing, tableFieldIds]
  );

  const handleViewChange = useCallback<OnViewChange<string>>(
    async (id, view, type) => {
      switch (type) {
        case TableMenuViewChangeTypes.CREATE_VIEW: {
          await handleCreateView(view);
          break;
        }
        case TableSelectViewChangeTypes.DUPLICATE_VIEW: {
          await handleDuplicateView(view);
          break;
        }
        case TableSelectViewChangeTypes.DELETE_VIEW: {
          await handleDeleteView(view);
          break;
        }
        case TableMenuViewChangeTypes.SWITCH_VIEW: {
          setActiveViewId(view.id);
          break;
        }
        case TableFilterChangeTypes.FILTER_VIEW: {
          await handleUpdateView(view);
          break;
        }
        case TableCustomFieldsChangeTypes.ORDER_COLUMN: {
          await handleOrderColumn(view.id, view.fieldOrder || []);
          break;
        }
        case TableCustomFieldsChangeTypes.EDIT_COLUMN: {
          const field = view.fields.find(f => f.fieldMetaId === id);

          if (field) {
            const payload: Partial<Field> = {
              id,
              name: field.header,
              type: field.type as any,
              options: field.options,
            };

            await handleEditColumn(id, payload);
          }
          break;
        }
        case TableCustomFieldsChangeTypes.DELETE_COLUMN: {
          await handleDeleteColumn(id);
          break;
        }
        case TableCustomFieldsChangeTypes.DUPLICATE_COLUMN: {
          await handleDuplicateColumn(id, view);
          break;
        }
        case TableActionMenuChangeTypes.EXPORT_DATA: {
          await handleExportData();
          break;
        }
        case TableActionMenuChangeTypes.TRASH_VIEW: {
          openTableTrashViewModal();
          break;
        }
        case DefaultTableToolbarChangeTypes.UPDATE_VIEW:
        default: {
          await handleUpdateView(view);
          break;
        }
      }
    },
    [
      handleCreateView,
      handleDuplicateView,
      handleCreateView,
      handleUpdateView,
      handleDeleteColumn,
      handleEditColumn,
      handleDuplicateColumn,
      handleExportData,
      handleOrderColumn,
    ]
  );

  const handleCreateColumn = useCallback(
    async (payload: FieldAPICreatePayload, insertIndex?: number) => {
      if (!baseId || !tableId) return;

      const { res, updateQueryCache } = await createField(baseId, tableId, payload, {
        updateCacheOnSuccess: false,
      });

      if (res.isOk()) {
        const updatedField = res.value.data;

        if (activeViewId && typeof insertIndex !== 'undefined') {
          const fieldOrder = selectedDisplayView?.columnOrder || [];

          const sortedFieldIds =
            insertIndex >= fieldOrder.length
              ? [...fieldOrder, updatedField.id]
              : [
                  ...fieldOrder.slice(0, insertIndex),
                  updatedField.id,
                  ...fieldOrder.slice(insertIndex),
                ];

          await handleOrderColumn(activeViewId, sortedFieldIds, [updatedField.id]);
        }

        if (updatedField.type === FieldType.AUTOID) {
          const allRecords = await findAll({ ignoreFilter: true });
          const updatedRecords = allRecords.map((record, index) => ({
            ...record,
            [updatedField.id]: index + 1,
          }));
          await upsert(updatedRecords);
        }
        updateQueryCache(baseId, tableId, updatedField);
      }
    },
    [
      baseId,
      tableId,
      createField,
      activeViewId,
      selectedDisplayView?.columnOrder,
      handleOrderColumn,
      findAll,
      upsert,
    ]
  );

  const getInsertColumnIndex = useCallback(
    (id: string, insertType: 'left' | 'right'): number => {
      const fieldOrder = selectedDisplayView?.columnOrder || [];
      const index = fieldOrder.findIndex(field => field === id);
      if (index === -1) {
        return insertType === 'left' ? 0 : fieldOrder.length;
      }
      return insertType === 'left' ? index : index + 1;
    },
    [selectedDisplayView?.columnOrder]
  );

  const renderEditColumnField = useCallback(
    (props: AddFieldFormProps | EditFieldFormProps) => {
      if (props.mode === 'edit') {
        const { data, onClose } = props;
        const options = convertFromTableFieldOptionsPayload(data?.options, data?.type) || undefined;
        return (
          <ColumnForm
            data={{
              name: data?.header,
              type: data?.type,
              options: options,
            }}
            onSubmit={async formData => {
              const options = convertToTableFieldOptionsPayload(formData.options, formData.type);
              const payload = { name: formData.name, type: formData.type, options };
              await handleEditColumn(data.id, payload);
            }}
            onCancel={onClose}
            generateIdFunction={generateFieldOptionsId}
          />
        );
      } else {
        const { id, insertType, onClose } = props;
        return (
          <ColumnForm
            onSubmit={async formData => {
              const options =
                convertToTableFieldOptionsPayload(formData.options, formData.type) || null;
              const insertIndex = getInsertColumnIndex(id, insertType);
              const payload = { name: formData.name, type: formData.type, options };
              await handleCreateColumn(payload, insertIndex);
            }}
            onCancel={() => {
              if (tableFields.length === 0) {
                setIsShowEmptyState(true);
              }
              onClose();
            }}
            generateIdFunction={generateFieldOptionsId}
          />
        );
      }
    },
    [handleEditColumn, handleCreateColumn, getInsertColumnIndex, tableFields.length]
  );

  const renderAddNewField = useCallback(() => {
    return (
      <AddNewColumnButton
        onSubmit={async formData => {
          const options =
            convertToTableFieldOptionsPayload(formData.options, formData.type) || null;
          const payload = {
            name: formData.name,
            type: formData.type,
            options: options,
          };
          await handleCreateColumn(payload);
        }}
        generateIdFunction={generateFieldOptionsId}
        opened={openedFormColumn}
        onClose={() => {
          if (tableFields.length === 0) {
            setIsShowEmptyState(true);
          }
          closeFormColumn();
        }}
        onToggle={toggleFormColumn}
      />
    );
  }, [handleCreateColumn, openedFormColumn, closeFormColumn, toggleFormColumn, tableFields.length]);

  const renderRowContextMenu = useCallback(
    (props: RowContextMenuRenderProps) => {
      const { isMultipleRowsSelected } = props;
      return (
        <>
          {!isMultipleRowsSelected && (
            <>
              <NumberInputRowContextMenuItem
                icon={<IconArrowUp />}
                label={t('rowContextMenu.insertAbove')}
                max={MAX_ROW_INSERT_LIMIT}
                onSelect={async ({ rowIds, insertNumber }) =>
                  await handleInsertRow('above', rowIds[0], insertNumber)
                }
              />
              <NumberInputRowContextMenuItem
                icon={<IconArrowDown />}
                label={t('rowContextMenu.insertBelow')}
                max={MAX_ROW_INSERT_LIMIT}
                onSelect={async ({ rowIds, insertNumber }) =>
                  await handleInsertRow('below', rowIds[0], insertNumber)
                }
              />
              <RowContextMenuSeparator />
              <RowContextMenuItem
                icon={<IconCopy />}
                label={t('rowContextMenu.duplicate')}
                onSelect={async ({ rowIds }) => {
                  await handleDuplicateRow(rowIds[0]);
                }}
              />
              <RowContextMenuSeparator />
            </>
          )}
          <ConfirmRowContextMenuItem
            color='red'
            icon={<IconTrash />}
            label={t('rowContextMenu.delete')}
            onConfirm={async ({ rowIds }) => {
              await handleDeleteRowSelection(rowIds);
            }}
            confirmModalProps={{
              title: t(
                isMultipleRowsSelected
                  ? 'rowContextMenu.deleteRowsWarningTitle'
                  : 'rowContextMenu.deleteRowWarningTitle'
              ),
              message: t(
                isMultipleRowsSelected
                  ? 'rowContextMenu.deleteRowsWarningMessage'
                  : 'rowContextMenu.deleteRowWarningMessage'
              ),
            }}
          />
        </>
      );
    },
    [t, handleDeleteRowSelection, handleDuplicateRow, handleInsertRow]
  );

  const handleUpdatePrimaryField = useCallback(
    async (fieldId: string, payload: FieldAPIUpdatePayload) => {
      if (!baseId || !tableId || !fieldId) return;
      await setPrimaryField(baseId, tableId, fieldId, payload);
    },
    [setPrimaryField]
  );

  const handlePrimaryFieldModalSubmit: PrimaryFieldFormSubmitCallback = useCallback(
    async ({ formData, closeModal }) => {
      await handleUpdatePrimaryField(formData.fieldId, formData.payload);
      closeModal();
    },
    [handleUpdatePrimaryField]
  );

  const renderHeaderContextMenu = useCallback(
    (props: HeaderContextMenuRenderProps) => {
      const {
        enableInsertLeft,
        enableInsertRight,
        isFieldInSortOrFilter,
        handleOpenEdit,
        handleOpenInsert,
        getSortByColumnUpdate,
        getFilterByColumnUpdate,
        getDuplicateColumnUpdate,
        getToggleColumnUpdate,
        getPrimaryFieldUpdate,
        currentHeader,
      } = props;

      const fieldType = getFieldTypeFromMRTHeader(currentHeader);
      const sortTranslationKey = getSortTranslationKey(fieldType);
      const enableSort = !UNSORTABLE_FIELD_TYPES.includes(fieldType);
      const enableFilter = !UNFILTERABLE_FIELD_TYPES.includes(fieldType);
      const enableChangePrimaryField = SUPPORTED_PRIMARY_FIELD_TYPES.includes(fieldType);
      const isPrimaryField = currentHeader?.id === primaryFieldId;

      return (
        <>
          <HeaderContextMenuItem
            icon={<IconEdit />}
            label={t('headerContextMenu.editField')}
            onSelect={params => {
              handleOpenEdit(params.event);
            }}
            closeOnClick={false}
          />
          <HeaderContextMenuItem
            icon={<IconCopy />}
            label={t('headerContextMenu.duplicateField')}
            onSelect={async () => {
              const update = getDuplicateColumnUpdate();
              if (update) {
                await handleDuplicateColumn(update.id, update.view);
              }
            }}
          />
          <HeaderContextMenuItem
            icon={<IconLock />}
            label={t(
              isPrimaryField
                ? 'headerContextMenu.changePrimaryField'
                : 'headerContextMenu.setPrimaryField'
            )}
            tooltip={
              !enableChangePrimaryField
                ? t('headerContextMenu.unsupportedPrimaryFieldTooltip')
                : undefined
            }
            disabled={!enableChangePrimaryField}
            onSelect={async () => {
              if (isPrimaryField) {
                openPrimaryFieldModal();
              } else {
                const update = getPrimaryFieldUpdate();
                if (update && update.field) {
                  const fieldPayload = getFieldFromViewColumnField(update.field);
                  await handleUpdatePrimaryField(update.id, fieldPayload);
                }
              }
            }}
          />
          <HeaderContextMenuItem
            icon={<IconEyeOff />}
            label={t('headerContextMenu.hideField')}
            tooltip={
              isPrimaryField ? t('headerContextMenu.disabledPrimaryFieldTooltip') : undefined
            }
            disabled={isPrimaryField || isFieldInSortOrFilter}
            onSelect={async () => {
              const update = getToggleColumnUpdate();
              if (update) {
                await handleUpdateView(update.view);
              }
            }}
          />
          <HeaderContextMenuSeparator />
          {enableInsertLeft && (
            <HeaderContextMenuItem
              icon={<IconArrowLeft />}
              disabled={isPrimaryField}
              label={t('headerContextMenu.insertLeft')}
              onSelect={params => {
                handleOpenInsert(params.event, 'left');
              }}
            />
          )}
          {enableInsertRight && (
            <HeaderContextMenuItem
              icon={<IconArrowRight />}
              label={t('headerContextMenu.insertRight')}
              onSelect={params => {
                handleOpenInsert(params.event, 'right');
              }}
            />
          )}
          {(enableInsertLeft || enableInsertRight) && <HeaderContextMenuSeparator />}
          {enableSort && (
            <>
              <HeaderContextMenuItem
                icon={<IconSortAscendingLetters />}
                label={t(sortTranslationKey.asc)}
                onSelect={async () => {
                  const update = getSortByColumnUpdate(SortOrder.Ascending);
                  if (update) {
                    await handleUpdateView(update.view);
                  }
                }}
              />
              <HeaderContextMenuItem
                icon={<IconSortDescendingLetters />}
                label={t(sortTranslationKey.desc)}
                onSelect={async () => {
                  const update = getSortByColumnUpdate(SortOrder.Descending);
                  if (update) {
                    await handleUpdateView(update.view);
                  }
                }}
              />
            </>
          )}
          {enableFilter && (
            <>
              <HeaderContextMenuSeparator />
              <HeaderContextMenuItem
                icon={<IconSortDescendingLetters />}
                label={t('headerContextMenu.filterBy')}
                onSelect={async () => {
                  const update = getFilterByColumnUpdate();
                  const updatedFilter = update?.view?.filters;
                  if (updatedFilter) {
                    setFilterValue(updatedFilter);
                    openFilterMenu();
                  }
                }}
              />
              <HeaderContextMenuSeparator />
            </>
          )}
          <ConfirmHeaderContextMenuItem
            color='red'
            disabled={isFieldInSortOrFilter || isPrimaryField}
            icon={<IconTrash />}
            tooltip={
              isPrimaryField ? t('headerContextMenu.disabledPrimaryFieldTooltip') : undefined
            }
            label={t('headerContextMenu.delete')}
            onConfirm={async ({ columnId }) => {
              await handleDeleteColumn(columnId);
            }}
            confirmModalProps={{
              title: t('headerContextMenu.deleteColumnWarningTitle'),
              message: t('headerContextMenu.deleteColumnWarningMessage', {
                field: currentHeader?.column.columnDef.header,
              }),
              confirmText: t('headerContextMenu.deleteColumnConfirmText'),
            }}
          />
        </>
      );
    },
    [
      t,
      handleDeleteColumn,
      handleDuplicateColumn,
      handleUpdateView,
      openFilterMenu,
      primaryFieldId,
      handleUpdatePrimaryField,
    ]
  );

  const getSortOrderOptions: GetSortOrderOptionsCallback = useCallback(
    fieldType => {
      const sortTranslationKey = getSortTranslationKey(fieldType);
      return [
        { label: t(sortTranslationKey.asc), value: SortOrder.Ascending },
        { label: t(sortTranslationKey.desc), value: SortOrder.Descending },
      ];
    },
    [t]
  );

  const getColumnEstimateSize = useCallback(
    (index: number): number => {
      const column = columns[index];
      const size = column?.size || DEFAULT_COLUMN_WIDTH;
      return size;
    },
    [columns]
  );

  const getRowEstimateSize = useCallback(
    (_: number): number => {
      switch (selectedDisplayView?.rowHeight) {
        case 'sm':
          return ViewRowHeight.SHORT;
        case 'md':
          return ViewRowHeight.MEDIUM;
        default:
          return DEFAULT_ROW_HEIGHT;
      }
    },
    [selectedDisplayView?.rowHeight]
  );

  const columnOrder = useMemo(() => {
    if (Array.isArray(selectedDisplayView?.columnOrder)) {
      return [
        ...LEFT_UNORDERED_COLUMN_IDS,
        ...selectedDisplayView.columnOrder,
        ...RIGHT_UNORDERED_COLUMN_IDS,
      ];
    }
    return [];
  }, [selectedDisplayView?.columnOrder]);

  const columnPinning = useMemo(() => {
    return {
      left: [...LEFT_UNORDERED_COLUMN_IDS, primaryFieldId],
    };
  }, [primaryFieldId]);

  const handleColumnOrderChange = useCallback(
    newColumnOrder => {
      if (!selectedDisplayView?.id || !newColumnOrder || !Array.isArray(newColumnOrder)) return;
      const updatedColumnOrder = newColumnOrder.filter(item => columnOrder.includes(item));
      handleOrderColumn(selectedDisplayView?.id, updatedColumnOrder);
    },
    [handleOrderColumn, selectedDisplayView?.id, columnOrder]
  );

  const fetchNextPage = useCallback(async () => {
    updatePageIndex(page => page + 1);
  }, [updatePageIndex]);

  const updateScrollPosition = useCallback((scrollTop: number, offsetToBottom: number) => {
    setScrollPosition(() => {
      if (scrollTop === 0) return 'top';
      if (Math.abs(offsetToBottom) < BOTTOM_POSITION) return 'bottom';
      return 'middle';
    });
  }, []);

  const fetchMoreOnBottomReached = useCallback(
    (containerRefElement?: HTMLDivElement | null) => {
      if (containerRefElement) {
        const { scrollHeight, scrollTop, clientHeight } = containerRefElement;
        const offsetToBottom = scrollHeight - scrollTop - clientHeight;
        const hasNextPage = totalFetched < totalDataCount;

        updateScrollPosition(scrollTop, offsetToBottom);

        if (offsetToBottom < SCROLL_BOTTOM_THRESHOLD && !isFetching && hasNextPage) {
          fetchNextPage();
        }
      }
    },
    [fetchNextPage, isFetching, totalFetched, totalDataCount, updateScrollPosition]
  );

  const enableBottomNavigation = useMemo(() => {
    return virtualizerHeight > tableWrapperHeight && totalFetched >= totalDataCount;
  }, [totalFetched, totalDataCount, tableWrapperHeight, virtualizerHeight]);

  const handleGoToTop = useCallback(() => {
    if (rowVirtualizerInstanceRef.current) {
      rowVirtualizerInstanceRef.current.scrollToIndex(0);
    }
  }, []);

  const handleGoToBottom = useCallback(() => {
    if (rowVirtualizerInstanceRef.current) {
      rowVirtualizerInstanceRef.current.scrollToIndex(totalDataCount - 1, {
        align: 'start',
      });
    }
  }, [totalDataCount]);

  const handleCopy: ClipboardCallback = useCallback((_, isSuccess) => {
    if (isSuccess) {
      notifications.show({ message: t('success.copy'), status: 'success' });
    }
  }, []);

  const handleClearTableOverlay = useCallback(() => {
    if (!tableId) return;
    const tableName = activeTable?.name || '';
    removePendingClearTableId(tableId);
    notifications.show({ message: t('success.clearData', { tableName }), status: 'success' });
  }, [removePendingClearTableId, tableId, activeTable?.name, t]);

  //scroll to top of table when sorting or filters change
  useEffect(() => {
    if (rowVirtualizerInstanceRef.current) {
      try {
        rowVirtualizerInstanceRef.current.scrollToIndex(0);
        updatePageIndex(1);
      } catch (e) {
        console.error(e);
      }
    }
  }, [filter.sort, filter.selectors, updatePageIndex]);

  //a check on mount to see if the table is already scrolled to the bottom and immediately needs to fetch more data
  useEffect(() => {
    fetchMoreOnBottomReached(tableContainerRef.current);
  }, [fetchMoreOnBottomReached]);

  useEffect(() => {
    if (rowVirtualizerInstanceRef.current) {
      const totalSize = rowVirtualizerInstanceRef.current.getTotalSize() || 0;
      setVirtualizerHeight(totalSize);
    }
  }, [totalFetched]);

  const handleTableSearch = useCallback(
    async (value: string) => {
      const searchableFields = tableFields
        .filter(field => !UNFILTERABLE_FIELD_TYPES.includes(field.type ?? ''))
        .map(field => field.id);
      const matchedFields = await searchData(value, searchableFields, { updateData: true });
      setSearchMatchedFields(matchedFields);
    },
    [searchData, tableFields]
  );

  if (!showTable) return null;

  return (
    <Box
      id={TABLE_WRAPPER_ID}
      ref={tableWrapperRef}
      className={classes.tableWrapper}
      data-table-empty={isEmpty || undefined}
    >
      <LoadingOverlay
        visible={
          !isCurrentTableClearing && (isTableLoading || isViewInitialLoading || isMinimongoLoading)
        }
        className={classes.loadingOverlay}
      />

      <TableDataClearOverlay
        parentId={TABLE_WRAPPER_ID}
        isShow={isCurrentTableClearing}
        closeOverlay={handleClearTableOverlay}
        className={classes.loadingOverlay}
      />

      <DecaTable
        data={renderData}
        dataCount={totalDataCount}
        views={displayViews}
        selectedView={selectedDisplayView}
        isFull={false}
        fields={FIELD_TYPES}
        showSelectColumn={columns.length > 0}
        showAddColumn={!isEmpty}
        addingNewRow={handleAddNewRow}
        editFieldForm={renderEditColumnField}
        addColumnFormButton={renderAddNewField}
        mantineTableContainerProps={{
          ref: tableContainerRef,
          onScroll: (
            event: UIEvent<HTMLDivElement> //add an event listener to the table container element
          ) => fetchMoreOnBottomReached(event.target as HTMLDivElement),
        }}
        rowContextMenuProps={{
          menuItemComponents: renderRowContextMenu,
        }}
        headerContextMenuProps={{
          menuItemComponents: renderHeaderContextMenu,
        }}
        toolbarProps={{
          leftToolbarComponents: (
            <>
              <TableMenuViewToolbarItem i18nMessages={{ tableView: t('table') }} />
              <SelectViewToolbarItem
                enableLockView={false}
                hideClearView
                leftIcon={<IconTable size={20} className={classes.tableViewIcon} />}
              />
              <CustomFieldsToolbarItem
                minFieldsToCustomize={0}
                i18nMessages={{
                  deleteTitle: t('headerContextMenu.deleteColumnWarningTitle'),
                  deleteMessage: (field: string) =>
                    t('headerContextMenu.deleteColumnWarningMessage', {
                      field,
                    }),
                  deleteConfirmText: t('headerContextMenu.deleteColumnConfirmText'),
                }}
              />
              <TableFilterToolbarItem
                opened={openedFilterMenu}
                initialFilters={filterValue}
                unfilterableFieldTypes={UNFILTERABLE_FIELD_TYPES}
                onOpenChange={opened => {
                  if (opened) {
                    openFilterMenu();
                  } else {
                    closeFilterMenu();
                    setFilterValue(undefined);
                  }
                }}
              />
              <TableSortToolbarItem
                opened={openedSortMenu}
                getSortOrderOptions={getSortOrderOptions}
                unsortableFieldTypes={UNSORTABLE_FIELD_TYPES}
                onOpenChange={opened => {
                  if (opened) {
                    openSortMenu();
                  } else {
                    closeSortMenu();
                  }
                }}
              />
              <TableHeightToolbarItem />
            </>
          ),
          rightToolbarComponents: (
            <>
              <TableAddRow onAddNewRow={handleAddNewRow} disabled={isRecordMutating} />
              <Divider orientation='vertical' h={rem(20)} my='auto' bg='decaLight.2' />
              <TableSearch onSearch={handleTableSearch} searchResultCount={searchResultCount} />
              <Divider orientation='vertical' h={rem(20)} my='auto' bg='decaLight.2' />
              <TableActionMenu />
            </>
          ),
          onToolbarViewChange: handleViewChange,
        }}
        onViewDuplicate={handleDuplicateView}
        onCellUpdate={handleUpdateRecord}
        enableRowVirtualization={true}
        enableColumnVirtualization={true}
        enablePagination={false}
        enableColumnOrdering={true}
        onColumnOrderChange={handleColumnOrderChange}
        enableColumnResizing={true}
        columnResizeMode='onEnd'
        onColumnSizingChange={handleColumnSizingChange}
        onRowSelectionChange={setRowSelection}
        rowVirtualizerInstanceRef={rowVirtualizerInstanceRef}
        rowVirtualizerOptions={{
          overscan: 5,
          estimateSize: getRowEstimateSize,
        }}
        columnVirtualizerOptions={{
          overscan: 5,
          estimateSize: getColumnEstimateSize,
        }}
        state={{
          columnOrder,
          columnSizing: tableColumnSizing,
          rowSelection,
          columnPinning,
        }}
        onCopy={handleCopy}
        primaryFieldId={primaryFieldId}
        searchMatchedFields={searchMatchedFields}
      />

      {isEmpty ? (
        <EmptyState
          onAddColumn={() => {
            setIsShowEmptyState(false);
            toggleFormColumn();
          }}
        />
      ) : null}

      {columns.length > 0 && !isEmpty && (
        <FixedStatusBar
          onNavigationTop={handleGoToTop}
          onNavigationBottom={handleGoToBottom}
          enableBottomNavigation={enableBottomNavigation}
          scrollPosition={scrollPosition}
          rowIds={rowIds}
          setRowSelection={setRowSelection}
          onRowsDelete={handleDeleteRowSelection}
          activeTable={activeTable}
          total={totalDataCount}
          totalItemsPerTable={recordsPagination?.totalItemsPerTable}
        />
      )}

      <TableTrashViewModal opened={tableTrashViewModalOpened} onClose={closeTableTrashViewModal} />

      <PrimaryFieldModal
        baseId={baseId}
        tableId={tableId}
        opened={primaryFieldModalOpened}
        onCancel={closePrimaryFieldModal}
        onSubmit={handlePrimaryFieldModalSubmit}
      />
    </Box>
  );
};

export default TableView;
