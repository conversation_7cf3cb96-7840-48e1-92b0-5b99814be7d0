import { alpha, rem, Text } from '@mantine/core';
import { Center } from '@mantine/core';
import { LoaderSpinner } from '../Common';
import { useTranslate } from '@tolgee/react';
import { createStyles } from '@mantine/emotion';
import { useCallback, useEffect } from 'react';
import { IWebsocketResponse } from '@/types';
import { createCustomEventListener } from '@resola-ai/utils';
import { TABLES_WEBSOCKET_EVENTS } from '@/constants/event';
import { useWindowEvent } from '@mantine/hooks';

const useStyles = createStyles(theme => ({
  root: {
    position: 'absolute',
    inset: 0,
    backgroundColor: alpha(theme.black, 0.5),
    zIndex: '150',
    gap: rem(40),
    flexDirection: 'column',
    userSelect: 'none',
  },
}));

type TableDataClearOverlayProps = {
  isShow: boolean;
  closeOverlay: () => void;
  className?: string;
  parentId?: string;
};

const TableDataClearOverlay = (props: TableDataClearOverlayProps) => {
  const { isShow, closeOverlay, className, parentId } = props;
  const { classes, cx } = useStyles();
  const { t } = useTranslate('table');

  const dataClearHandler = useCallback(
    async (event: CustomEvent<IWebsocketResponse<{ isCleaned?: boolean }>>) => {
      const isCleaned = !!event.detail.data?.isCleaned;

      if (isCleaned) {
        closeOverlay();
      }
    },
    [closeOverlay]
  );

  const handleKeyDown = useCallback(
    (e: KeyboardEvent) => {
      if (isShow) {
        const activeElement = document.activeElement;
        if (parentId && activeElement?.closest(`#${parentId}`)) {
          e.preventDefault();
          e.stopPropagation();
        }
      }
    },
    [isShow, parentId]
  );

  useWindowEvent('keydown', handleKeyDown, { capture: true });

  useEffect(() => {
    const unregisterDataClearHandler = createCustomEventListener(
      TABLES_WEBSOCKET_EVENTS.DATA_CLEAR,
      dataClearHandler
    );

    return () => {
      unregisterDataClearHandler();
    };
  }, [dataClearHandler]);

  if (!isShow) return null;

  return (
    <Center className={cx(classes.root, className)}>
      <LoaderSpinner />
      <Text size={rem(20)} fw='500' c='white'>
        {t('dataClearing')}
      </Text>
    </Center>
  );
};

export default TableDataClearOverlay;
