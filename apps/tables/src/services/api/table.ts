import { ISuccessListPaginationResponse, Table } from '@/types';
import { ISuccessResponse } from '@resola-ai/models';
import { axiosService, logger } from '@resola-ai/services-shared';
import { DEFAULT_TABLE_LIMIT, TABLE_API_META_PATH } from '@/constants/api';
import { SUCCESS_CODES } from '@/constants/common';
import { Err, Ok } from 'ts-results-es';

export type TableAPICreatePayload = Required<Pick<Table, 'name' | 'fields'>> &
  Partial<Omit<Table, 'id' | 'name' | 'fields'>> & {
    duplicateFromId?: string;
    duplicateRecord?: boolean;
  };
export type TableAPIUpdatePayload = Required<Pick<Table, 'fields'>> &
  Partial<Omit<Table, 'fields'>>;

export type TableAPIGetListPayload = {
  deleted?: boolean;
  limit?: number;
  page: number;
};

export const TableAPI = {
  getList: async (
    baseId: string,
    params: TableAPIGetListPayload = {
      deleted: false,
      page: 1,
      limit: DEFAULT_TABLE_LIMIT,
    }
  ) => {
    try {
      const response = await axiosService.instance.get<ISuccessListPaginationResponse<Table>>(
        `${TABLE_API_META_PATH}/${baseId}/tables`,
        {
          params,
        }
      );
      return Ok(response.data);
    } catch (error: any) {
      logger.error(error);
      const response = error.response?.data;
      return Err(response?.error?.message);
    }
  },
  get: async (baseId: string, tableId: string) => {
    try {
      const response = await axiosService.instance.get<ISuccessResponse<Table>>(
        `${TABLE_API_META_PATH}/${baseId}/tables/${tableId}`
      );
      return Ok(response.data);
    } catch (error: any) {
      logger.error(error);
      const response = error.response?.data;
      return Err(response?.error?.message);
    }
  },
  create: async (baseId: string, payload?: TableAPICreatePayload) => {
    try {
      const response = await axiosService.instance.post<ISuccessResponse<Table>>(
        `${TABLE_API_META_PATH}/${baseId}/tables`,
        payload
      );
      return Ok(response.data);
    } catch (error: any) {
      logger.error(error);
      const response = error.response?.data;
      return Err(response?.error?.message);
    }
  },
  update: async (baseId: string, tableId: string, payload?: TableAPIUpdatePayload) => {
    try {
      const response = await axiosService.instance.put<ISuccessResponse<Table>>(
        `${TABLE_API_META_PATH}/${baseId}/tables/${tableId}`,
        payload
      );
      return Ok(response.data);
    } catch (error: any) {
      logger.error(error);
      const response = error.response?.data;
      return Err(response?.error?.message);
    }
  },
  remove: async (baseId: string, tableId: string) => {
    try {
      const response = await axiosService.instance.delete<ISuccessResponse<Table>>(
        `${TABLE_API_META_PATH}/${baseId}/tables/${tableId}`
      );
      if (response.status === SUCCESS_CODES.NO_CONTENT) {
        return Ok(response.data);
      }
      return Err(undefined);
    } catch (error: any) {
      logger.error(error);
      const response = error.response?.data;
      return Err(response?.error?.message);
    }
  },
  getDeletedList: async (baseId: string, deleted?: boolean) => {
    try {
      const queryParam = deleted !== undefined ? `?deleted=${deleted}` : '';
      const response = await axiosService.instance.get<ISuccessListPaginationResponse<Table>>(
        `${TABLE_API_META_PATH}/${baseId}/tables${queryParam}`
      );
      return Ok(response.data);
    } catch (error: any) {
      logger.error(error);
      const response = error.response?.data;
      return Err(response?.error?.message);
    }
  },
  restore: async (tableId: string) => {
    try {
      const response = await axiosService.instance.patch<ISuccessResponse<Table>>(
        `${TABLE_API_META_PATH}/trash/${tableId}`,
        {
          status: 'restored',
        }
      );
      return Ok(response.data);
    } catch (error: any) {
      logger.error(error);
      const response = error.response?.data;
      return Err(response?.error?.message);
    }
  },
};
