import {
  CreatedAtFieldOptions,
  CurrencyFieldOptions,
  CurrencyFormatOption,
  DateFormatOption,
  DatetimeFieldOptions,
  FieldType,
  LongtextFieldOptions,
  NumberFieldOptions,
  NumberFormatOption,
  PercentageFieldOptions,
  PhoneFieldOptions,
  TimeFormatOption,
  UpdatedAtFieldOptions,
} from '@/types';
import {
  TableAutonumberField,
  TableCheckboxField,
  TableCreatedByField,
  TableCreatedTimeField,
  TableCurrencyField,
  TableDateTimeField,
  TableEmailField,
  TableImageField,
  TableLongTextField,
  TableModifiedByField,
  TableModifiedTimeField,
  TableMultipleSelectField,
  TableNumberField,
  TablePercentField,
  TablePhoneField,
  TableSingleSelectField,
  TableTextField,
  TableURLField,
} from '@resola-ai/ui/components';
import { CUSTOM_SELECT_COL_ID, NEWCOL_ID } from '@resola-ai/ui/components/DecaTable/constants';

export const FIELD_TYPES = {
  [FieldType.TEXT]: TableTextField,
  [FieldType.LONGTEXT]: TableLongTextField,
  [FieldType.EMAIL]: TableEmailField,
  [FieldType.PHONE]: TablePhoneField,
  [FieldType.URL]: TableURLField,
  [FieldType.SELECT]: TableSingleSelectField,
  [FieldType.MULTISELECT]: TableMultipleSelectField,
  [FieldType.DATETIME]: TableDateTimeField,
  [FieldType.CREATEDAT]: TableCreatedTimeField,
  [FieldType.UPDATEDAT]: TableModifiedTimeField,
  [FieldType.IMAGE]: TableImageField,
  [FieldType.NUMBER]: TableNumberField,
  [FieldType.CHECKBOX]: TableCheckboxField,
  [FieldType.CURRENCY]: TableCurrencyField,
  [FieldType.AUTOID]: TableAutonumberField,
  [FieldType.PERCENTAGE]: TablePercentField,
  [FieldType.CREATEDBY]: TableCreatedByField,
  [FieldType.UPDATEDBY]: TableModifiedByField,
  // @TODO: Not implemented yet
  // [FieldType.RELATIONSHIP]: TableRelationshipField,
  // [FieldType.LOOKUP]: TableLookUpField,
} as const;

export const UNEDIT_FIELDS = [
  FieldType.CREATEDAT,
  FieldType.UPDATEDAT,
  FieldType.CREATEDBY,
  FieldType.UPDATEDBY,
  FieldType.AUTOID,
] as const;

export const FIELD_OPTIONS_DEFAULT_VALUES = {
  [FieldType.LONGTEXT]: { richtext: { enabled: true } } as LongtextFieldOptions,
  [FieldType.PHONE]: { phoneFormat: { enabled: true, format: '' } } as PhoneFieldOptions,
  [FieldType.DATETIME]: {
    date: {
      format: DateFormatOption.JAPAN,
    },
    time: {
      enabled: false,
      format: TimeFormatOption.HIDDEN,
    },
    timezone: {
      enabled: false,
    },
    displayTimezone: false,
  } as DatetimeFieldOptions,
  [FieldType.CREATEDAT]: {
    date: {
      format: DateFormatOption.JAPAN,
    },
    time: {
      enabled: false,
      format: TimeFormatOption.HIDDEN,
    },
    timezone: {
      enabled: false,
    },
    displayTimezone: false,
  } as CreatedAtFieldOptions,
  [FieldType.UPDATEDAT]: {
    date: {
      format: DateFormatOption.JAPAN,
    },
    time: {
      enabled: false,
      format: TimeFormatOption.HIDDEN,
    },
    timezone: {
      enabled: false,
    },
    displayTimezone: false,
  } as UpdatedAtFieldOptions,
  [FieldType.NUMBER]: {
    numberFormat: NumberFormatOption.INTEGER,
    allowNegative: false,
    decimalPlaces: 0,
  } as NumberFieldOptions,
  [FieldType.CURRENCY]: {
    format: CurrencyFormatOption.JP,
    customSymbol: undefined,
  } as CurrencyFieldOptions,
  [FieldType.PERCENTAGE]: {
    decimalPlaces: 0,
    presentation: undefined,
  } as PercentageFieldOptions,
  [FieldType.TEXT]: null,
  [FieldType.EMAIL]: null,
  [FieldType.URL]: null,
  [FieldType.SELECT]: null,
  [FieldType.MULTISELECT]: null,
  [FieldType.CHECKBOX]: null,
  [FieldType.IMAGE]: null,
  [FieldType.AUTOID]: null,
  [FieldType.CREATEDBY]: null,
  [FieldType.UPDATEDBY]: null,
  [FieldType.RELATIONSHIP]: null,
  [FieldType.LOOKUP]: null,
} as const;

export const UNEDIT_FIELD_TYPES = [
  FieldType.CREATEDAT,
  FieldType.UPDATEDAT,
  FieldType.CREATEDBY,
  FieldType.UPDATEDBY,
  FieldType.AUTOID,
] as const;

export const UNSORTABLE_FIELD_TYPES = [FieldType.IMAGE, FieldType.AUTOID];
export const UNFILTERABLE_FIELD_TYPES = [FieldType.IMAGE];

export const SUPPORTED_PRIMARY_FIELD_TYPES = [
  FieldType.TEXT,
  FieldType.DATETIME,
  FieldType.PHONE,
  FieldType.EMAIL,
  FieldType.URL,
  FieldType.NUMBER,
  FieldType.AUTOID,
];

export const LEFT_UNORDERED_COLUMN_IDS = [CUSTOM_SELECT_COL_ID];
export const RIGHT_UNORDERED_COLUMN_IDS = [NEWCOL_ID];

export const MIN_COLUMN_WIDTH = 120;
export const MAX_COLUMN_WIDTH = 400;
export const DEFAULT_COLUMN_WIDTH = 200;
export const DEFAULT_ROW_HEIGHT = 40;
export const SCROLL_BOTTOM_THRESHOLD = 480;
export const BOTTOM_POSITION = 24;

export const TEMP_RECORD_ID_PREFIX = 'temp-row-';
export const MAX_ROW_INSERT_LIMIT = 5;
export const MAX_IMAGE_FILE_SIZE = 5 * 1024 * 1024; // 5MB in bytes

export const TABLE_WRAPPER_ID = 'app-deca-table-wrapper';
