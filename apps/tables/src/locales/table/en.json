{"addColumn": "Add a column", "dataClearing": "Clearing data, please wait a few moments...", "empty": "The Table is empty.\nPlease add column to get started", "error": {"fileSizeExceeded": "File size exceeded"}, "headerContextMenu": {"changePrimaryField": "Change primary field", "delete": "Delete column", "deleteColumnConfirmText": "Delete Field", "deleteColumnWarningMessage": "Are you sure you want to delete \"{field}\" field ?", "deleteColumnWarningTitle": "Delete Field", "disabledPrimaryFieldTooltip": "This is a primary field and cannot be hidden or deleted", "duplicateField": "Duplicate field", "editField": "Edit field", "filterBy": "Filter by", "freezeField": "Freeze up to this field", "hideField": "Hide field", "insertLeft": "<PERSON><PERSON><PERSON> left", "insertRight": "Insert right", "setPrimaryField": "Set as primary field", "sortFirst": "Sort first to last", "sortLast": "Sort last to first", "unsupportedPrimaryFieldTooltip": "Primary fields cannot be set for this type."}, "newRecordAdded": "New record added.", "primaryFieldModal": {"cancelBtn": "Cancel", "selectLabel": "Primary Field", "selectPlaceholder": "Please select the field", "submitBtn": "Confirm", "title": "Change primary field"}, "rowContextMenu": {"delete": "Delete", "deleteRowsWarningMessage": "Are you sure you want to delete these rows?", "deleteRowsWarningTitle": "Delete rows", "deleteRowWarningMessage": "Are you sure you want to delete this row?", "deleteRowWarningTitle": "Delete row", "deselect": "Deselect row", "duplicate": "Duplicate", "insertAbove": "Insert <input /> above", "insertBelow": "Insert <input /> below", "select": "Select row"}, "sort": {"sortAsc": "Ascending", "sortAscCheckbox": "Unchecked First", "sortAscDate": "Earliest to Latest", "sortAscNumber": "0 to 9", "sortAscSelect": "First to Last", "sortAscText": "A to Z", "sortDesc": "Descending", "sortDescCheckbox": "Checked First", "sortDescDate": "Latest to Earliest", "sortDescNumber": "9 to 0", "sortDescSelect": "Last to First", "sortDescText": "Z to A"}, "statusBar": {"addRecord": "Add Record", "overLimitRecordsDays": "Over limits - {days} days left", "reachRecordLimitTooltip": "The record limit has been reached. Excess records will be deleted in {days} days.", "records": "records", "rowSelectionActions": {"deleteRow": "Delete row", "deleteRowCancel": "Cancel", "deleteRowConfirm": "Confirm", "deleteRowsWarningMessage": "Are you sure you want to delete these rows?", "deleteRowsWarningTitle": "Delete rows", "deleteRowWarningMessage": "Are you sure you want to delete this row?", "deleteRowWarningTitle": "Delete row", "deselect": "Deselect", "recordsSelected": "records selected"}}, "success": {"clearData": "Data for table {tableName} has been cleared", "copy": "Copied to clipboard", "deleteRow": "Row has been deleted", "deleteRows": "Rows have been deleted"}, "table": "Table View", "trashView": {"modalTitle": "Restore deleted tables", "search": "Search"}, "updateView": "Update view."}