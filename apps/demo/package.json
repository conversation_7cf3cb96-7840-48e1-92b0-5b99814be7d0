{"name": "demo", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "tsc && vite build", "lint": "biome lint --write .", "format": "biome format --write .", "check": "biome check --write .", "preview": "vite preview", "test": "vitest --run", "test:watch": "vitest", "test:unit": "vitest --run", "coverage": "vitest --run --coverage"}, "dependencies": {"@auth0/auth0-react": "^2.2.1", "@emotion/react": "^11.11.1", "@mantine/core": "7.17.7", "@mantine/hooks": "7.17.7", "@resola-ai/models": "workspace:^", "@resola-ai/services-shared": "workspace:^", "json-beautify": "^1.1.1", "jwt-decode": "^3.1.2", "path": "^0.12.7", "react": "^18.2.0", "react-dom": "^18.2.0", "react-router-dom": "^6.15.0"}, "devDependencies": {"@resola-ai/biome-config": "workspace:*", "@resola-ai/typescript-config": "workspace:*", "@testing-library/jest-dom": "^6.2.0", "@types/react": "^18.2.15", "@types/react-dom": "^18.2.7", "@vitejs/plugin-react": "^4.0.3", "@biomejs/biome": "^1.5.3", "typescript": "5.6.3", "vite": "5.4.19", "vitest": "2.1.9", "@vitest/coverage-v8": "^2.1.9", "jest-environment-jsdom": "^29.7.0"}}