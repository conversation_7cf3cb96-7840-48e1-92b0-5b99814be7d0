import { useEffect, useState } from 'react';
import decaLogo from '/deca.svg';
import viteLogo from '/vite.svg';
import reactLogo from '../assets/react.svg';
import '../App.css';
import { useAuth0 } from '@auth0/auth0-react';
// import { Code } from '@mantine/core';
// import { Prism } from "@mantine/prism";
import { Container, Flex, ScrollArea, Space } from '@mantine/core';
import jwt_decode from 'jwt-decode';
import useOrgRedirect from '../hooks/useOrgRedirect';
import { isAllowedDomains } from '../utils/location';
import LogoutButton from './logout';

// create a custom prism component as mantine v7 does not support it
// @todo: mantine v7 use https://mantine.dev/x/code-highlight/ for it, this is low priority, please do it when you have free time.
const Prism = ({ children, language, scrollAreaComponent }: any) => {
  console.log(language, scrollAreaComponent);
  return <div>{children}</div>;
};

const CustomCodeBlock = ({ input }: any) => {
  if (!input) return null;
  return (
    <Prism language='json' scrollAreaComponent={ScrollArea}>
      {input}
    </Prism>
  );
};

function Application() {
  const { user, getAccessTokenSilently, isAuthenticated, getIdTokenClaims } = useAuth0();
  const [accessToken, setAccessToken] = useState('');
  const [idTokenClaims, setIdTokenClaims] = useState<any>('');

  const getClaimFromAccessToken = (inputAccessToken: string) => {
    if (inputAccessToken === '') {
      return '';
    }
    return jwt_decode(inputAccessToken);
  };

  useEffect(() => {
    setTimeout(() => {
      function removeQueryString(url: any) {
        const urlObj = new URL(url);

        urlObj.search = '';

        return urlObj.toString();
      }
      const url = removeQueryString(window.location.href);
      window.history.replaceState({}, document.title, url);
    }, 2000);
  }, []);

  useEffect(() => {
    if (isAuthenticated) {
      getAccessTokenSilently().then((token) => {
        setAccessToken(token);
      });
      getIdTokenClaims().then((claims) => {
        setIdTokenClaims(claims);
      });
    }
  }, [isAuthenticated]);

  useOrgRedirect();
  if (!user) {
    return null;
  }
  if (isAllowedDomains()) {
    return null;
  }
  const customIdTokenClaims = {
    ...idTokenClaims,
    __raw: undefined,
  };
  const idToken = idTokenClaims.__raw;
  return (
    <>
      <div>
        <a href='https://vitejs.dev' target='_blank' rel='noreferrer'>
          <img src={viteLogo} className='logo' alt='Vite logo' />
        </a>
        <a href='https://react.dev' target='_blank' rel='noreferrer'>
          <img src={reactLogo} className='logo react' alt='React logo' />
        </a>
        <a href={window.location.origin} target='_self'>
          <img src={decaLogo} className='logo' alt='Deca logo' />
        </a>
      </div>
      <img src={user.picture} alt={user.name} />
      <h1>Demo app </h1>
      <LogoutButton />
      <Space h={'lg'} />
      <Container fluid>
        <Flex gap={'lg'}>
          <Container
            fluid
            maw={'500px'}
            bg='dark'
            sx={{
              color: 'white',
              borderRadius: '10px',
            }}
          >
            <div className='card'>
              <h4>Id Token</h4>
              <CustomCodeBlock input={idToken} />
            </div>
            <div className='card'>
              <h4>Id Token Payload</h4>
              <CustomCodeBlock input={JSON.stringify(customIdTokenClaims, null, 2)} />
            </div>
          </Container>
          <Container
            fluid
            maw={'500px'}
            bg='teal'
            sx={{
              color: 'white',
              borderRadius: '10px',
            }}
          >
            <div className='card'>
              <h4>Access Token</h4>
              <CustomCodeBlock input={accessToken} />
            </div>
            <div className='card'>
              <h4>Access Token Payload</h4>
              <CustomCodeBlock
                input={JSON.stringify(getClaimFromAccessToken(accessToken), null, 2) as any}
              />
            </div>
          </Container>
        </Flex>
      </Container>
    </>
  );
}

export default Application;
