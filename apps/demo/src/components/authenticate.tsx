import { useAuth0 } from '@auth0/auth0-react';
import type React from 'react';
import NotAuthenticate from './notAuthenticated';

const ErrorComponent = ({ statusCode, title }: any) => {
  return (
    <div className='flex flex-col items-center justify-center h-screen'>
      <h1 className='text-2xl font-bold text-gray-700'>{statusCode}</h1>
      <p className='text-gray-500'>{title}</p>
    </div>
  );
};

const AuthenticateLayer: React.FC<any> = ({ children }) => {
  const { error, isAuthenticated, isLoading } = useAuth0();
  if (isLoading) {
    return <div>...</div>;
  }

  if (error) {
    console.log('error', error);
    return <ErrorComponent statusCode={500} title={error.message} />;
  }

  if (!isAuthenticated) {
    return <NotAuthenticate />;
  }

  return <>{children}</>;
};

export default AuthenticateLayer;
