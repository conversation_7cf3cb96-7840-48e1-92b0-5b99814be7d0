import { useAuth0 } from '@auth0/auth0-react';
import { Button } from '@mantine/core';
import AppConfig from '../configs';

const LogoutButton = () => {
  const { logout } = useAuth0();
  const getReturnUrl = () => {
    if (typeof window === 'undefined') {
      return '';
    }
    if (
      AppConfig.EXCLUDED_BASE_PATH_DOMAINS.filter((domain) =>
        window.location.origin.includes(domain)
      ).length > 0
    ) {
      return `${window.location.origin}`;
    }
    if (
      AppConfig.INCLUDED_BASE_PATH_DOMAINS.filter((domain) =>
        window.location.origin.includes(domain)
      ).length > 0
    ) {
      return `${window.location.origin}${AppConfig.BASE_PATH}`;
    }
    return `${window.location.origin}${AppConfig.BASE_PATH}`;
  };
  const logoutReturnTo = getReturnUrl();
  console.log('debug logoutReturnTo', logoutReturnTo);
  return (
    <Button color={'red'} onClick={() => logout({ logoutParams: { returnTo: logoutReturnTo } })}>
      Log Out
    </Button>
  );
};

export default LogoutButton;
