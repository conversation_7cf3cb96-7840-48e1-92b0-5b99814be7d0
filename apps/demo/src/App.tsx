import './App.css';

import { Auth0Provider } from '@auth0/auth0-react';
import { MantineProvider } from '@mantine/core';
import Application from './components/Application';
import AppConfig from './configs';
import { getOrganizationName } from './utils/organization';

export const AppContainer = ({ children }: any) => {
  const organizationName = getOrganizationName();
  console.log('organizationName', organizationName);
  const onRedirectCallback = (appState: any) => {
    console.log('debug onRedirectCallback', window.location.origin, appState);
    if (window.location.origin.includes('amplifyapp.com')) {
      window.location.href = appState?.returnTo || '/';
      return;
    }
    if (
      AppConfig.INCLUDED_BASE_PATH_DOMAINS.filter((domain) =>
        window.location.origin.includes(domain)
      ).length > 0
    ) {
      return `${window.location.origin}${AppConfig.BASE_PATH}`;
    }
    // Router.replace(appState?.returnTo || "/");
    window.location.href = appState?.returnTo || '/';
  };
  const getRedirectUri = () => {
    console.log('debug getRedirectUri', window.location.origin);
    if (typeof window === 'undefined') {
      return '';
    }

    if (
      AppConfig.EXCLUDED_BASE_PATH_DOMAINS.filter((domain) =>
        window.location.origin.includes(domain)
      ).length > 0
    ) {
      return `${window.location.origin}`;
    }
    if (
      AppConfig.INCLUDED_BASE_PATH_DOMAINS.filter((domain) =>
        window.location.origin.includes(domain)
      ).length > 0
    ) {
      return `${window.location.origin}${AppConfig.BASE_PATH}`;
    }
    return `${window.location.origin}`;
  };
  return (
    <Auth0Provider
      domain={AppConfig.AUTH0.DOMAIN}
      clientId={AppConfig.AUTH0.CLIENT_ID}
      onRedirectCallback={onRedirectCallback}
      authorizationParams={{
        redirect_uri: getRedirectUri(),
        audience: AppConfig.AUTH0.AUDIENCE,
        scope: AppConfig.AUTH0.SCOPE,
        organization: organizationName,
      }}
    >
      {children}
    </Auth0Provider>
  );
};

function App() {
  return (
    <MantineProvider withGlobalClasses withCssVariables>
      <Application />
    </MantineProvider>
  );
}

export default App;
