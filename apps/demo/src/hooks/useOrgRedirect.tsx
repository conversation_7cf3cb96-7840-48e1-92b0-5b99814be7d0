import { useAuth0 } from '@auth0/auth0-react';
import { useEffect } from 'react';
import AppConfig from '../configs';
import { isAllowedDomains } from '../utils/location';
import { getOrganizationName } from '../utils/organization';

const useOrgRedirect = () => {
  const { user } = useAuth0();
  useEffect(() => {
    if (user && typeof window !== 'undefined') {
      const currentOrganizationName = getOrganizationName();
      const wantedOrganizationName = user.org_name;
      const isAllowed = isAllowedDomains();
      console.log('isAllow', isAllowed);
      console.log(
        wantedOrganizationName !== undefined &&
          currentOrganizationName !== wantedOrganizationName &&
          isAllowed
      );
      if (
        wantedOrganizationName !== undefined &&
        currentOrganizationName !== wantedOrganizationName &&
        isAllowed
      ) {
        const allowedDomain = AppConfig.INCLUDED_BASE_PATH_DOMAINS.find((domain) =>
          window.location.origin.includes(domain)
        );
        if (allowedDomain) {
          window.location.href = `https://${wantedOrganizationName}.${allowedDomain}${window.location.pathname}`;
        }
      }
    }
  }, [user]);
};

export default useOrgRedirect;
