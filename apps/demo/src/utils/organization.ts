import AppConfig from '../configs';

export const getOrganizationName = () => {
  if (typeof window === 'undefined') {
    return AppConfig.DEFAULT_ORGANIZATION_NAME;
  }
  if (window.location.origin.includes('localhost')) {
    return AppConfig.DEFAULT_ORGANIZATION_NAME;
  }
  try {
    if (
      AppConfig.EXCLUDED_BASE_PATH_DOMAINS.filter((domain) =>
        window.location.origin.includes(domain)
      ).length > 0
    ) {
      return AppConfig.DEFAULT_ORGANIZATION_NAME;
    }
    const orgName = window.location.origin.split('.')[0].split('//')[1];
    if (orgName === 'deca-dev') {
      return undefined;
    }
    return orgName;
  } catch (error) {
    return AppConfig.DEFAULT_ORGANIZATION_NAME;
  }
};
