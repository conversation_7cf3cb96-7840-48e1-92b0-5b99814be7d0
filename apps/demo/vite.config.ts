import path from 'path';
import react from '@vitejs/plugin-react';
import { defineConfig, loadEnv } from 'vite';
// https://vitejs.dev/config/
export default defineConfig(({ command, mode }) => {
  const env = loadEnv(mode, process.cwd(), '');
  const CDN_URL = env.VITE_CDN_PREFIX;
  const BASE_PATH = env.VITE_BASE_PATH;
  return {
    plugins: [react()],
    base: BASE_PATH,
    experimental: {
      renderBuiltUrl(
        filename: string,
        {
          hostId,
          hostType,
          type,
        }: { hostId: string; hostType: 'js' | 'css' | 'html'; type: 'public' | 'asset' }
      ) {
        if (type === 'public') {
          return CDN_URL + filename;
        }
        if (path.extname(hostId) === '.js') {
          return CDN_URL + filename;
        }
        return CDN_URL + filename;
      },
    },
    server: {
      port: '3001',
    },
    test: {
      globals: true,
      environment: 'jsdom',
      setupFiles: './setupTest.js',
      coverage: {
        enabled: true,
        provider: 'v8',
        reporter: ['text', 'lcov', 'json'],
        reportsDirectory: './coverage',
        exclude: [
          'coverage/**',
          'dist/**',
          '**/*.d.ts',
          'test/**',
          '**/*.test.ts',
          '**/*.test.tsx',
          '**/*.spec.ts',
          '**/*.spec.tsx',
          '**/vite.config.*',
          '**/vitest.config.*',
          '**/jest.config.*',
          '**/setupTest.*',
        ],
      },
    },
  };
});
