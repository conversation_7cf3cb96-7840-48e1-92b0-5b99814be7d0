import { memo, useCallback } from 'react';
import { Box, Group, Text } from '@mantine/core';
import { IconDownload, IconReload } from '@tabler/icons-react';
import { Job } from '@/types';
import JobStatusBadge from '@/components/KBJobs/JobStatus';
import { useJobDetailContext } from '@/contexts/JobDetailContext';
import { useKbAccessControl } from '@/hooks';
import { useJobSummaryCommon, useJobRetry } from '../hooks';

interface ExportJobSummaryProps {
  job: Job;
}

/**
 * ExportJobSummary component displays summary information for article export jobs
 * Shows creator, status, and download options
 */
const ExportJobSummary = ({ job }: ExportJobSummaryProps) => {
  const { classes, t, renderSummaryRow, renderCreator, handleDownload, handleRetryJob } =
    useJobSummaryCommon({
      rowLabelWidth: 180,
    });
  const { jobResult } = useJobDetailContext();
  const { permJob } = useKbAccessControl();
  const { shouldShowRetry } = useJobRetry({ job, canRetry: permJob.canRetry });

  /**
   * Handles the download by opening a new tab with the downloadUrl from job result
   */
  const handleDownloadResult = useCallback(() => {
    if (jobResult?.downloadUrl) {
      handleDownload(jobResult.downloadUrl);
    }
  }, [jobResult, handleDownload]);

  /**
   * Retries a failed job using the shared handler
   */
  const handleRetryJobClick = useCallback(async () => {
    if (job.id) {
      await handleRetryJob(job.id);
    }
  }, [handleRetryJob, job.id]);

  return (
    <Box className={classes.root}>
      {renderSummaryRow('creator', renderCreator(job))}

      {renderSummaryRow(
        'status',
        <Group gap='md'>
          <JobStatusBadge status={job.status} />
          {shouldShowRetry && (
            <Text className={classes.retryLink} onClick={handleRetryJobClick}>
              <IconReload size={14} />
              {t('actions.retry', { ns: 'common' })}
            </Text>
          )}
        </Group>
      )}

      {renderSummaryRow(
        'downloadExport',
        jobResult?.downloadUrl ? (
          <a
            href={jobResult.downloadUrl}
            className={classes.resultLink}
            target='_blank'
            rel='noopener noreferrer'
          >
            <Group gap='xs'>
              <IconDownload size={16} />
              <Text>{t('jobSummary.exportResult')}</Text>
            </Group>
          </a>
        ) : (
          <Box className={classes.emptyResult} onClick={handleDownloadResult}>
            <Group gap='xs'>
              <Text>{t('jobSummary.noExportResultFound')}</Text>
            </Group>
          </Box>
        )
      )}
    </Box>
  );
};

export default memo(ExportJobSummary);
