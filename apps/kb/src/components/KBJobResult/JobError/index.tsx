import { Box, Flex, rem, Text } from '@mantine/core';
import { IconAlertTriangle, IconReload, IconTrash } from '@tabler/icons-react';
import { useTranslate } from '@tolgee/react';
import { DecaButton } from '@resola-ai/ui';
import { createStyles } from '@mantine/emotion';
import { Job } from '@/types/job';
import { useKbAccessControl } from '@/hooks';

const useStyles = createStyles(theme => ({
  wrapper: {
    display: 'flex',
    flexDirection: 'column',
    alignItems: 'center',
    justifyContent: 'center',
    padding: `${rem(80)} ${theme.spacing.xl}`,
    backgroundColor: theme.colors.decaLight[0],
    border: `${rem(1)} solid ${theme.colors.decaLight[2]}`,
    gap: theme.spacing.sm,
    borderRadius: rem(10),
  },
  title: {
    color: theme.colors.decaNavy[5],
    fontWeight: 700,
    fontSize: rem(24),
    marginTop: rem(16),
    marginBottom: 0,
  },
  message: {
    color: theme.colors.decaNavy[5],
    fontWeight: 500,
    fontSize: rem(20),
    marginBottom: 0,
  },
  icon: {
    color: theme.colors.decaRed[5],
    width: rem(36),
    height: rem(36),
    strokeWidth: rem(2),
  },
  buttonIcon: {
    width: rem(16),
    height: rem(16),
  },
  buttonGroup: {
    gap: rem(12),
    marginTop: rem(8),
  },
}));

interface JobErrorProps {
  job: Job;
  onRetry?: () => void;
  onDelete?: () => void;
  className?: string;
}

const JobError = ({ job: _job, onRetry, onDelete, className }: JobErrorProps) => {
  const { classes, cx } = useStyles();
  const { t } = useTranslate(['job', 'common']);
  const { permJob } = useKbAccessControl();

  return (
    <Box className={cx(classes.wrapper, className)}>
      <IconAlertTriangle className={classes.icon} />
      <Text className={classes.title}>
        {t(`errors.failed.title`, { ns: 'job' }) || t('errors.defaultTitle', { ns: 'job' })}
      </Text>
      <Text className={classes.message}>
        {t(`errors.failed.message`, { ns: 'job' }) || t('errors.defaultMessage', { ns: 'job' })}
      </Text>
      <Flex className={classes.buttonGroup}>
        {onRetry && permJob.canRetry && (
          <DecaButton
            leftSection={<IconReload className={classes.buttonIcon} />}
            size='sm'
            variant='secondary'
            onClick={onRetry}
          >
            {t('actions.retry', { ns: 'common' })}
          </DecaButton>
        )}
        {onDelete && permJob.canDelete && (
          <DecaButton
            leftSection={<IconTrash className={classes.buttonIcon} />}
            size='sm'
            variant='neutral'
            onClick={onDelete}
          >
            {t('actions.delete', { ns: 'common' })}
          </DecaButton>
        )}
      </Flex>
    </Box>
  );
};

export default JobError;
