import { COMMON_HEIGHT_NODE } from '@/components/FlowBuilder/constants';
import { FlowDiagram } from '@/helpers/flowDiagram';
import { useEffect, useRef, useState } from 'react';
import { type FlowNodes, FlowNodeType } from '@/models/flow';
import { type Edge, type Node, useEdgesState, useNodesState } from '@xyflow/react';

const INITIAL_TRIGGER_NODE = {} as FlowNodes;

const checkIsNodeIdToNumberChanges = (
  prevNodeIdToOrderNumber: Record<string, number>,
  nextNodeIdToOrderNumber: Record<string, number>
) => {
  if (Object.keys(prevNodeIdToOrderNumber).length !== Object.keys(nextNodeIdToOrderNumber).length) {
    return true;
  }
  return Object.keys(prevNodeIdToOrderNumber).some(
    key => prevNodeIdToOrderNumber?.[key] !== nextNodeIdToOrderNumber?.[key]
  );
};

function useFlowNodeAndEdgeGenerator(triggers?: FlowNodes, nodesConfig?: FlowNodes) {
  const [nodes, setNodes] = useNodesState<Node>([]);
  const [edges, setEdges] = useEdgesState<Edge>([]);
  const [nodeIdToOrderNumber, setNodeIdToOrderNumber] = useState<Record<string, number>>({});
  // a ref to save old triggers and nodesConfig
  const oldTriggersRef = useRef<FlowNodes>({});
  const oldNodesConfigRef = useRef<FlowNodes>({});

  useHideFlowAttribution();

  useEffect(() => {
    const triggerNodes = triggers ?? INITIAL_TRIGGER_NODE;

    // compare old triggers and nodesConfig with new one to check if we need to update the diagram
    // by comparing the number of triggers, nodesConfig
    // by comparing the type, displayName, icon of each node
    if (
      oldTriggersRef.current &&
      oldNodesConfigRef.current &&
      Object.keys(oldTriggersRef.current).length === Object.keys(triggerNodes).length &&
      Object.keys(oldNodesConfigRef.current).length === Object.keys(nodesConfig ?? {}).length
    ) {
      const isSameTrigger = Object.keys(triggerNodes).every(key => {
        const oldTrigger = oldTriggersRef.current?.[key];
        const newTrigger = triggerNodes?.[key];
        return (
          oldTrigger?.name === newTrigger?.name &&
          oldTrigger?.displayName === newTrigger?.displayName &&
          oldTrigger?.icon === newTrigger?.icon
        );
      });
      const isSameNodesConfig = Object.keys(nodesConfig ?? {}).every(key => {
        const oldNode = oldNodesConfigRef.current?.[key];
        const newNode = nodesConfig?.[key];
        return (
          oldNode?.name === newNode?.name &&
          oldNode?.displayName === newNode?.displayName &&
          oldNode?.icon === newNode?.icon
        );
      });
      if (isSameTrigger && isSameNodesConfig) {
        console.log("SAME DATA DISPLAY TO USER WHICH DOESN'T NEED TO RE-CALCULATE THE DIAGRAM");
        return;
      }
    }

    oldTriggersRef.current = triggerNodes;
    oldNodesConfigRef.current = nodesConfig ?? {};

    try {
      const { nodes, edges, loopings, nodeIdToOrderNumber } = FlowDiagram.getNodesAndEdges(
        triggerNodes,
        nodesConfig
      );

      setNodeIdToOrderNumber(pre => {
        if (checkIsNodeIdToNumberChanges(pre, nodeIdToOrderNumber)) {
          return nodeIdToOrderNumber;
        }
        return pre;
      });

      const layoutCalculatedData = async () =>
        await FlowDiagram.getLayoutCalculateElk(nodes, edges, loopings);
      layoutCalculatedData()
        .then(data => {
          if (data) {
            const nodesForDiagram = FlowDiagram.getNodesForDiagram(
              nodes,
              data.nodes
            ) as Node<any>[];
            const edgesForDiagram = FlowDiagram.getEdgesForDiagram(
              edges,
              data.edges
            ) as Edge<any>[];

            if (loopings?.length > 0) {
              // Add some looping frame node to cover looping node and its children
              loopings.forEach(looping => {
                const loopNode = nodesForDiagram.find(node => node.id === looping.loopId);
                const nodeIds = looping.loopSubNodes.map(node => node.id);
                const nodeData = nodesForDiagram.filter(node => nodeIds?.includes(node.id));
                if (!loopNode) return;
                const { top, bottom, left, right } = FlowDiagram.getBoundingRectOfNodes([
                  loopNode,
                  ...nodeData,
                ]);

                // Add a frame to cover looping node and its children
                nodesForDiagram.push({
                  id: FlowDiagram.getCompoundNodeId(looping.loopId),
                  type: FlowNodeType.LoopingFrame,
                  position: { x: left - 10, y: top + COMMON_HEIGHT_NODE / 2 },
                  data: {
                    width: right - left + 20,
                    height: bottom - top,
                  },
                  width: right - left + 20,
                  height: bottom - top + 20,
                  zIndex: -1,
                  draggable: true,
                });
              });
            }
            setNodes(nodesForDiagram);
            setEdges(edgesForDiagram);
          }
        })
        .catch(error => {
          console.log('PARSING ERROR', { error });
        });
    } catch (error) {
      console.log({ error });
    }
  }, [triggers, nodesConfig]);
  return {
    nodes,
    edges,
    nodeIdToOrderNumber,
    setNodes,
  };
}

export const useHideFlowAttribution = () => {
  // Hide React Flow attribution
  useEffect(() => {
    const timeout = setInterval(() => {
      if (window) {
        const element = window.document.querySelector('.react-flow__attribution') as HTMLDivElement;
        if (element) {
          element.style.display = 'none';
        }
      }
    }, 1000);
    return () => clearInterval(timeout);
  }, []);
};

export default useFlowNodeAndEdgeGenerator;
