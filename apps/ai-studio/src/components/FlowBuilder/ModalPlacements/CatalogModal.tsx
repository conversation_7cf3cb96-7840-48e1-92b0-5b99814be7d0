import { forwardRef, useImperativeHandle, useState } from 'react';
import { Modal, rem } from '@mantine/core';
import {
  Catalog,
  CatalogConfig,
  TriggerNodes,
  OtherNormalNodes,
  LoopingNodes,
} from '@resola-ai/ui';

type CatalogModalProps = {
  opened?: boolean;
  onClose: () => void;
  disabledNodes?: string[];
  withCloseButton?: boolean;
  onSelect: (item: any) => void;
};

export type CatalogModalRef = {
  openWithDisabledOptions: (disabledOptions?: DisabledNodesType[]) => void;
};

const CatalogModal = forwardRef<CatalogModalRef, CatalogModalProps>(
  ({ onClose, onSelect, withCloseButton = false }, ref) => {
    const [opened, setOpened] = useState(false);
    const [disabledNodes, setDisabledNodes] = useState<DisabledNodesType[]>([]);

    useImperativeHandle(ref, () => ({
      openWithDisabledOptions: (disabledOptions?: DisabledNodesType[]) => {
        disabledOptions && setDisabledNodes(disabledOptions);
        setOpened(true);
      },
    }));

    const handleClose = () => {
      setOpened(false);
      setDisabledNodes([]);
      onClose();
    };

    const handleOnSelectCatalog = (item: any) => {
      onSelect(item);
      handleClose();
    };

    return (
      <Modal
        centered
        radius='md'
        opened={opened}
        size={rem(489)}
        padding={'1rem'}
        closeOnClickOutside
        onClose={handleClose}
        withCloseButton={withCloseButton}
      >
        <Catalog
          schema={CatalogConfig.schema}
          onSelect={handleOnSelectCatalog}
          disabledNodes={disabledNodes}
        />
      </Modal>
    );
  }
);

CatalogModal.displayName = 'CatalogModal';

export default CatalogModal;

export type DisabledNodesType =
  | 'chatbot'
  | 'code'
  | 'deca-ai-widgets'
  | 'deca-livechat'
  | 'deca-crm'
  | 'deca-kb'
  | 'deca-tables'
  | 'filter'
  | 'gmail'
  | 'google-calendar'
  | 'google-docs'
  | 'google-drive'
  | 'google-sheets'
  | 'google-slides'
  | 'hubspot'
  | 'http'
  | 'loop'
  | 'new-trigger'
  | 'openai'
  | 'pages'
  | 'path'
  | 'schedule'
  | 'slack'
  | 'wait'
  | 'formatter'
  | 'function'
  | 'zoom-meetings';

export { TriggerNodes, LoopingNodes, OtherNormalNodes };
