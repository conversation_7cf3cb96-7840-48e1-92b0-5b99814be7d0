import BuilderCanvas from '@/components/Builder/Canvas/Canvas';
import { EditorResolver } from '@/components/Builder/Editor';
import ScreenSizeSelector from '@/components/Builder/ScreenSizeSelector';
import usePages from '@/hooks/usePages';
import type { ScreenSizeType } from '@/hooks/useScreenSize';
import {
  setBuilderCanvasPreviewMode,
  setHeaderNavigationShowingPreview,
} from '@/store/action/headerNavigation';
import { useAppDispatch, useAppSelector } from '@/store/hooks';
import { Device, type Page } from '@/types';
import { Editor } from '@craftjs/core';
import { Box, Flex, Modal, Select, rem, useMantineTheme } from '@mantine/core';
import { LoadingOverlay } from '@mantine/core';
import { IconArrowLeft } from '@tabler/icons-react';
import { useTranslate } from '@tolgee/react';
import { useEffect, useState } from 'react';
import { useParams } from 'react-router-dom';
import { Label } from '../Label';

type Props = {
  isOpen: boolean;
  onClose: () => void;
  id?: string;
  pageId?: string;
};

export const PreviewModal = ({ isOpen, onClose, id, pageId }: Props) => {
  const { t } = useTranslate('site_settings');
  const theme = useMantineTheme();
  const dispatch = useAppDispatch();
  const { builderPreviewMode } = useAppSelector((state) => state.headerNavigation);
  const [pages, setPages] = useState<Page[]>([]);
  const [selectedPageIndex, setSelectedPageIndex] = useState<number>(0);
  const [customWidth, setCustomWidth] = useState<string>('1200px');

  const { siteId: resourceId } = useParams();
  const { isLoading: isLoadingPages, mutate } = usePages(
    {
      siteId: resourceId as string,
      versionId: id || '',
    },
    {
      onSuccess: (data: Page[]) => {
        setPages(data);
        if (data.length > 0) {
          let index = 0;
          if (pageId) {
            index = data.findIndex((page: Page) => page.id === pageId);
          }
          setSelectedPageIndex(index);
        }
      },
    }
  );

  useEffect(() => {
    if (pageId) {
      mutate();
    }
  }, [pageId]);

  useEffect(() => {
    if (isOpen) {
      mutate();
      dispatch(setHeaderNavigationShowingPreview(true));
    }
  }, [isOpen]);

  const pageOptions = pages.map((page, index) => ({
    value: index.toString(),
    label: page.name || `Page ${index + 1}`,
  }));

  const handleWidthChange = (width: string) => {
    setCustomWidth(width);
    const widthNum = Number.parseInt((width || '1200px').replace('px', ''));
    dispatch(
      setBuilderCanvasPreviewMode(
        widthNum >= 1200 ? Device.Desktop : widthNum >= 768 ? Device.Tablet : Device.Mobile
      )
    );
  };

  const onCloseModal = () => {
    onClose();
    dispatch(setBuilderCanvasPreviewMode(Device.Desktop));
    dispatch(setHeaderNavigationShowingPreview(false));
  };

  return (
    <Modal
      opened={isOpen}
      onClose={onCloseModal}
      withCloseButton={false}
      size='100%'
      styles={{
        body: {
          padding: rem(16),
          paddingTop: 0,
          paddingBottom: 0,
        },
      }}
      fullScreen
    >
      <Box>
        <Flex align='center' gap={rem(16)} justify='space-between' m={rem(16)}>
          <Flex
            onClick={onCloseModal}
            sx={{ cursor: 'pointer' }}
            align='center'
            justify='center'
            gap={rem(8)}
          >
            <IconArrowLeft color={theme.colors.blue[6]} size={20} />
            <Label text={t('backButton')} fw={500} size={rem(16)} mb={0} color='blue.6' />
          </Flex>
          <ScreenSizeSelector
            initialSizeType={builderPreviewMode.toUpperCase() as ScreenSizeType}
            onWidthChange={handleWidthChange}
          />
          <Flex align='center' gap={rem(8)}>
            <Select
              data={pageOptions}
              value={selectedPageIndex.toString()}
              onChange={(value) => setSelectedPageIndex(Number(value))}
              w={rem(150)}
              disabled={isLoadingPages || pages.length === 0}
            />
          </Flex>
        </Flex>
      </Box>
      <Box sx={{ position: 'relative', height: 'calc(100vh - 70px)' }}>
        <LoadingOverlay visible={isLoadingPages} />
        {!isLoadingPages && pages.length > 0 && (
          <Flex sx={{ flex: 1, height: '100%' }}>
            <Box sx={{ flex: 1, overflow: 'auto' }} bg={'silverFox.2'}>
              <Editor resolver={EditorResolver} enabled={false}>
                <BuilderCanvas
                  content={pages[selectedPageIndex].content}
                  preview={true}
                  customWidth={customWidth}
                />
              </Editor>
            </Box>
          </Flex>
        )}
      </Box>
    </Modal>
  );
};

export default PreviewModal;
