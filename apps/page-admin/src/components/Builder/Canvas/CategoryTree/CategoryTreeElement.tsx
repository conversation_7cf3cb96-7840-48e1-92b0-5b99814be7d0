import { ElementWrapper } from '@/components/Builder/Canvas/ElementWrapper';
import { DefaultElementProps } from '@/constants';
import { useResponsiveNode } from '@/hooks';
import { useCurrentTheme } from '@/hooks/theme/useCurrentTheme';
import useCategories from '@/hooks/useCategories';
import { useAppSelector } from '@/store/hooks';
import { Device } from '@/types';
import { PageBuilderElement } from '@/types/enum';
import { CategoryTreeElement as CategoryTreeElementUI } from '@resola-ai/ui/components/PageBuilder';
import {
  BUILDER_CONTAINER_ELEMENT_ID,
  PREVIEW_BUILDER_CONTAINER_ELEMENT_ID,
} from '@resola-ai/ui/constants/page-builder';
import CategoryTreeSettings from '../../Settings/CategoryTreeSettings/CategoryTreeSettings';

const CategoryTreeElement = () => {
  const { width, backgroundColor, padding, showRightDivider, dividerColor } = useResponsiveNode(
    (node) => ({
      width: node.data.props.width,
      backgroundColor: node.data.props.backgroundColor,
      padding: node.data.props.padding,
      showRightDivider: node.data.props.showRightDivider,
      dividerColor: node.data.props.dividerColor,
    })
  );
  const { getThemeColor } = useCurrentTheme();
  const { categories, selectedCategory, selectedSubCategory } = useCategories();
  const accordionStyles = {
    root: {
      backgroundColor: getThemeColor(backgroundColor, 'background'),
    },
    item: {
      borderColor: getThemeColor(dividerColor, 'border'),
    },
    chevron: {
      color: getThemeColor('text'),
    },
    label: {
      color: getThemeColor('foreground'),
    },
    control: {
      '&:hover': {
        backgroundColor: getThemeColor('secondary'),
      },
    },
  };

  const { isShowingPreview, builderPreviewMode } = useAppSelector(
    (state) => state.headerNavigation
  );

  return (
    <ElementWrapper style={builderPreviewMode !== Device.Desktop ? { width: '100%' } : undefined}>
      <CategoryTreeElementUI
        padding={padding}
        width={width || 'auto'}
        categories={categories || []}
        selectedElement={''}
        selectedCategory={selectedCategory?.id || ''}
        selectedSubCategory={selectedSubCategory?.id || ''}
        styles={accordionStyles}
        showRightDivider={showRightDivider}
        dividerColor={getThemeColor(dividerColor, 'border')}
        previewMode={builderPreviewMode}
        containerId={
          isShowingPreview ? PREVIEW_BUILDER_CONTAINER_ELEMENT_ID : BUILDER_CONTAINER_ELEMENT_ID
        }
      />
    </ElementWrapper>
  );
};

CategoryTreeElement.craft = {
  props: DefaultElementProps[PageBuilderElement.CategoryTreeElement],
  related: {
    settings: CategoryTreeSettings,
  },
};

export default CategoryTreeElement;
