import {
  SET_BUILDER_CANVAS_PREVIEW_MODE,
  SET_BUILDER_CANVAS_WIDTH,
  SET_BUILDER_CANVAS_ZOOM,
  SET_HEADER_NAVIGATION_SHOWING,
  SET_HEADER_NAVIGATION_SHOWING_PREVIEW,
} from '@/store/action/actionTypes';
import type { Device } from '@/types/Device';
import { createAction } from '@reduxjs/toolkit';

export const setHeaderNavigationShowing = createAction<boolean, string>(
  SET_HEADER_NAVIGATION_SHOWING
);

export const setHeaderNavigationShowingPreview = createAction<boolean, string>(
  SET_HEADER_NAVIGATION_SHOWING_PREVIEW
);

export const setBuilderCanvasWidth = createAction<string, string>(SET_BUILDER_CANVAS_WIDTH);

export const setBuilderCanvasZoom = createAction<string, string>(SET_BUILDER_CANVAS_ZOOM);

export const setBuilderCanvasPreviewMode = createAction<Device, string>(
  SET_BUILDER_CANVAS_PREVIEW_MODE
);
