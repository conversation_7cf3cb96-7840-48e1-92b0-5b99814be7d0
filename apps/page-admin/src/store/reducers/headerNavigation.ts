import {
  setBuilderCanvasPreviewMode,
  setBuilderCanvasWidth,
  setBuilderCanvasZoom,
  setHeaderNavigationShowing,
  setHeaderNavigationShowingPreview,
} from '@/store/action/headerNavigation';
import { Device } from '@/types/Device';
import { createReducer } from '@reduxjs/toolkit';

interface HeaderNavigationState {
  isShowing: boolean;
  isShowingPreview: boolean;
  builderWidth: string;
  builderZoom: string;
  builderPreviewMode: Device;
}

const initialState: HeaderNavigationState = {
  isShowing: true,
  isShowingPreview: false,
  builderWidth: '',
  builderZoom: '',
  builderPreviewMode: Device.Desktop,
};

const headerNavigationReducer = createReducer(initialState, (builder) => {
  builder.addCase(setHeaderNavigationShowing, (state, action) => {
    state.isShowing = action.payload;
  });
  builder.addCase(setHeaderNavigationShowingPreview, (state, action) => {
    state.isShowingPreview = action.payload;
  });
  builder.addCase(setBuilderCanvasWidth, (state, action) => {
    state.builderWidth = action.payload;
  });
  builder.addCase(setBuilderCanvasZoom, (state, action) => {
    state.builderZoom = action.payload;
  });
  builder.addCase(setBuilderCanvasPreviewMode, (state, action) => {
    state.builderPreviewMode = action.payload;
  });
});

export default headerNavigationReducer;
