import { searchArticles, getSiteData } from '@/api/site';
import { SearchResultElement as SearchResultElementUI } from '@resola-ai/ui/components/PageBuilder/FAQSearchResultElement';
import { useSearchParams } from 'next/navigation';
import { useEffect, useState } from 'react';
import { SiteDataType } from '@/types/enum';

const SearchResultElement = (props: Record<string, any>) => {
  const {
    categoryType,
    maxWidth,
    backgroundColor,
    illustrationType,
    iconSize,
    iconColor,
    iconBgColor,
    hasMaxWidth,
    siteData,
    articleDetailSlug,
    borderColor,
    padding,
    itemBackgroundColor,
    siteId,
  } = props;

  const searchParams = useSearchParams();
  const faqQuery = searchParams.get('faq_query');
  const elementId = searchParams.get('element_id');

  const [searchResults, setSearchResults] = useState([]);
  const [isLoading, setIsLoading] = useState(false);

  useEffect(() => {
    if (!faqQuery) {
      setSearchResults([]);
      return;
    }

    setIsLoading(true);

    const performSearch = async () => {
      try {
        // Use siteId from props, fallback to environment variable
        const targetSiteId = siteId || process.env.NEXT_PUBLIC_SITE_ID || process.env.BUILD_SITE_ID;

        if (!targetSiteId) {
          console.error('No siteId available for search');
          setSearchResults([]);
          return;
        }

        // First, fetch the latest siteData to get fresh articleIds
        const latestSiteData = await getSiteData(targetSiteId, SiteDataType.FaqArticles);

        // Find the element and get the latest articleIds
        const result = latestSiteData?.find((item: any) => item.element_id === elementId);
        const latestArticleIds = result?.articles.map((article: any) => article.value);

        // Now search with the fresh articleIds
        const data = await searchArticles({
          query: faqQuery,
          article_ids: latestArticleIds,
        });

        const formattedResults = data.map((article: any) => ({
          title: article.title,
          description: article.description,
          faq_article_id: article.id,
          element_id: elementId,
          faq_base_id: article.base_id,
        }));

        setSearchResults(formattedResults);
      } catch (error) {
        console.error('Error performing search:', error);
        setSearchResults([]);
      } finally {
        setIsLoading(false);
      }
    };

    performSearch();
  }, [faqQuery, elementId, siteId]);

  return (
    <SearchResultElementUI
      searchResults={searchResults}
      maxWidth={maxWidth}
      backgroundColor={backgroundColor}
      categoryType={categoryType}
      illustrationType={illustrationType}
      iconSize={iconSize}
      iconColor={iconColor}
      iconBgColor={iconBgColor}
      hasMaxWidth={hasMaxWidth}
      articleDetailSlug={articleDetailSlug}
      isLoading={isLoading}
      borderColor={borderColor}
      padding={padding}
      itemBackgroundColor={itemBackgroundColor}
    />
  );
};

export default SearchResultElement;
