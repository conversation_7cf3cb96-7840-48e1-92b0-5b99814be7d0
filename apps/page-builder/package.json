{"name": "page-builder", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "pnpm build-ssr", "build-static": "ENABLE_STATIC_EXPORT=true next build", "build-ssr": "ENABLE_STATIC_EXPORT=false next build", "postbuild": "bash ./postbuild.sh", "start": "next start", "lint": "next lint", "test": "jest", "test:unit": "jest --no-watch", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "coverage": "jest --coverage"}, "dependencies": {"@craftjs/core": "^0.2.12", "@emotion/cache": "^11.14.0", "@emotion/react": "^11.14.0", "@emotion/serialize": "^1.3.3", "@emotion/server": "^11.11.0", "@emotion/styled": "^11.11.0", "@emotion/utils": "^1.4.2", "@mantine/core": "7.17.7", "@mantine/emotion": "7.17.7", "@mantine/hooks": "7.17.7", "@resola-ai/blocknote-editor": "workspace:*", "@resola-ai/models": "workspace:*", "@resola-ai/services-shared": "workspace:*", "@resola-ai/ui": "workspace:*", "lodash": "^4.17.21", "next": "^14.2.26", "react": "^18.2.0", "react-dom": "^18.2.0", "swr": "^2.3.3"}, "devDependencies": {"@babel/preset-env": "^7.26.9", "@babel/preset-react": "^7.26.3", "@babel/preset-typescript": "^7.26.0", "@eslint/eslintrc": "^3", "@types/jest": "29.5.0", "@types/node": "^20", "@types/react": "^18", "@types/react-dom": "^18", "@testing-library/jest-dom": "^6.2.0", "@testing-library/react": "^14.1.2", "@testing-library/user-event": "^14.5.2", "eslint": "^8", "eslint-config-next": "^14.1.0", "jest": "29.5.0", "jest-environment-jsdom": "^29.7.0", "jsdom": "^23.2.0", "ts-jest": "29.0.5", "typescript": "^5"}}