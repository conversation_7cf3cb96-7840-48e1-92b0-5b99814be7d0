import { exec } from "child_process";
import { promisify } from "util";
import * as fs from "fs/promises";
import path from "path";
import { queueWorkerJob, uploadDirToS3, decryptToken } from "./utils";
import { ErrorCode, VersionStatus } from "./types";
import { BuildSiteRequest, BuildSiteResponse } from "./schemas";
import { logger, metrics, tracer } from "./powertools";
import { MetricUnit } from "@aws-lambda-powertools/metrics";
import { SecretsManagerClient, GetSecretValueCommand } from "@aws-sdk/client-secrets-manager";

const UPDATE_PUBLISH_TASK_NAME: string = "apps.pages.tasks.update_publish_task";
const PATHS = {
    BUILD: "/tmp/deca-apps",
    PAGE_BUILDER: "/tmp/deca-apps/apps/page-builder",
    BUILD_OUTPUT: "/tmp/deca-apps/apps/page-builder/out",
} as const;

export interface Environment {
    S3_BUCKET: string;
    SQS_URI: string;
    SECRET_KEY_ARN: string;
    NODE_ENV: string;
    API_SERVER_URL: string;
}

export interface BuildResult {
    success: boolean;
    stdout?: string;
    stderr?: string;
}

export interface QueueResult {
    success: boolean;
    messageId?: string;
    error?: Error;
}

export class EnvironmentService {
    static validate(): Environment {
        const DEFAULT_NODE_ENV = "production";
        const { S3_BUCKET, SQS_URI, SECRET_KEY_ARN, API_SERVER_URL, NODE_ENV } = process.env;
        const nodeEnv = NODE_ENV || DEFAULT_NODE_ENV;

        if (!S3_BUCKET) {
            logger.error("S3 bucket not found in environment variables");
            throw new Error(ErrorCode.CONFIG_BUCKET_NOT_FOUND);
        }
        if (!SQS_URI) {
            logger.error("SQS URI not found in environment variables");
            throw new Error(ErrorCode.CONFIG_SQS_OUTPUT_URI_NOT_FOUND);
        }

        if (!SECRET_KEY_ARN) {
            logger.error("Secret key ARN not found in environment variables");
            throw new Error(ErrorCode.CONFIG_SECRET_KEY_NOT_FOUND);
        }

        if (!API_SERVER_URL) {
            logger.error("API server URL not found in environment variables");
            throw new Error(ErrorCode.CONFIG_API_SERVER_URL_NOT_FOUND);
        }

        return { S3_BUCKET, SQS_URI, SECRET_KEY_ARN, NODE_ENV: nodeEnv, API_SERVER_URL };
    }
}

export class FileSystemService {
    private static execAsync = promisify(exec);

    static async extractSourceFiles(): Promise<void> {
        const subsegment = tracer.getSegment()?.addNewSubsegment("extractSourceFiles");
        try {
            logger.info("Start extracting source files");
            await this.execAsync("tar -xzf /var/task/deca-apps.tar.gz -C /tmp");
            logger.info("Successfully extracted source files");
        } catch (error) {
            logger.error("Failed to extract source files", { error });
            throw error;
        } finally {
            subsegment?.close();
        }
    }

    static async copySourceFiles(): Promise<void> {
        const subsegment = tracer.getSegment()?.addNewSubsegment("copySourceFiles");
        try {
            logger.info("Start copying source files");
            await this.execAsync("cp -r deca-apps /tmp/deca-apps");
            logger.info("Successfully copied source files");
        } catch (error) {
            logger.error("Failed to copy source files", { error });
            throw error;
        } finally {
            subsegment?.close();
        }
    }

    static async writeEnvFile(envVars: Record<string, string>): Promise<void> {
        const subsegment = tracer.getSegment()?.addNewSubsegment("writeEnvFile");
        try {
            const envContent = Object.entries(envVars)
                .map(([key, value]) => `${key}=${value}`)
                .join("\n");

            await fs.writeFile(path.join(PATHS.PAGE_BUILDER, ".env"), envContent, "utf-8");
            logger.info("Successfully wrote environment file");
        } catch (error) {
            logger.error("Failed to write environment file", { error });
            throw error;
        } finally {
            subsegment?.close();
        }
    }
}

export class SiteBuilder {
    private readonly execAsync = promisify(exec);

    constructor(private readonly buildEnvs: Record<string, string>) {}

    private censorSensitiveValue(key: string, value: string): string {
        const sensitiveKeywords = ["TOKEN", "PASSWORD", "SECRET", "KEY"];
        if (sensitiveKeywords.some(keyword => key.toUpperCase().includes(keyword))) {
            if (value.length <= 8) return "****";
            return value.slice(0, 4) + "****" + value.slice(-4);
        }
        return value;
    }

    private logBuildEnvironment(): void {
        const censoredEnvs = Object.entries(this.buildEnvs).reduce(
            (acc, [key, value]) => {
                acc[key] = this.censorSensitiveValue(key, value);
                return acc;
            },
            {} as Record<string, string>
        );

        logger.info("Build environment variables", {
            envVars: censoredEnvs,
        });
    }

    async build(): Promise<BuildResult> {
        const subsegment = tracer.getSegment()?.addNewSubsegment("buildSite");
        const startTime = Date.now();
        try {
            // await FileSystemService.copySourceFiles();
            await FileSystemService.extractSourceFiles();
            await FileSystemService.writeEnvFile(this.buildEnvs);
            this.logBuildEnvironment();

            const { stdout, stderr } = await this.execAsync(
                "cd /tmp/deca-apps/apps/page-builder && bash ./build.sh static"
            );

            const duration = Date.now() - startTime;
            metrics.addMetric("BuildDuration", MetricUnit.Milliseconds, duration);
            metrics.addMetric("BuildSuccess", MetricUnit.Count, 1);

            logger.info("Build completed successfully", { duration });

            return { success: true, stdout, stderr };
        } catch (error) {
            const duration = Date.now() - startTime;
            metrics.addMetric("BuildDuration", MetricUnit.Milliseconds, duration);
            metrics.addMetric("BuildFailure", MetricUnit.Count, 1);

            logger.error("Build failed", {
                error,
                duration,
            });

            return {
                success: false,
                stderr: error instanceof Error ? error.message : "Unknown error",
            };
        } finally {
            subsegment?.close();
        }
    }
}

export class SiteBuilderService {
    private readonly env: Environment;
    private readonly secretsManager: SecretsManagerClient;
    private secretKey: string | null = null;

    constructor() {
        this.env = EnvironmentService.validate();
        this.secretsManager = new SecretsManagerClient({});
    }

    public getLambdaEnvVars(): Environment {
        return this.env;
    }

    private async getSecretKey(): Promise<string> {
        if (this.secretKey) {
            return this.secretKey;
        }

        try {
            const command = new GetSecretValueCommand({
                SecretId: this.env.SECRET_KEY_ARN,
            });

            const response = await this.secretsManager.send(command);
            if (!response.SecretString) {
                throw new Error("Secret string is empty");
            }

            this.secretKey = response.SecretString;
            return this.secretKey;
        } catch (error) {
            logger.error("Failed to fetch secret key", { error });
            throw error;
        }
    }

    public async decryptToken(token: string): Promise<string> {
        const secretKey = await this.getSecretKey();
        return decryptToken(token, secretKey);
    }

    private async sendToQueue(
        request: BuildSiteRequest,
        response: BuildSiteResponse
    ): Promise<QueueResult> {
        const subsegment = tracer.getSegment()?.addNewSubsegment("sendToQueue");
        try {
            logger.info("Sending message to queue", {
                siteId: request.site_id,
                versionId: request.version_id,
                status: response.publish_data.status,
                response: JSON.stringify(response),
            });

            const result = await queueWorkerJob(
                this.env.SQS_URI,
                UPDATE_PUBLISH_TASK_NAME,
                [],
                response,
                request.oid
            );
            if (!result.MessageId) {
                throw new Error("No MessageId received from SQS");
            }

            metrics.addMetric("QueueMessageSent", MetricUnit.Count, 1);
            logger.info("Successfully sent message to queue", {
                messageId: result.MessageId,
                siteId: request.site_id,
                versionId: request.version_id,
            });

            return {
                success: true,
                messageId: result.MessageId,
            };
        } catch (error) {
            metrics.addMetric("QueueMessageFailed", MetricUnit.Count, 1);
            logger.error("Failed to send message to queue", {
                error,
                siteId: request.site_id,
                versionId: request.version_id,
            });

            return {
                success: false,
                error: error instanceof Error ? error : new Error("Unknown queue error"),
            };
        } finally {
            subsegment?.close();
        }
    }

    async handleSuccess(
        request: BuildSiteRequest,
        buildResult: BuildResult
    ): Promise<BuildSiteResponse> {
        const subsegment = tracer.getSegment()?.addNewSubsegment("handleSuccess");
        const startTime = Date.now();

        try {
            const s3TargetDir = `${request.site_id}_${request.version_id}`;
            const s3Prefix = `sites/${s3TargetDir}`;

            await uploadDirToS3(PATHS.BUILD_OUTPUT, this.env.S3_BUCKET, s3Prefix);
            const uploadDuration = Date.now() - startTime;
            metrics.addMetric("UploadDuration", MetricUnit.Milliseconds, uploadDuration);

            const response: BuildSiteResponse = {
                site_id: request.site_id,
                version_id: request.version_id,
                publish_task_id: request.task_id,
                publish_data: {
                    status: VersionStatus.PUBLISHED,
                    stdout: buildResult.stdout,
                    stderr: buildResult.stderr,
                },
            };

            const queueResult = await this.sendToQueue(request, response);
            if (!queueResult.success) {
                logger.error("Queue operation failed in handleSuccess", {
                    error: queueResult.error,
                    siteId: request.site_id,
                    versionId: request.version_id,
                });
                throw queueResult.error;
            }

            logger.info("Successfully handled build", {
                siteId: request.site_id,
                versionId: request.version_id,
                uploadDuration,
                queueMessageId: queueResult.messageId,
            });

            return response;
        } catch (error) {
            logger.error("Failed to handle successful build", { error });
            throw error;
        } finally {
            subsegment?.close();
        }
    }

    async handleFailure(
        request: BuildSiteRequest,
        buildResult: BuildResult
    ): Promise<BuildSiteResponse> {
        const subsegment = tracer.getSegment()?.addNewSubsegment("handleFailure");
        try {
            const response: BuildSiteResponse = {
                site_id: request.site_id,
                version_id: request.version_id,
                publish_task_id: request.task_id,
                publish_data: {
                    status: VersionStatus.DRAFT,
                    stdout: buildResult.stdout,
                    stderr: buildResult.stderr,
                    error_code: ErrorCode.BUILD_FAILED,
                },
            };

            const queueResult = await this.sendToQueue(request, response);
            if (!queueResult.success) {
                logger.error("Queue operation failed in handleFailure", {
                    error: queueResult.error,
                    siteId: request.site_id,
                    versionId: request.version_id,
                });
                throw queueResult.error;
            }

            logger.warn("Handled build failure", {
                siteId: request.site_id,
                versionId: request.version_id,
                error: ErrorCode.UNHANDLED_EXC,
                queueMessageId: queueResult.messageId,
            });

            return response;
        } catch (error) {
            logger.error("Failed to handle build failure", { error });
            throw error;
        } finally {
            subsegment?.close();
        }
    }
}
