import path, { resolve } from 'node:path';
import react from '@vitejs/plugin-react';
import dotenv from 'dotenv';
import { defineConfig, loadEnv } from 'vite';
import circularDependency from 'vite-plugin-circular-dependency';
import tsconfigPaths from 'vite-tsconfig-paths';

const getCdnPrefix = () => {
  if (process.env.AWS_PULL_REQUEST_ID) {
    console.log('[getCdnPrefix] load .env.preview file');
    const previewEnvironmentVariables = dotenv.configDotenv({
      path: '.env.preview',
    });
    return previewEnvironmentVariables.parsed!.VITE_CDN_PREFIX;
  }
  // console.log('[getCdnPrefix] use default env');
  return process.env.VITE_CDN_PREFIX ?? undefined;
};

const cdnPrefix = getCdnPrefix();

// https://vitejs.dev/config/
export default defineConfig(({ mode }) => {
  // Load env file based on `mode` in the current directory.
  // Set the third parameter to '' to load all env regardless of the `VITE_` prefix.
  const env = loadEnv(mode, process.cwd(), '');
  const CDN_URL = cdnPrefix ?? '/';
  const publicUrl = env.VITE_PUBLIC_URL || '/crm/';

  // Common configuration shared between dev/build and test
  const commonConfig = {
    plugins: [react(), tsconfigPaths(), circularDependency()],
    resolve: {
      alias: {
        '@': resolve(__dirname, './src'),
      },
    },
  };

  // Development and build configuration
  return {
    ...commonConfig,
    base: publicUrl,
    experimental: {
      renderBuiltUrl(
        filename: string,
        {
          hostId,
          type,
        }: { hostId: string; hostType: 'js' | 'css' | 'html'; type: 'public' | 'asset' }
      ) {
        if (type === 'public') {
          return CDN_URL + filename;
        }
        if (path.extname(hostId) === '.js') {
          return CDN_URL + filename;
        }
        return CDN_URL + filename;
      },
    },
    server: {
      port: Number(env.VITE_APP_PORT) || 3004,
    },
    preview: {
      port: Number(env.VITE_APP_PORT) || 3004,
    },
    test: {
      globals: true,
      environment: 'jsdom',
      setupFiles: './setupTest.js',
      coverage: {
        enabled: true,
        provider: 'v8',
        reporter: ['text', 'json', 'html', 'lcov', 'text-summary'],
        reportsDirectory: './coverage',
        exclude: [
          'node_modules/**',
          'dist/**',
          'vite.config.ts',
          '.eslintrc.cjs',
          '**/*.d.ts',
          'test/**',
          'tests/**',
          '**/__tests__/**',
          'src/templates/**',
          'src/configs/**',
          'src/constants/**',
          'src/i18n/**',
          'src/tolgee/**',
          'src/services/api/*.ts',
          'src/main.tsx',
          'src/models/**',
        ],
        include: ['src/**/*.{ts,tsx}'],
      },
    },
  };
});
