import { RecordAPI } from '@/services/api';
import React from 'react';
import useSWR from 'swr';

export const useRecordsCount = (wsId: string, objectId: string, enabled = true) => {
  const {
    data: recordsCount,
    isLoading: recordsCountLoading,
    mutate: mutateRecordsCount,
  } = useSWR(wsId && objectId && enabled ? `data/${wsId}/${objectId}/recordsCount` : null, () =>
    RecordAPI.recordsCount(wsId || '', objectId || '')
  );

  return React.useMemo(() => {
    return {
      recordsCount,
      recordsCountLoading,
      mutateRecordsCount,
    };
  }, [recordsCount, recordsCountLoading, mutateRecordsCount]);
};
