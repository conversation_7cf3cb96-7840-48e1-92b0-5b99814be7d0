import AppConfig from '@/configs';
import { IgnoreActivityActorType } from '@/constants/workspace';
import { ActivityAPI } from '@/services/api';
import { useParams } from 'react-router-dom';
import useSWR from 'swr';
const templateDomain = `${AppConfig.CDN_BASE_URL}/assets/templates/activities`;

export const useActivities = () => {
  const { wsId, id: objectId, recordId } = useParams();

  const {
    data: activities,
    isLoading,
    mutate,
  } = useSWR(
    wsId && objectId && recordId
      ? `data/workspaces/${wsId}/${objectId}/${recordId}/activities`
      : null,
    async () => {
      try {
        const res = await ActivityAPI.getList(wsId || '', objectId || '', recordId || '');
        if (!res) return [];

        const sortedActivities = res.sort(
          (a, b) => new Date(b.sentAt || '').getTime() - new Date(a.sentAt || '').getTime()
        );

        // Create an array of unique template URLs by using reduce to deduplicate
        const uniqueTemplateUrls = sortedActivities.reduce(
          (acc, activity) => {
            const key = `${activity.source.type}.${activity.action}`;
            if (
              !acc.some((item) => item.key === key) &&
              !IgnoreActivityActorType.includes(activity.actor?.type || '')
            ) {
              acc.push({
                url: `${templateDomain}/${activity.source.type}.${activity.action}.html`,
                key,
              });
            }
            return acc;
          },
          [] as Array<{ url: string; key: string }>
        );
        // Fetch all unique templates at once
        const templateMap = new Map();
        await Promise.all(
          Array.from(uniqueTemplateUrls).map(async ({ url, key }) => {
            try {
              const response = await fetch(url);
              const template = await response.text();
              templateMap.set(key, template);
            } catch (error) {
              console.error(`Error fetching template for ${url}:`, error);
              templateMap.set(key, ''); // Set empty template on error
            }
          })
        );

        // Map templates back to activities and filter out ignored actor types
        return sortedActivities.map((activity) => ({
          ...activity,
          template: (templateMap.get(`${activity.source.type}.${activity.action}`) as string) || '',
        }));
      } catch (error) {
        console.error('Error while fetching activities', error);
        return [];
      }
    }
  );

  return {
    activities,
    isLoading,
    mutate,
  };
};
