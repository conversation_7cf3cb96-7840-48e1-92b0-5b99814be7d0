import { ObjectAPI } from '@/services/api';
import React from 'react';
import useSWR from 'swr';

export const useObject = (wsId, objId, includeViewSettings = false, reValidate?: boolean) => {
  const {
    data: object,
    mutate,
    isLoading,
  } = useSWR(
    wsId && objId ? `workspace/${wsId}/objects/${objId}` : null,
    () => ObjectAPI.get(wsId || '', objId || '', includeViewSettings),
    { revalidateIfStale: reValidate || true }
  );

  return React.useMemo(() => {
    return {
      object,
      mutate,
      objectLoading: isLoading,
    };
  }, [object, mutate, isLoading]);
};
