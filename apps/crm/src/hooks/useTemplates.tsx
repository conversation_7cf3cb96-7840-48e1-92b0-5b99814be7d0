import { WorkspaceAPI } from '@/services/api';
import React from 'react';
import useSWR from 'swr';

export const useTemplates = (wsId?: string, filters?: string) => {
  const {
    data: templates,
    isLoading,
    mutate: mutateTemplates,
  } = useSWR(wsId ? `${wsId}/templates` : null, () =>
    WorkspaceAPI.getTemplates(wsId as string, filters)
  );

  return React.useMemo(() => {
    return {
      templates,
      isLoading,
      mutateTemplates,
    };
  }, [templates]);
};
