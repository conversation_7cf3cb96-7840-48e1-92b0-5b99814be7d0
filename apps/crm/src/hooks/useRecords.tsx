import { RecordAPI } from '@/services/api';
import React from 'react';
import useSWR from 'swr';

export const useRecords = (wsId, objectId, reValidate?: boolean) => {
  const { data: records, isLoading: recordsLoading } = useSWR(
    wsId && objectId ? `data/${wsId}/${objectId}/records` : null,
    () => RecordAPI.getRecords(wsId || '', objectId || ''),
    {
      revalidateIfStale: reValidate || false,
    }
  );

  return React.useMemo(() => {
    return {
      records,
      recordsLoading,
    };
  }, [records, recordsLoading]);
};
