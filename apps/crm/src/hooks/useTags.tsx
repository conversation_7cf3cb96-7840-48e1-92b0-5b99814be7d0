import { TagAPI } from '@/services/api';
import React from 'react';
import useSWR from 'swr';

export const useTags = (wsId, objId) => {
  const {
    data: tags,
    mutate,
    isLoading,
  } = useSWR(
    wsId && objId ? `data/workspaces/${wsId}/tags/${objId}` : null,
    () => TagAPI.getList(wsId || '', objId || ''),
    { revalidateIfStale: true }
  );

  return React.useMemo(() => {
    return {
      tags,
      mutate,
    };
  }, [tags, mutate, isLoading]);
};
