import { AttachmentAPI } from '@/services/api';
import useSWR from 'swr';

type UseAttachmentIdProps = {
  workspaceId: string;
  objectId: string;
  recordId: string;
  attachmentId: string;
  enabled?: boolean;
};

export const useAttachmentId = ({
  workspaceId,
  objectId,
  recordId,
  attachmentId,
  enabled = true,
}: UseAttachmentIdProps) => {
  const { data, error, isLoading } = useSWR(
    enabled && workspaceId && objectId && recordId && attachmentId
      ? ['attachment', workspaceId, objectId, recordId, attachmentId]
      : null,
    async () => {
      if (!workspaceId || !objectId || !recordId || !attachmentId) {
        throw new Error('Missing required parameters');
      }

      return await AttachmentAPI.get({
        workspaceId,
        objectId,
        recordId,
        attachmentId,
      });
    },
    {
      dedupingInterval: 5000,
      revalidateOnFocus: false,
      shouldRetryOnError: false,
      revalidateIfStale: false,
    }
  );

  return {
    attachment: data,
    url: data?.url || '',
    isLoading,
    error,
  };
};
