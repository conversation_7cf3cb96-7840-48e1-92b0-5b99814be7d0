import { ObjectAPI } from '@/services/api';
import React from 'react';
import useSWR from 'swr';

export const useObjects = (wsId) => {
  const { data: objects = [], mutate } = useSWR(wsId ? `/workspaces/${wsId}/objects` : null, () => {
    return ObjectAPI.getList(wsId as string);
  });

  return React.useMemo(() => {
    return {
      objects,
      mutate,
      firstObject: objects?.[0],
    };
  }, [objects, mutate]);
};
