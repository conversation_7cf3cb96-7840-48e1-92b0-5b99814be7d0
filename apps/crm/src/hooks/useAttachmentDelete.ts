import useSWRMutation from 'swr/mutation';

import { AttachmentAPI } from '@/services/api';

type UseAttachmentDeleteProps = Parameters<typeof AttachmentAPI.delete>[0];

export const useAttachmentDelete = (props: UseAttachmentDeleteProps) => {
  const { attachmentId, objectId, recordId, workspaceId } = props;

  return useSWRMutation(
    attachmentId && objectId && recordId && workspaceId && [props, 'attachments/delete'],
    () => AttachmentAPI.delete(props)
  );
};
