import { RecordAPI } from '@/services/api';
import React from 'react';
import useSWR from 'swr';

export const useRecord = (wsId, objectId, recordId, reValidate?: boolean) => {
  const {
    data: record,
    isLoading: recordLoading,
    mutate: mutateRecord,
  } = useSWR(
    wsId && objectId && recordId ? `data/${wsId}/${objectId}/${recordId}` : null,
    () => RecordAPI.getRecordById(wsId || '', objectId || '', recordId || ''),
    {
      revalidateIfStale: reValidate || false,
    }
  );

  return React.useMemo(() => {
    return {
      record,
      recordLoading,
      mutateRecord,
    };
  }, [record, recordLoading, mutateRecord]);
};
