import AppConfig from '@/configs';
import { useAppContext } from '@/contexts/AppContext';
import { type BreadcrumbItem, useBreadcrumbContext } from '@/contexts/BreadcrumbContext';
import { useProfileContext } from '@/contexts/ProfileContext';
import { useWorkspaceContext } from '@/contexts/WorkspaceContext';
import { usePathParams } from '@resola-ai/ui/hooks';
import { useTranslate } from '@tolgee/react';
import isEmpty from 'lodash/isEmpty';
import { useCallback, useEffect } from 'react';
import { useNavigate, useParams } from 'react-router-dom';

export const useBreadcrumbNavigation = () => {
  const { wsId, id: objectId, recordId } = useParams();
  const navigate = useNavigate();
  const { createPathWithLngParam } = usePathParams();
  const { addBreadcrumb } = useBreadcrumbContext();
  const { object, activeView } = useWorkspaceContext();
  const { profile } = useProfileContext();
  const { objects } = useAppContext();
  const { t } = useTranslate();

  const wsPath = `${AppConfig.BASE_PATH}workspace/${wsId}/objects`;

  // Add or update current record breadcrumb
  useEffect(() => {
    if (!recordId || !object || !activeView || !objectId) return;

    const recordName = profile ? getRecordDisplayName(profile, object, recordId, t) : recordId;

    const breadcrumbItem: BreadcrumbItem = {
      id: `${objectId}-${recordId}`,
      objectId,
      recordId,
      recordName,
      objectName: object.name?.singular || 'Record',
      path: `${wsPath}/${objectId}/views/${activeView.id}/${recordId}`,
    };

    addBreadcrumb(breadcrumbItem, true); // Always replace for current record
  }, [recordId, profile, object, activeView, objectId, wsPath, addBreadcrumb, t]);

  const navigateToLinkedRecord = useCallback(
    (linkedObjectId: string, linkedRecordId: string, linkedRecord?: any) => {
      if (isEmpty(linkedRecord) || !linkedObjectId || !linkedRecordId || !objects?.length) {
        console.warn('Missing required data for navigation');
        return;
      }

      const targetObject = objects.find((obj) => obj.id === linkedObjectId);
      if (!targetObject) {
        console.warn('Target object not found:', linkedObjectId);
        return;
      }

      // Get target view ID
      const targetViewId =
        targetObject.userconfig?.viewId ||
        (targetObject.views?.[0] &&
          (typeof targetObject.views[0] === 'string'
            ? targetObject.views[0]
            : targetObject.views[0].id));

      if (!targetViewId) {
        console.warn('No view found for target object:', linkedObjectId);
        return;
      }

      // Add current record to breadcrumb before navigating
      if (recordId && profile && object && activeView) {
        const currentBreadcrumbItem: BreadcrumbItem = {
          id: `${objectId}-${recordId}`,
          objectId: objectId || '',
          recordId,
          recordName: getRecordDisplayName(profile, object, recordId, t),
          objectName: object.name?.singular || 'Record',
          path: `${wsPath}/${objectId}/views/${activeView.id}/${recordId}`,
        };
        addBreadcrumb(currentBreadcrumbItem);
      }

      // Navigate to linked record
      const targetPath = `${wsPath}/${linkedObjectId}/views/${targetViewId}/${linkedRecordId}`;
      navigate(createPathWithLngParam(targetPath));
    },
    [
      objects,
      wsPath,
      createPathWithLngParam,
      navigate,
      addBreadcrumb,
      recordId,
      profile,
      object,
      activeView,
      objectId,
      t,
    ]
  );

  return {
    navigateToLinkedRecord,
  };
};

// Helper function to get a display name for a record
function getRecordDisplayName(record: any, object: any, recordId: string, t: any): string {
  if (!record || !object) return recordId || t('unknownRecord');

  // Handle relationship field mapValue structure
  if (record.name && typeof record.name === 'string' && record.name.trim()) {
    return record.name.trim();
  }

  // Look for common display fields
  const fields = object.fields || [];
  const displayFields = ['name', 'title', 'label', 'display_name'];

  for (const fieldType of displayFields) {
    const field = fields.find(
      (f: any) =>
        f.name?.toLowerCase().includes(fieldType) || f.id?.toLowerCase().includes(fieldType)
    );
    if (field && record[field.id] && typeof record[field.id] === 'string') {
      const value = record[field.id].trim();
      if (value) return value;
    }
  }

  // Fallback to first text field
  const textField = fields.find(
    (f: any) => f.type === 'singleLineText' && record[f.id] && typeof record[f.id] === 'string'
  );

  if (textField) {
    const value = record[textField.id].trim();
    if (value) return value;
  }

  return recordId || record.id || t('unknownRecord');
}
