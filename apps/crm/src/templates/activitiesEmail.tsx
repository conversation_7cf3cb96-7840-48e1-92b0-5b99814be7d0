import { FormatDate, FormatTime, type MessageType } from '@/constants/workspace';
import type { IActivity } from '@/models';
import {
  Badge,
  Box,
  Divider,
  Flex,
  Group,
  HoverCard,
  Modal,
  Pill,
  ScrollArea,
  Text,
  rem,
  useMantineTheme,
} from '@mantine/core';
import { createStyles } from '@mantine/emotion';
import { IconLine } from '@resola-ai/ui/components/Icons';
import { IconMailFilled, IconMessage } from '@tabler/icons-react';
import { useTranslate } from '@tolgee/react';
import dayjs from 'dayjs';
import DOMPurify from 'dompurify';
import { useState } from 'react';
import { useTranslation } from 'react-i18next';

const useStyles = createStyles(() => ({
  actType: {
    borderRadius: `${rem(4)}`,
  },
  pointer: {
    cursor: 'pointer',
  },
}));

interface ActivityEmailSmsProps {
  type: MessageType;
  payload: IActivity;
}

const formatStatus = (status: string | boolean) => {
  return status === true || status === 'queued' ? 'sent' : 'failed';
};

const ActivityEmailSms = ({ type, payload }: ActivityEmailSmsProps) => {
  const { t } = useTranslate('workspace');
  const { i18n } = useTranslation();
  const { classes } = useStyles();
  const theme = useMantineTheme();
  const [opened, setOpened] = useState(false);
  return (
    <Flex direction='column' w='100%' h='100%' pt={rem(10)}>
      <Flex
        className={classes.pointer}
        gap={rem(10)}
        fz={14}
        fw={400}
        c='#495057'
        align='center'
        onClick={() => setOpened(true)}
      >
        <Flex
          align='center'
          bg='white'
          bd={`${rem(1)} solid ${theme.colors.decaLight[2]}`}
          className={classes.actType}
          py={rem(2)}
          px={rem(8)}
        >
          {type === 'email' && <IconMailFilled size={16} color={theme.colors.decaBlue[4]} />}
          {type === 'sms' && <IconMessage size={16} color={theme.colors.decaViolet[4]} />}
          {type === 'line' && <IconLine fill='green' />}
          <Text mx={rem(6)}>{t(type)}</Text>
          <Badge
            variant='light'
            c={
              formatStatus(payload.context?.status) === 'sent'
                ? theme.colors.decaGreen[8]
                : theme.colors.decaRed[8]
            }
            bg={
              formatStatus(payload.context?.status) === 'sent'
                ? theme.colors.decaGreen[0]
                : theme.colors.decaRed[0]
            }
          >
            {t(formatStatus(payload.context?.status), { ns: 'common' })}
          </Badge>
        </Flex>
        <span>
          {dayjs(payload.createdAt).format(
            i18n.language === 'en'
              ? `${FormatDate.long} ${FormatTime['12h']}`
              : `${FormatDate.longJa} ${FormatTime['24h']}`
          )}
        </span>
      </Flex>
      <Flex wrap='wrap' direction='row' justify='space-between'>
        <Box w='50%'>
          <Text c={theme.colors.decaGrey[9]} fw={500} py={rem(8)}>
            {payload.object?.properties?.subject}
          </Text>
          {type === 'email' && (
            <Text
              span
              lineClamp={3}
              dangerouslySetInnerHTML={{
                __html: DOMPurify.sanitize(payload.object?.properties?.html || '', {
                  ALLOWED_TAGS: ['p', 'br'],
                }),
              }}
            />
          )}
          {type === 'sms' && (
            <Text span lineClamp={3}>
              {payload.object?.properties?.body}
            </Text>
          )}
          {type === 'line' && (
            <Text span lineClamp={3}>
              {payload.object?.properties?.messages?.[0].text}
            </Text>
          )}
        </Box>
      </Flex>
      <Modal.Root
        w='70%'
        opened={opened}
        onClose={() => setOpened(false)}
        scrollAreaComponent={ScrollArea.Autosize}
      >
        <Modal.Overlay />
        <Modal.Content>
          <Modal.Header style={{ justifyContent: 'center' }}>
            <HoverCard width={280} shadow='md'>
              <HoverCard.Target>
                <Text fz={16} fw={500}>
                  {type === 'email' ? payload.object?.properties?.subject : t(type)}
                </Text>
              </HoverCard.Target>
              <HoverCard.Dropdown>
                <Group gap={rem(8)}>
                  {type !== 'line' && (
                    <Flex gap={rem(8)}>
                      <Text fz={14} fw={500}>
                        {t('to')}
                      </Text>
                      <Group gap={rem(4)}>
                        {payload.target?.properties?.to.map((to: string, index: number) => (
                          <Pill key={index}>{to}</Pill>
                        ))}
                      </Group>
                    </Flex>
                  )}
                  {type === 'email' && payload.target?.properties?.cc && (
                    <Flex gap={rem(8)}>
                      <Text fz={14} fw={500}>
                        Cc
                      </Text>
                      <Group gap={rem(4)}>
                        {payload.target?.properties?.cc.map((cc: string, index: number) => (
                          <Pill key={index}>{cc}</Pill>
                        ))}
                      </Group>
                    </Flex>
                  )}
                  {type === 'email' && payload.target?.properties?.bcc && (
                    <Flex gap={rem(8)}>
                      <Text fz={14} fw={500}>
                        Bcc
                      </Text>
                      <Group gap={rem(4)}>
                        {payload.target?.properties?.bcc.map((bcc: string, index: number) => (
                          <Pill key={index}>{bcc}</Pill>
                        ))}
                      </Group>
                    </Flex>
                  )}
                </Group>
              </HoverCard.Dropdown>
            </HoverCard>
          </Modal.Header>
          <Divider />
          <Modal.Body>
            {type === 'sms' && <Text p={rem(10)}>{payload.object?.properties?.body}</Text>}
            {type === 'email' && (
              <Text
                p={rem(10)}
                dangerouslySetInnerHTML={{ __html: payload.object?.properties?.html || '' }}
              />
            )}
            {type === 'line' && (
              <Text p={rem(10)}>{payload.object?.properties?.messages?.[0].text}</Text>
            )}
          </Modal.Body>
        </Modal.Content>
      </Modal.Root>
    </Flex>
  );
};

export default ActivityEmailSms;
