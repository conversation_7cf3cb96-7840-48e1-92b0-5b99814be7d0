import {
  getOrganizationName,
  getRedirectUri,
  isExcludedBasePathDomains,
  isIncludedBasePathDomains,
} from '@/utils';
import { type AppState, Auth0Provider } from '@auth0/auth0-react';
import type React from 'react';
import AppConfig from '../configs';

const Auth0ProviderConfiguration = ({ children }: React.PropsWithChildren) => {
  const redirectUri = getRedirectUri();
  const domain = AppConfig.AUTH0.DOMAIN;
  const clientId = AppConfig.AUTH0.CLIENT_ID;
  const organizationName = getOrganizationName();

  const onRedirectCallback = (appState?: AppState) => {
    if (isExcludedBasePathDomains()) {
      window.location.href = window.location.origin;
      return;
    }
    if (isIncludedBasePathDomains()) {
      window.location.href = window.location.origin + AppConfig.BASE_PATH;
      return;
    }
    window.location.href = appState?.returnTo || AppConfig.BASE_PATH;
  };

  if (!(domain && clientId && redirectUri)) {
    return null;
  }

  const authorizationParams = {
    redirect_uri: redirectUri,
    audience: AppConfig.AUTH0.AUDIENCE,
    scope: AppConfig.AUTH0.SCOPE,
    organization: organizationName,
  };

  return (
    <Auth0Provider
      domain={domain}
      clientId={clientId}
      onRedirectCallback={onRedirectCallback}
      authorizationParams={authorizationParams}
    >
      {children}
    </Auth0Provider>
  );
};

export default Auth0ProviderConfiguration;
