import i18n from 'i18next';
import LanguageDetector from 'i18next-browser-languagedetector';
import { initReactI18next } from 'react-i18next';

import { I18NEXT_COMMON_OPTIONS } from '@resola-ai/ui/constants';
import { table_en } from '@resola-ai/ui/locales/en';
import { table_ja } from '@resola-ai/ui/locales/ja';

const resources = {
  en: {
    table: table_en,
  },
  ja: {
    table: table_ja,
  },
} as const;

const defaultNS = 'common';

const nsList = ['common', 'workspace', 'table'] as const;

const i18nInstance = i18n.createInstance();

i18nInstance
  .use(LanguageDetector)
  .use(initReactI18next)
  .init({
    ...I18NEXT_COMMON_OPTIONS,
    resources,
    ns: nsList,
    defaultNS,
  });

export { i18nInstance };
