import { MantineProvider } from '@mantine/core';
import { MantineEmotionProvider, emotionTransform } from '@mantine/emotion';
import { themeConfigurations } from '@resola-ai/ui/constants';
import { render } from '@testing-library/react';
import { vi } from 'vitest';

if (typeof global.ResizeObserver === 'undefined') {
  global.ResizeObserver = class MockResizeObserver {
    observe = vi.fn();
    unobserve = vi.fn();
    disconnect = vi.fn();
  };
}

export const MantineWrapper = ({ children }: { children: React.ReactNode }) => (
  <MantineProvider stylesTransform={emotionTransform} theme={themeConfigurations}>
    <MantineEmotionProvider>{children}</MantineEmotionProvider>
  </MantineProvider>
);

export const renderWithMantine = (ui: React.ReactNode) => {
  return render(<MantineWrapper>{ui}</MantineWrapper>);
};

export const mockLibraries = () => {
  vi.mock('@mantine/core', async () => {
    const actual = await vi.importActual<typeof import('@mantine/core')>('@mantine/core');
    return {
      ...actual,
    };
  });

  vi.mock('@tabler/icons-react', async () => {
    const actual =
      await vi.importActual<typeof import('@tabler/icons-react')>('@tabler/icons-react');
    return {
      ...actual,
    };
  });

  vi.mock('@mantine/emotion', async () => {
    const actual = await vi.importActual<typeof import('@mantine/emotion')>('@mantine/emotion');
    return {
      ...actual,
    };
  });

  vi.mock('@mantine/hooks', async () => {
    const actual = await vi.importActual<typeof import('@mantine/hooks')>('@mantine/hooks');
    return {
      ...actual,
    };
  });
};
