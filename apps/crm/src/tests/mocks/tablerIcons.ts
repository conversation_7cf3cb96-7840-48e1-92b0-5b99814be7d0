import { vi } from 'vitest';

// Create a mock component for any tabler icon
const createMockIconComponent = (name: string) => {
  return {
    [name]: vi.fn().mockImplementation(({ size, color, ...props }) => {
      // Return a mock representation instead of JSX
      return {
        type: 'div',
        props: {
          'data-testid': `tabler-icon-${name}`,
          'data-icon-size': size,
          'data-icon-color': color,
          ...props,
        },
      };
    }),
  };
};

// Helper to create all necessary tabler icon mocks
export const createTablerIconsMock = () => {
  return {
    // Add icons as needed for the tests
    IconInfoCircle: createMockIconComponent('IconInfoCircle').IconInfoCircle,
    IconChevronRight: createMockIconComponent('IconChevronRight').IconChevronRight,
    IconEdit: createMockIconComponent('IconEdit').IconEdit,
    IconTrash: createMockIconComponent('IconTrash').IconTrash,
    // Add any other icons that are used in the test
  };
};
