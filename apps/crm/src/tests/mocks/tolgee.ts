import { vi } from 'vitest';

// Create a mock for the Tolgee library
const mockTolgee = vi.fn(() => ({
  use: vi.fn().mockReturnThis(),
  init: vi.fn().mockReturnThis(),
  getInitialOptions: vi.fn(() => ({
    language: 'en',
    fallbackLanguage: 'en',
    defaultNs: 'common',
    ns: ['workspace', 'common'],
    apiUrl: 'https://app.tolgee.io',
    apiKey: 'mock-key',
    staticData: {
      'en:workspace': {},
      'ja:workspace': {},
      'en:common': {},
      'ja:common': {},
    },
    onFormatError: (error: string) => {
      console.error('CRM Tolgee translate error', error);
      return error;
    },
  })),
}));

const mockFormatSimple = vi.fn(() => ({
  use: vi.fn().mockReturnThis(),
  init: vi.fn().mockReturnThis(),
}));

const mockInContextTools = vi.fn(() => ({
  use: vi.fn().mockReturnThis(),
  init: vi.fn().mockReturnThis(),
}));

export {
  mockTolgee as Tolgee,
  mockFormatSimple as FormatSimple,
  mockInContextTools as InContextTools,
};
