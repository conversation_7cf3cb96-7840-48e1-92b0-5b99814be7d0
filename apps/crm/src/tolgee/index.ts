import { FormatSimple, Tolgee } from '@tolgee/react';
import { InContextTools } from '@tolgee/web/tools';

import AppConfig from '@/configs';
import { DEFAULT_LANGUAGE } from '@resola-ai/shared-constants';
import enCommon from '../locales/common/en.json';
import jaCommon from '../locales/common/ja.json';
import enWorkspace from '../locales/workspace/en.json';
import jaWorkspace from '../locales/workspace/ja.json';

const tolgee = Tolgee()
  .use(AppConfig.TOLGEE_TOOLS_ENABLED ? InContextTools() : undefined)
  .use(FormatSimple())
  // .use(BackendFetch({ prefix: 'https://cdn.tolg.ee/6216feb86c07be00dc08621981b0ddf0' })) // TODO: if use CDN
  .init({
    language: DEFAULT_LANGUAGE,
    fallbackLanguage: DEFAULT_LANGUAGE,
    defaultNs: 'common',
    apiUrl: AppConfig.TOLGEE_URL,
    apiKey: AppConfig.TOLGEE_KEY,

    staticData: {
      'en:common': enCommon,
      'ja:common': jaCommon,
      'en:workspace': enWorkspace,
      'ja:workspace': jaWorkspace,
    },
    onFormatError: (error) => {
      console.error('CRM Tolgee translate error', error);
      return error;
    },
  });

export { tolgee };
