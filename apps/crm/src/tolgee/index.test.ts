import AppConfig from '@/configs';
import { DEFAULT_LANGUAGE } from '@resola-ai/shared-constants';
import { FALLBACK_LANGUAGE } from '@resola-ai/ui/constants';
import { describe, expect, it, vi } from 'vitest';
import { tolgee } from './index';

// Mock the JSON files with default exports
vi.mock('@/locales/workspace/en.json', () => ({
  default: { test: 'Test EN Workspace' },
}));
vi.mock('@/locales/workspace/ja.json', () => ({
  default: { test: 'Test JA Workspace' },
}));
vi.mock('@/locales/common/en.json', () => ({
  default: { test: 'Test EN Common' },
}));
vi.mock('@/locales/common/ja.json', () => ({
  default: { test: 'Test JA Common' },
}));

vi.mock('@tolgee/react', () => {
  const actual = vi.importActual<typeof import('@tolgee/react')>('@tolgee/react');
  return {
    ...actual,
    FormatSimple: vi.fn(() => ({
      use: vi.fn().mockReturnThis(),
      init: vi.fn().mockReturnThis(),
    })),
    Tolgee: vi.fn(() => ({
      use: vi.fn().mockReturnThis(),
      init: vi.fn().mockReturnThis(),
      getInitialOptions: vi.fn(() => ({
        language: DEFAULT_LANGUAGE,
        fallbackLanguage: FALLBACK_LANGUAGE,
        defaultNs: 'common',
        ns: ['workspace', 'common'],
        apiUrl: AppConfig.TOLGEE_URL,
        apiKey: AppConfig.TOLGEE_KEY,
        staticData: {
          'en:workspace': { test: 'Test EN Workspace' },
          'ja:workspace': { test: 'Test JA Workspace' },
          'en:common': { test: 'Test EN Common' },
          'ja:common': { test: 'Test JA Common' },
        },
        onFormatError: (error: string) => {
          console.error('CRM Tolgee translate error', error);
          return error;
        },
      })),
    })),
  };
});

describe('Tolgee Configuration', () => {
  it('should initialize with correct default settings', () => {
    const options = tolgee.getInitialOptions();

    expect(options.language).toBe(DEFAULT_LANGUAGE);
    expect(options.fallbackLanguage).toBe(FALLBACK_LANGUAGE);
    expect(options.defaultNs).toBe('common');
    expect(options.ns).toEqual(['workspace', 'common']);
  });

  it('should have correct API configuration', () => {
    const options = tolgee.getInitialOptions();

    expect(options.apiUrl).toBe(AppConfig.TOLGEE_URL);
    expect(options.apiKey).toBe(AppConfig.TOLGEE_KEY);
  });

  it('should have all required static data configurations', () => {
    const options = tolgee.getInitialOptions();
    const staticData = options.staticData;

    expect(staticData).toHaveProperty('en:workspace');
    expect(staticData).toHaveProperty('ja:workspace');
    expect(staticData).toHaveProperty('en:common');
    expect(staticData).toHaveProperty('ja:common');
  });

  it('should handle format errors correctly', () => {
    const options = tolgee.getInitialOptions();
    const error = 'Test error';
    const consoleSpy = vi.spyOn(console, 'error');

    if (typeof options.onFormatError === 'function') {
      const result = options.onFormatError(error, { key: 'test.key' });
      expect(consoleSpy).toHaveBeenCalledWith('CRM Tolgee translate error', error);
      expect(result).toBe(error);
    }

    consoleSpy.mockRestore();
  });
});
