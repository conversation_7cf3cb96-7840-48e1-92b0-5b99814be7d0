import { usePathParams } from '@resola-ai/ui/hooks';
import type React from 'react';
import { createContext, useCallback, useContext, useEffect, useState } from 'react';
import { useNavigate, useParams } from 'react-router-dom';

export interface BreadcrumbItem {
  id: string;
  objectId: string;
  recordId: string;
  recordName: string;
  objectName: string;
  path: string;
}

interface BreadcrumbContextType {
  breadcrumbs: BreadcrumbItem[];
  addBreadcrumb: (item: BreadcrumbItem, replaceLastIfSameObject?: boolean) => void;
  navigateToBreadcrumb: (index: number) => void;
  clearBreadcrumbs: () => void;
}

const BreadcrumbContext = createContext<BreadcrumbContextType | undefined>(undefined);

export const useBreadcrumbContext = () => {
  const context = useContext(BreadcrumbContext);
  if (!context) {
    throw new Error('useBreadcrumbContext must be used within a BreadcrumbProvider');
  }
  return context;
};

export const BreadcrumbProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const [breadcrumbs, setBreadcrumbs] = useState<BreadcrumbItem[]>([]);
  const navigate = useNavigate();
  const { createPathWithLngParam } = usePathParams();
  const { recordId } = useParams();

  const addBreadcrumb = useCallback((item: BreadcrumbItem, replaceLastIfSameObject?: boolean) => {
    setBreadcrumbs((prev) => {
      // Check if this item already exists in the breadcrumb trail
      const existingIndex = prev.findIndex((b) => b.id === item.id);

      if (existingIndex !== -1) {
        // If it exists, remove everything after it (user navigated back to this item)
        return prev.slice(0, existingIndex + 1);
      }

      // If we should replace the last breadcrumb when navigating within the same object
      if (replaceLastIfSameObject && prev.length > 0) {
        const lastBreadcrumb = prev[prev.length - 1];
        if (lastBreadcrumb.objectId === item.objectId) {
          // Replace the last breadcrumb with the new one
          return [...prev.slice(0, -1), item];
        }
      }

      return [...prev, item];
    });
  }, []);

  const navigateToBreadcrumb = useCallback(
    (index: number) => {
      if (index < 0 || index >= breadcrumbs.length) return;

      const targetBreadcrumb = breadcrumbs[index];
      const fullPath = createPathWithLngParam(targetBreadcrumb.path);

      // Remove breadcrumbs after the target index
      setBreadcrumbs((prev) => prev.slice(0, index + 1));

      navigate(fullPath);
    },
    [breadcrumbs, navigate, createPathWithLngParam]
  );

  const clearBreadcrumbs = useCallback(() => {
    setBreadcrumbs([]);
  }, []);

  // Clear breadcrumbs when navigating to a different object or when there's no recordId
  useEffect(() => {
    if (!recordId) {
      clearBreadcrumbs();
    }
  }, [recordId, clearBreadcrumbs]);

  const value: BreadcrumbContextType = {
    breadcrumbs,
    addBreadcrumb,
    navigateToBreadcrumb,
    clearBreadcrumbs,
  };

  return <BreadcrumbContext.Provider value={value}>{children}</BreadcrumbContext.Provider>;
};
