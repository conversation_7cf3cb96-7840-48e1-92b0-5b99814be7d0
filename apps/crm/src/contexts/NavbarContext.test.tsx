import { useData, useObject } from '@/hooks';
import type { WSObject } from '@/models';
import { useDisclosure } from '@mantine/hooks';
import { act, render, screen, waitFor } from '@testing-library/react';
import { useParams } from 'react-router-dom';
import { v4 as uuidV4 } from 'uuid';
import { afterEach, beforeEach, describe, expect, it, vi } from 'vitest';
import { useAppContext } from './AppContext';
import { NavbarContextProvider, useNavbarContext } from './NavbarContext';

// Define mock responses using vi.hoisted to avoid hoisting issues
const mockSaveResponse = vi.hoisted(() => ({
  id: 'new-obj',
  name: { singular: 'Test', plural: 'Tests' },
  fields: [],
  displaySettings: {},
}));

const mockUpdateResponse = vi.hoisted(() => ({
  id: 'obj-1',
  name: { singular: 'Updated', plural: 'Updated' },
  fields: [],
  displaySettings: {},
}));

const mockGetResponse = vi.hoisted(() => ({
  id: 'obj-1',
  name: { singular: 'Original', plural: 'Originals' },
  fields: [{ id: 'field-1', type: 'TEXT', header: 'Field 1', options: {} }],
  displaySettings: {},
}));

// Mock API functions with vi.hoisted
const mockObjectAPISave = vi.hoisted(() => vi.fn().mockResolvedValue(mockSaveResponse));
const mockObjectAPIUpdate = vi.hoisted(() => vi.fn().mockResolvedValue(mockUpdateResponse));
const mockObjectAPIGet = vi.hoisted(() => vi.fn().mockResolvedValue(mockGetResponse));
const mockObjectAPIClearData = vi.hoisted(() => vi.fn().mockResolvedValue({ success: true }));
const mockObjectAPIDelete = vi.hoisted(() => vi.fn().mockResolvedValue({ success: true }));
const mockRecordAPISave = vi.hoisted(() =>
  vi.fn().mockResolvedValue({ id: 'new-record', data: {} })
);

// Mock dependencies
vi.mock('./AppContext', () => ({
  useAppContext: vi.fn(),
}));

vi.mock('@/hooks', () => ({
  useData: vi.fn(),
  useObject: vi.fn(),
}));

// Mock the API imports
vi.mock('@/services/api', () => ({
  RecordAPI: {
    save: mockRecordAPISave,
  },
}));

vi.mock('@/services/api/object', () => ({
  ObjectAPI: {
    save: mockObjectAPISave,
    update: mockObjectAPIUpdate,
    get: mockObjectAPIGet,
    clearData: mockObjectAPIClearData,
    delete: mockObjectAPIDelete,
  },
}));

vi.mock('@resola-ai/ui/components', () => ({
  FieldTypes: {
    CHECKBOX: 'CHECKBOX',
  },
}));

vi.mock('@mantine/hooks', () => ({
  useDisclosure: vi.fn(),
}));

vi.mock('react-router-dom', () => ({
  useParams: vi.fn(),
}));

vi.mock('uuid', () => ({
  v4: vi.fn(),
}));

// Test component that uses the context
const TestComponent = () => {
  const {
    createObject,
    updateObject,
    duplicateObject,
    clearData,
    delObject,
    settingOpened,
    settingOpen,
    onCloseSetting,
    currObj,
    setCurrObj,
    actionMenu,
    setActionMenu,
  } = useNavbarContext();

  return (
    <div>
      <div data-testid='setting-status'>{settingOpened ? 'open' : 'closed'}</div>
      <button data-testid='setting-open-btn' onClick={settingOpen}>
        Open Settings
      </button>
      <button data-testid='setting-close-btn' onClick={onCloseSetting}>
        Close Settings
      </button>

      <div data-testid='curr-obj'>{currObj ? currObj.id : 'none'}</div>
      <button
        data-testid='set-curr-obj-btn'
        onClick={() => setCurrObj({ id: 'test-obj' } as WSObject)}
      >
        Set Current Object
      </button>

      <div data-testid='action-menu'>{actionMenu}</div>

      <button
        data-testid='create-btn'
        onClick={() => createObject({ name: { singular: 'Test', plural: 'Test' } })}
      >
        Create Object
      </button>

      <button
        data-testid='update-btn'
        onClick={() => updateObject('obj-1', { name: { singular: 'Updated' } })}
      >
        Update Object
      </button>

      <button data-testid='duplicate-btn' onClick={() => duplicateObject('obj-1')}>
        Duplicate Object
      </button>

      <button data-testid='clear-data-btn' onClick={() => clearData('obj-1')}>
        Clear Data
      </button>

      <button data-testid='delete-btn' onClick={() => delObject('obj-1')}>
        Delete Object
      </button>

      <button data-testid='set-action-menu-btn' onClick={() => setActionMenu('test-action')}>
        Set Action Menu
      </button>
    </div>
  );
};

describe('NavbarContext', () => {
  const mockSettingOpen = vi.fn();
  const mockSettingClose = vi.fn();
  const mockMutate = vi.fn();
  const mockMutateObj = vi.fn();
  const mockMutateRecord = vi.fn();
  const mockSetReloadObject = vi.fn();

  beforeEach(() => {
    vi.clearAllMocks();

    // Setup mock for useAppContext
    (useAppContext as unknown as ReturnType<typeof vi.fn>).mockReturnValue({
      wsDefault: 'default-workspace',
      setReloadObject: mockSetReloadObject,
      objects: [{ id: 'obj-1', views: [{ id: 'view-1', active: true }] }],
      mutateObjects: mockMutate,
    });

    // Setup mock for useData
    (useData as unknown as ReturnType<typeof vi.fn>).mockReturnValue({
      mutate: mockMutateRecord,
    });

    // Setup mock for useObject
    (useObject as unknown as ReturnType<typeof vi.fn>).mockReturnValue({
      mutate: mockMutateObj,
    });

    // Setup mock for useDisclosure
    (useDisclosure as unknown as ReturnType<typeof vi.fn>).mockReturnValue([
      false,
      { open: mockSettingOpen, close: mockSettingClose },
    ]);

    // Setup mock for useParams
    (useParams as unknown as ReturnType<typeof vi.fn>).mockReturnValue({
      wsId: 'workspace-123',
    });

    // Setup mock for uuidV4
    (uuidV4 as unknown as ReturnType<typeof vi.fn>).mockReturnValue('new-uuid');
  });

  afterEach(() => {
    vi.resetAllMocks();
  });

  it('provides context with default values', () => {
    render(
      <NavbarContextProvider>
        <TestComponent />
      </NavbarContextProvider>
    );

    // Action menu should be empty by default
    expect(screen.getByTestId('action-menu').textContent).toBe('');

    // Current object should be undefined by default
    expect(screen.getByTestId('curr-obj').textContent).toBe('none');
  });

  it('creates a new object', async () => {
    render(
      <NavbarContextProvider>
        <TestComponent />
      </NavbarContextProvider>
    );

    // Create object - must wait for the async operation to complete
    await act(async () => {
      screen.getByTestId('create-btn').click();
    });

    // Wait for any pending promises to resolve
    await waitFor(() => {
      // Verify API was called correctly
      expect(mockObjectAPISave).toHaveBeenCalledWith(
        'workspace-123',
        expect.objectContaining({ name: { singular: 'Test', plural: 'Test' }, fields: [] })
      );
      expect(mockMutate).toHaveBeenCalled();
    });
  });

  it('updates an existing object', async () => {
    render(
      <NavbarContextProvider>
        <TestComponent />
      </NavbarContextProvider>
    );

    // Update object
    await act(async () => {
      screen.getByTestId('update-btn').click();
    });

    await waitFor(() => {
      expect(mockObjectAPIUpdate).toHaveBeenCalledWith(
        'workspace-123',
        expect.objectContaining({ name: { singular: 'Updated' }, fields: [] })
      );
      expect(mockMutate).toHaveBeenCalled();
    });
  });

  it('duplicates an object', async () => {
    render(
      <NavbarContextProvider>
        <TestComponent />
      </NavbarContextProvider>
    );

    // Duplicate object
    await act(async () => {
      screen.getByTestId('duplicate-btn').click();
    });

    await waitFor(() => {
      expect(mockObjectAPIGet).toHaveBeenCalledWith('workspace-123', 'obj-1', true);
      expect(mockObjectAPISave).toHaveBeenCalled();
      expect(mockRecordAPISave).toHaveBeenCalled();
    });
  });

  it('clears object data', async () => {
    render(
      <NavbarContextProvider>
        <TestComponent />
      </NavbarContextProvider>
    );

    // Clear data
    await act(async () => {
      screen.getByTestId('clear-data-btn').click();
    });

    await waitFor(() => {
      expect(mockObjectAPIClearData).toHaveBeenCalledWith('workspace-123', 'obj-1');
      expect(mockSetReloadObject).toHaveBeenCalledWith('obj-1');
      expect(mockMutateObj).toHaveBeenCalled();
    });
  });

  it('deletes an object', async () => {
    render(
      <NavbarContextProvider>
        <TestComponent />
      </NavbarContextProvider>
    );

    // Delete object
    await act(async () => {
      screen.getByTestId('delete-btn').click();
    });

    await waitFor(() => {
      expect(mockObjectAPIDelete).toHaveBeenCalledWith('workspace-123', 'obj-1');
      expect(mockMutate).toHaveBeenCalled();
    });
  });

  it('handles setting panel operations', async () => {
    render(
      <NavbarContextProvider>
        <TestComponent />
      </NavbarContextProvider>
    );

    // Open settings
    act(() => {
      screen.getByTestId('setting-open-btn').click();
    });

    expect(mockSettingOpen).toHaveBeenCalled();

    // Set current object
    act(() => {
      screen.getByTestId('set-curr-obj-btn').click();
    });

    expect(screen.getByTestId('curr-obj').textContent).toBe('test-obj');

    // Close settings
    act(() => {
      screen.getByTestId('setting-close-btn').click();
    });

    expect(mockSettingClose).toHaveBeenCalled();
    expect(screen.getByTestId('curr-obj').textContent).toBe('none');
  });

  it('sets action menu', () => {
    render(
      <NavbarContextProvider>
        <TestComponent />
      </NavbarContextProvider>
    );

    // Set action menu
    act(() => {
      screen.getByTestId('set-action-menu-btn').click();
    });

    expect(screen.getByTestId('action-menu').textContent).toBe('test-action');
  });
});
