import { NAVBAR_MIN_WIDTH, NAVBAR_WIDTH } from '@/constants';
import { useObjects } from '@/hooks';
import { WorkspaceAPI } from '@/services/api';
import { useAuth0 } from '@auth0/auth0-react';
import { act, render, screen, waitFor } from '@testing-library/react';
import jwtDecode from 'jwt-decode';
import { afterEach, beforeEach, describe, expect, it, vi } from 'vitest';
import { AppContextProvider, useAppContext } from './AppContext';

// Create factory functions for mocks
const createWorkspaceAPIMock = () => ({
  getFirst: vi.fn().mockResolvedValue({ id: 'workspace-123' }),
});

// Setup mocks with proper return types
const workspaceAPIMock = createWorkspaceAPIMock();

// Mock dependencies
vi.mock('@/services/api', () => ({
  WorkspaceAPI: {
    getFirst: vi.fn(),
  },
}));

vi.mock('@auth0/auth0-react', () => ({
  useAuth0: vi.fn(),
}));

vi.mock('@/hooks', () => ({
  useObjects: vi.fn(),
}));

vi.mock('jwt-decode', () => ({
  default: vi.fn(),
}));

const mockJwtDecode = vi.mocked(jwtDecode);

vi.mock('react-router-dom', () => ({
  useLocation: () => ({ pathname: '/' }),
}));

vi.mock('@resola-ai/ui/hooks', () => ({
  useSetDefaultLang: () => 'en',
}));

vi.mock('@/tolgee', () => ({
  tolgee: {
    changeLanguage: vi.fn(),
  },
}));

// Test component that uses the context
const TestComponent = () => {
  const {
    wsDefault,
    objects,
    accessToken,
    reloadObject,
    setReloadObject,
    sidebarWidth,
    setSidebarWidth,
  } = useAppContext();
  return (
    <div>
      <div data-testid='sidebar-status'>
        {sidebarWidth > NAVBAR_MIN_WIDTH ? 'visible' : 'hidden'}
      </div>
      <div data-testid='workspace-id'>{wsDefault}</div>
      <div data-testid='objects-count'>{objects?.length || 0}</div>
      <div data-testid='is-manager'>no</div>
      <div data-testid='token'>{accessToken ? 'has-token' : 'no-token'}</div>
      <div data-testid='reload-object'>{reloadObject}</div>
      <button
        data-testid='toggle-btn'
        onClick={() =>
          setSidebarWidth(sidebarWidth > NAVBAR_MIN_WIDTH ? NAVBAR_MIN_WIDTH : NAVBAR_WIDTH)
        }
      >
        Toggle
      </button>
      <button data-testid='reload-btn' onClick={() => setReloadObject('obj-1')}>
        Reload
      </button>
    </div>
  );
};

describe('AppContext', () => {
  const mockGetAccessTokenSilently = vi.fn();
  const mockObjectsMutate = vi.fn();

  beforeEach(() => {
    vi.clearAllMocks();

    // Setup WorkspaceAPI mock
    Object.keys(workspaceAPIMock).forEach((key) => {
      (WorkspaceAPI[key] as any) = workspaceAPIMock[key];
    });

    // Setup useAuth0 mock
    mockGetAccessTokenSilently.mockResolvedValue('mock-token');
    (useAuth0 as unknown as ReturnType<typeof vi.fn>).mockReturnValue({
      isAuthenticated: true,
      getAccessTokenSilently: mockGetAccessTokenSilently,
    });

    // Setup useObjects mock
    (useObjects as unknown as ReturnType<typeof vi.fn>).mockReturnValue({
      objects: [{ id: 'obj-1' }, { id: 'obj-2' }],
      mutate: mockObjectsMutate,
    });

    // Setup jwtDecode mock
    mockJwtDecode.mockReturnValue({
      namespace: 'https://example.com',
      'https://example.com/roles': ['crm:manager'],
    });
  });

  afterEach(() => {
    vi.resetAllMocks();
  });

  it('provides context with default values', async () => {
    render(
      <AppContextProvider>
        <TestComponent />
      </AppContextProvider>
    );

    // Sidebar should be visible by default
    expect(screen.getByTestId('sidebar-status').textContent).toBe('visible');

    // Wait for workspace ID to be loaded
    await waitFor(() => {
      expect(screen.getByTestId('workspace-id').textContent).toBe('workspace-123');
    });

    // Should have objects
    expect(screen.getByTestId('objects-count').textContent).toBe('2');
  });

  it('toggles sidebar visibility', async () => {
    render(
      <AppContextProvider>
        <TestComponent />
      </AppContextProvider>
    );

    // Sidebar starts visible
    expect(screen.getByTestId('sidebar-status').textContent).toBe('visible');

    // Toggle sidebar off
    act(() => {
      screen.getByTestId('toggle-btn').click();
    });

    // Sidebar should now be hidden
    expect(screen.getByTestId('sidebar-status').textContent).toBe('hidden');
  });

  it('handles authentication and token retrieval', async () => {
    render(
      <AppContextProvider>
        <TestComponent />
      </AppContextProvider>
    );

    await waitFor(() => {
      expect(mockGetAccessTokenSilently).toHaveBeenCalled();
      expect(screen.getByTestId('token').textContent).toBe('has-token');
    });
  });

  it('handles reload object state', async () => {
    render(
      <AppContextProvider>
        <TestComponent />
      </AppContextProvider>
    );

    // Initial state should be empty
    expect(screen.getByTestId('reload-object').textContent).toBe('');

    // Set reload object
    act(() => {
      screen.getByTestId('reload-btn').click();
    });

    // Reload object should be updated
    expect(screen.getByTestId('reload-object').textContent).toBe('obj-1');
  });

  it('handles manager role detection', async () => {
    render(
      <AppContextProvider>
        <TestComponent />
      </AppContextProvider>
    );

    await waitFor(() => {
      // isManager functionality has been removed in favor of permission-based system
      expect(screen.getByTestId('is-manager').textContent).toBe('no');
    });
  });

  it('handles non-manager role', async () => {
    // Update jwtDecode mock to return non-manager role
    mockJwtDecode.mockReturnValue({
      namespace: 'https://example.com',
      'https://example.com/roles': ['other-role'],
    });

    render(
      <AppContextProvider>
        <TestComponent />
      </AppContextProvider>
    );

    await waitFor(() => {
      // isManager functionality has been removed in favor of permission-based system
      expect(screen.getByTestId('is-manager').textContent).toBe('no');
    });
  });

  it('handles unauthenticated state', async () => {
    // Update useAuth0 mock to return unauthenticated
    (useAuth0 as unknown as ReturnType<typeof vi.fn>).mockReturnValue({
      isAuthenticated: false,
      getAccessTokenSilently: mockGetAccessTokenSilently,
    });

    render(
      <AppContextProvider>
        <TestComponent />
      </AppContextProvider>
    );

    await waitFor(() => {
      expect(screen.getByTestId('token').textContent).toBe('no-token');
    });
  });

  it('handles getAccessTokenSilently error', async () => {
    // Make getAccessTokenSilently reject
    mockGetAccessTokenSilently.mockRejectedValue(new Error('Token error'));

    // Mock console.error to prevent error from showing in test output
    const consoleErrorMock = vi.spyOn(console, 'error');
    consoleErrorMock.mockImplementation(() => {});

    render(
      <AppContextProvider>
        <TestComponent />
      </AppContextProvider>
    );

    await waitFor(() => {
      expect(mockGetAccessTokenSilently).toHaveBeenCalled();
      expect(screen.getByTestId('token').textContent).toBe('no-token');
    });

    consoleErrorMock.mockRestore();
  });
});
