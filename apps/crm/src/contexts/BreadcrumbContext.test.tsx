import { act, render, renderHook, screen } from '@testing-library/react';
import type React from 'react';
import { MemoryRouter } from 'react-router-dom';
import { beforeEach, describe, expect, it, vi } from 'vitest';
import { type BreadcrumbItem, BreadcrumbProvider, useBreadcrumbContext } from './BreadcrumbContext';

// Mock react-router-dom
const mockNavigate = vi.fn();
const mockUseParams = vi.fn();

vi.mock('react-router-dom', async () => {
  const actual = await vi.importActual('react-router-dom');
  return {
    ...actual,
    useNavigate: () => mockNavigate,
    useParams: () => mockUseParams(),
  };
});

// Mock @resola-ai/ui/hooks
const mockCreatePathWithLngParam = vi.fn();
vi.mock('@resola-ai/ui/hooks', () => ({
  usePathParams: () => ({
    createPathWithLngParam: mockCreatePathWithLngParam,
  }),
}));

const createMockBreadcrumbItem = (
  id: string,
  objectId = 'object1',
  recordId = 'record1',
  recordName = 'Test Record',
  objectName = 'Test Object',
  path = '/test/path'
): BreadcrumbItem => ({
  id,
  objectId,
  recordId,
  recordName,
  objectName,
  path,
});

const TestComponent: React.FC = () => {
  const { breadcrumbs, addBreadcrumb, navigateToBreadcrumb, clearBreadcrumbs } =
    useBreadcrumbContext();

  return (
    <div>
      <div data-testid='breadcrumb-count'>{breadcrumbs.length}</div>
      <div data-testid='breadcrumbs'>
        {breadcrumbs.map((item, index) => (
          <div key={item.id} data-testid={`breadcrumb-${index}`}>
            {item.recordName}
          </div>
        ))}
      </div>
      <button
        data-testid='add-breadcrumb'
        onClick={() => addBreadcrumb(createMockBreadcrumbItem('test-1'))}
      >
        Add Breadcrumb
      </button>
      <button data-testid='navigate-breadcrumb' onClick={() => navigateToBreadcrumb(0)}>
        Navigate to First
      </button>
      <button data-testid='clear-breadcrumbs' onClick={() => clearBreadcrumbs()}>
        Clear Breadcrumbs
      </button>
    </div>
  );
};

const renderWithProvider = (recordId: string | undefined = 'test-record') => {
  mockUseParams.mockReturnValue({ recordId });
  mockCreatePathWithLngParam.mockReturnValue('/mocked/path');

  return render(
    <MemoryRouter>
      <BreadcrumbProvider>
        <TestComponent />
      </BreadcrumbProvider>
    </MemoryRouter>
  );
};

const renderHookWithProvider = (recordId: string | undefined = 'test-record') => {
  mockUseParams.mockReturnValue({ recordId });
  mockCreatePathWithLngParam.mockReturnValue('/mocked/path');

  const wrapper = ({ children }: { children: React.ReactNode }) => (
    <MemoryRouter>
      <BreadcrumbProvider>{children}</BreadcrumbProvider>
    </MemoryRouter>
  );

  return renderHook(() => useBreadcrumbContext(), { wrapper });
};

describe('BreadcrumbContext', () => {
  beforeEach(() => {
    vi.clearAllMocks();
  });

  describe('Context Setup', () => {
    it('should throw error when useBreadcrumbContext is used outside provider', () => {
      // Suppress console.error for this test
      const originalError = console.error;
      console.error = vi.fn();

      expect(() => {
        renderHook(() => useBreadcrumbContext());
      }).toThrow('useBreadcrumbContext must be used within a BreadcrumbProvider');

      console.error = originalError;
    });

    it('should provide context value when used within provider', () => {
      const { result } = renderHookWithProvider();

      expect(result.current).toBeDefined();
      expect(result.current.breadcrumbs).toEqual([]);
      expect(typeof result.current.addBreadcrumb).toBe('function');
      expect(typeof result.current.navigateToBreadcrumb).toBe('function');
      expect(typeof result.current.clearBreadcrumbs).toBe('function');
    });
  });

  describe('Initial State', () => {
    it('should start with empty breadcrumbs array', () => {
      const { result } = renderHookWithProvider();

      expect(result.current.breadcrumbs).toEqual([]);
    });

    it('should clear breadcrumbs when recordId is undefined', () => {
      // Start with a recordId, add breadcrumbs, then change to undefined
      mockUseParams.mockReturnValue({ recordId: 'test-record' });
      const { result, rerender } = renderHookWithProvider('test-record');

      // Add a breadcrumb first
      act(() => {
        result.current.addBreadcrumb(createMockBreadcrumbItem('test-1'));
      });

      expect(result.current.breadcrumbs).toHaveLength(1);

      // Now change recordId to undefined
      mockUseParams.mockReturnValue({ recordId: undefined });
      rerender();

      expect(result.current.breadcrumbs).toHaveLength(0);
    });
  });

  describe('addBreadcrumb', () => {
    it('should add new breadcrumb to empty array', () => {
      const { result } = renderHookWithProvider();
      const testItem = createMockBreadcrumbItem('test-1');

      act(() => {
        result.current.addBreadcrumb(testItem);
      });

      expect(result.current.breadcrumbs).toHaveLength(1);
      expect(result.current.breadcrumbs[0]).toEqual(testItem);
    });

    it('should add multiple breadcrumbs in sequence', () => {
      const { result } = renderHookWithProvider();
      const item1 = createMockBreadcrumbItem('test-1');
      const item2 = createMockBreadcrumbItem('test-2');

      act(() => {
        result.current.addBreadcrumb(item1);
        result.current.addBreadcrumb(item2);
      });

      expect(result.current.breadcrumbs).toHaveLength(2);
      expect(result.current.breadcrumbs[0]).toEqual(item1);
      expect(result.current.breadcrumbs[1]).toEqual(item2);
    });

    it('should handle existing breadcrumb by removing everything after it', () => {
      const { result } = renderHookWithProvider();
      const item1 = createMockBreadcrumbItem('test-1');
      const item2 = createMockBreadcrumbItem('test-2');
      const item3 = createMockBreadcrumbItem('test-3');

      // Add three breadcrumbs
      act(() => {
        result.current.addBreadcrumb(item1);
        result.current.addBreadcrumb(item2);
        result.current.addBreadcrumb(item3);
      });

      expect(result.current.breadcrumbs).toHaveLength(3);

      // Add existing item1 again - should remove everything after it
      act(() => {
        result.current.addBreadcrumb(item1);
      });

      expect(result.current.breadcrumbs).toHaveLength(1);
      expect(result.current.breadcrumbs[0]).toEqual(item1);
    });

    it('should replace last breadcrumb when replaceLastIfSameObject is true and same objectId', () => {
      const { result } = renderHookWithProvider();
      const item1 = createMockBreadcrumbItem('test-1', 'object1');
      const item2 = createMockBreadcrumbItem('test-2', 'object1'); // Same objectId

      act(() => {
        result.current.addBreadcrumb(item1);
        result.current.addBreadcrumb(item2, true); // replaceLastIfSameObject = true
      });

      expect(result.current.breadcrumbs).toHaveLength(1);
      expect(result.current.breadcrumbs[0]).toEqual(item2);
    });

    it('should not replace when replaceLastIfSameObject is true but different objectId', () => {
      const { result } = renderHookWithProvider();
      const item1 = createMockBreadcrumbItem('test-1', 'object1');
      const item2 = createMockBreadcrumbItem('test-2', 'object2'); // Different objectId

      act(() => {
        result.current.addBreadcrumb(item1);
        result.current.addBreadcrumb(item2, true); // replaceLastIfSameObject = true
      });

      expect(result.current.breadcrumbs).toHaveLength(2);
      expect(result.current.breadcrumbs[0]).toEqual(item1);
      expect(result.current.breadcrumbs[1]).toEqual(item2);
    });

    it('should not replace when replaceLastIfSameObject is true but breadcrumbs array is empty', () => {
      const { result } = renderHookWithProvider();
      const item1 = createMockBreadcrumbItem('test-1');

      act(() => {
        result.current.addBreadcrumb(item1, true); // replaceLastIfSameObject = true, but array is empty
      });

      expect(result.current.breadcrumbs).toHaveLength(1);
      expect(result.current.breadcrumbs[0]).toEqual(item1);
    });
  });

  describe('navigateToBreadcrumb', () => {
    it('should navigate to valid index and remove breadcrumbs after it', () => {
      const { result } = renderHookWithProvider();
      const item1 = createMockBreadcrumbItem(
        'test-1',
        'object1',
        'record1',
        'Record 1',
        'Object 1',
        '/path/1'
      );
      const item2 = createMockBreadcrumbItem(
        'test-2',
        'object2',
        'record2',
        'Record 2',
        'Object 2',
        '/path/2'
      );
      const item3 = createMockBreadcrumbItem(
        'test-3',
        'object3',
        'record3',
        'Record 3',
        'Object 3',
        '/path/3'
      );

      // Add three breadcrumbs
      act(() => {
        result.current.addBreadcrumb(item1);
        result.current.addBreadcrumb(item2);
        result.current.addBreadcrumb(item3);
      });

      expect(result.current.breadcrumbs).toHaveLength(3);

      // Navigate to index 1 (second breadcrumb)
      act(() => {
        result.current.navigateToBreadcrumb(1);
      });

      expect(result.current.breadcrumbs).toHaveLength(2);
      expect(result.current.breadcrumbs[0]).toEqual(item1);
      expect(result.current.breadcrumbs[1]).toEqual(item2);
      expect(mockCreatePathWithLngParam).toHaveBeenCalledWith('/path/2');
      expect(mockNavigate).toHaveBeenCalledWith('/mocked/path');
    });

    it('should handle invalid negative index', () => {
      const { result } = renderHookWithProvider();
      const item1 = createMockBreadcrumbItem('test-1');

      act(() => {
        result.current.addBreadcrumb(item1);
        result.current.navigateToBreadcrumb(-1); // Invalid index
      });

      expect(result.current.breadcrumbs).toHaveLength(1); // Should remain unchanged
      expect(mockNavigate).not.toHaveBeenCalled();
    });

    it('should handle invalid index that exceeds array length', () => {
      const { result } = renderHookWithProvider();
      const item1 = createMockBreadcrumbItem('test-1');

      act(() => {
        result.current.addBreadcrumb(item1);
        result.current.navigateToBreadcrumb(5); // Index out of bounds
      });

      expect(result.current.breadcrumbs).toHaveLength(1); // Should remain unchanged
      expect(mockNavigate).not.toHaveBeenCalled();
    });

    it('should navigate to first breadcrumb correctly', () => {
      const { result } = renderHookWithProvider();
      const item1 = createMockBreadcrumbItem(
        'test-1',
        'object1',
        'record1',
        'Record 1',
        'Object 1',
        '/path/1'
      );
      const item2 = createMockBreadcrumbItem(
        'test-2',
        'object2',
        'record2',
        'Record 2',
        'Object 2',
        '/path/2'
      );

      act(() => {
        result.current.addBreadcrumb(item1);
        result.current.addBreadcrumb(item2);
      });

      expect(result.current.breadcrumbs).toHaveLength(2);

      act(() => {
        result.current.navigateToBreadcrumb(0); // Navigate to first
      });

      expect(result.current.breadcrumbs).toHaveLength(1);
      expect(result.current.breadcrumbs[0]).toEqual(item1);
      expect(mockCreatePathWithLngParam).toHaveBeenCalledWith('/path/1');
      expect(mockNavigate).toHaveBeenCalledWith('/mocked/path');
    });

    it('should navigate to last breadcrumb correctly', () => {
      const { result } = renderHookWithProvider();
      const item1 = createMockBreadcrumbItem('test-1');
      const item2 = createMockBreadcrumbItem(
        'test-2',
        'object2',
        'record2',
        'Record 2',
        'Object 2',
        '/path/2'
      );

      act(() => {
        result.current.addBreadcrumb(item1);
        result.current.addBreadcrumb(item2);
      });

      expect(result.current.breadcrumbs).toHaveLength(2);

      act(() => {
        result.current.navigateToBreadcrumb(1); // Navigate to last (index 1)
      });

      expect(result.current.breadcrumbs).toHaveLength(2); // Should keep both since we navigated to the last one
      expect(mockCreatePathWithLngParam).toHaveBeenCalledWith('/path/2');
      expect(mockNavigate).toHaveBeenCalledWith('/mocked/path');
    });
  });

  describe('clearBreadcrumbs', () => {
    it('should clear all breadcrumbs', () => {
      const { result } = renderHookWithProvider();
      const item1 = createMockBreadcrumbItem('test-1');
      const item2 = createMockBreadcrumbItem('test-2');

      // Add breadcrumbs
      act(() => {
        result.current.addBreadcrumb(item1);
        result.current.addBreadcrumb(item2);
      });

      expect(result.current.breadcrumbs).toHaveLength(2);

      // Clear breadcrumbs
      act(() => {
        result.current.clearBreadcrumbs();
      });

      expect(result.current.breadcrumbs).toHaveLength(0);
    });

    it('should handle clearing empty breadcrumbs array', () => {
      const { result } = renderHookWithProvider();

      expect(result.current.breadcrumbs).toHaveLength(0);

      act(() => {
        result.current.clearBreadcrumbs();
      });

      expect(result.current.breadcrumbs).toHaveLength(0);
    });
  });

  describe('useEffect behavior', () => {
    it('should clear breadcrumbs when recordId changes to undefined', () => {
      mockUseParams.mockReturnValue({ recordId: 'test-record' });

      const { result, rerender } = renderHookWithProvider('test-record');
      const item1 = createMockBreadcrumbItem('test-1');

      // Add breadcrumb
      act(() => {
        result.current.addBreadcrumb(item1);
      });

      expect(result.current.breadcrumbs).toHaveLength(1);

      // Change recordId to undefined
      mockUseParams.mockReturnValue({ recordId: undefined });
      rerender();

      expect(result.current.breadcrumbs).toHaveLength(0);
    });

    it('should not clear breadcrumbs when recordId exists', () => {
      const { result } = renderHookWithProvider('test-record');
      const item1 = createMockBreadcrumbItem('test-1');

      act(() => {
        result.current.addBreadcrumb(item1);
      });

      expect(result.current.breadcrumbs).toHaveLength(1);
      // Breadcrumbs should remain since recordId exists
    });
  });

  describe('Integration scenarios', () => {
    it('should handle complex navigation flow', () => {
      const { result } = renderHookWithProvider();
      const item1 = createMockBreadcrumbItem(
        'test-1',
        'object1',
        'record1',
        'Record 1',
        'Object 1',
        '/path/1'
      );
      const item2 = createMockBreadcrumbItem(
        'test-2',
        'object2',
        'record2',
        'Record 2',
        'Object 2',
        '/path/2'
      );
      const item3 = createMockBreadcrumbItem(
        'test-3',
        'object3',
        'record3',
        'Record 3',
        'Object 3',
        '/path/3'
      );
      const item4 = createMockBreadcrumbItem(
        'test-4',
        'object4',
        'record4',
        'Record 4',
        'Object 4',
        '/path/4'
      );

      // Build breadcrumb trail
      act(() => {
        result.current.addBreadcrumb(item1);
        result.current.addBreadcrumb(item2);
        result.current.addBreadcrumb(item3);
        result.current.addBreadcrumb(item4);
      });

      expect(result.current.breadcrumbs).toHaveLength(4);

      // Navigate back to middle item
      act(() => {
        result.current.navigateToBreadcrumb(1);
      });

      expect(result.current.breadcrumbs).toHaveLength(2);

      // Add new item from middle
      const item5 = createMockBreadcrumbItem(
        'test-5',
        'object5',
        'record5',
        'Record 5',
        'Object 5',
        '/path/5'
      );
      act(() => {
        result.current.addBreadcrumb(item5);
      });

      expect(result.current.breadcrumbs).toHaveLength(3);
      expect(result.current.breadcrumbs[2]).toEqual(item5);
    });

    it('should handle replacing breadcrumbs within same object multiple times', () => {
      const { result } = renderHookWithProvider();
      const item1 = createMockBreadcrumbItem(
        'test-1',
        'object1',
        'record1',
        'Record 1',
        'Object 1',
        '/path/1'
      );
      const item2 = createMockBreadcrumbItem(
        'test-2',
        'object2',
        'record2',
        'Record 2',
        'Object 2',
        '/path/2'
      );
      const item3 = createMockBreadcrumbItem(
        'test-3',
        'object1',
        'record3',
        'Record 3',
        'Object 1',
        '/path/3'
      ); // Same object as item1
      const item4 = createMockBreadcrumbItem(
        'test-4',
        'object1',
        'record4',
        'Record 4',
        'Object 1',
        '/path/4'
      ); // Same object as item1

      act(() => {
        result.current.addBreadcrumb(item1);
        result.current.addBreadcrumb(item2);
        result.current.addBreadcrumb(item3, true); // Should replace item2 since different object
      });

      expect(result.current.breadcrumbs).toHaveLength(3);

      act(() => {
        result.current.addBreadcrumb(item4, true); // Should replace item3 since same object
      });

      expect(result.current.breadcrumbs).toHaveLength(3);
      expect(result.current.breadcrumbs[2]).toEqual(item4);
    });
  });

  describe('Component Integration', () => {
    it('should work correctly with React components', () => {
      renderWithProvider();

      expect(screen.getByTestId('breadcrumb-count')).toHaveTextContent('0');

      // Add breadcrumb through component
      act(() => {
        screen.getByTestId('add-breadcrumb').click();
      });

      expect(screen.getByTestId('breadcrumb-count')).toHaveTextContent('1');
      expect(screen.getByTestId('breadcrumb-0')).toHaveTextContent('Test Record');
    });

    it('should handle navigation through component', () => {
      renderWithProvider();

      // Add breadcrumb first
      act(() => {
        screen.getByTestId('add-breadcrumb').click();
      });

      expect(screen.getByTestId('breadcrumb-count')).toHaveTextContent('1');

      // Navigate to breadcrumb
      act(() => {
        screen.getByTestId('navigate-breadcrumb').click();
      });

      expect(mockNavigate).toHaveBeenCalled();
    });

    it('should handle clearing through component', () => {
      renderWithProvider();

      // Add breadcrumb first
      act(() => {
        screen.getByTestId('add-breadcrumb').click();
      });

      expect(screen.getByTestId('breadcrumb-count')).toHaveTextContent('1');

      // Clear breadcrumbs
      act(() => {
        screen.getByTestId('clear-breadcrumbs').click();
      });

      expect(screen.getByTestId('breadcrumb-count')).toHaveTextContent('0');
    });
  });
});
