import { NAVBAR_MIN_WIDTH, NAVBAR_WIDTH } from '@/constants';
import { useObjects } from '@/hooks';
import { WorkspaceAPI } from '@/services/api';
import logger from '@/services/logger';
import { tolgee } from '@/tolgee';
import { useAuth0 } from '@auth0/auth0-react';
import { useSetDefaultLang } from '@resola-ai/ui/hooks';
import type React from 'react';
import { createContext, useContext, useEffect, useMemo, useState } from 'react';
import { useLocation } from 'react-router-dom';
import useSWR from 'swr';

const useApp = () => {
  const location = useLocation();
  const [reloadObject, setReloadObject] = useState('');
  const lang = useSetDefaultLang();
  const { isAuthenticated, getAccessTokenSilently } = useAuth0();
  const [accessToken, setAccessToken] = useState('');
  const [sidebarWidth, setSidebarWidth] = useState(NAVBAR_WIDTH);
  const [formOpened, setFormOpened] = useState(false);

  const wsId = useMemo(() => {
    const matches = location.pathname.match(/\/workspace\/([^/]+)/);
    return matches ? matches[1] : undefined;
  }, [location.pathname]);

  // Include wsId in the SWR key to refetch when the route parameter changes
  const { data: wsDefault = '' } = useSWR([`/workspaces`, wsId], async () => {
    if (wsId) {
      const workspace = await WorkspaceAPI.getById(wsId);
      return workspace?.id;
    }
    const workspace = await WorkspaceAPI.getFirst();
    return workspace?.id;
  });
  const { objects, mutate: mutateObjects } = useObjects(wsDefault);

  useEffect(() => {
    tolgee.changeLanguage(lang);
  }, [location]);

  useEffect(() => {
    if (isAuthenticated) {
      getAccessTokenSilently()
        .then((token) => {
          setAccessToken(token);
        })
        .catch((error) => {
          logger.error(error);
        });
    }
  }, [isAuthenticated, getAccessTokenSilently]);

  const onToggleSidebar = () => {
    setSidebarWidth(sidebarWidth === NAVBAR_MIN_WIDTH ? NAVBAR_WIDTH : NAVBAR_MIN_WIDTH);
  };

  return {
    onToggleSidebar,
    wsDefault,
    reloadObject,
    setReloadObject,
    accessToken,
    objects,
    mutateObjects,
    sidebarWidth,
    setSidebarWidth,
    formOpened,
    setFormOpened,
  };
};

export type AppContextType = ReturnType<typeof useApp>;

const context = createContext<AppContextType | null>(null);

export const AppContextProvider = ({ children }: { children: React.ReactNode }) => {
  const value = useApp();

  return <context.Provider value={value}>{children}</context.Provider>;
};

export const useAppContext = () => {
  const value = useContext(context);

  if (!value) {
    throw new Error('useAppContext must be used inside AppContextProvider');
  }

  return value;
};
