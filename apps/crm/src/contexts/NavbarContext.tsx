import { useAppContext } from '@/contexts/AppContext';
import { useData, useObject } from '@/hooks';
import type { WSObject } from '@/models';
import { RecordAPI } from '@/services/api';
import { ObjectAPI } from '@/services/api/object';
import { useDisclosure } from '@mantine/hooks';
import { FieldTypes } from '@resola-ai/ui/components';
import type React from 'react';
import { createContext, useContext, useMemo, useState } from 'react';
import { useParams } from 'react-router-dom';
import { v4 as uuidV4 } from 'uuid';

const useNavbar = () => {
  const [clearObj, setClearObj] = useState('');
  const { wsDefault, setReloadObject, objects, mutateObjects: mutate } = useAppContext();
  const [settingOpened, { open: settingOpen, close: settingClose }] = useDisclosure(false);
  const [currObj, setCurrObj] = useState<WSObject | undefined>();

  const { wsId: currWorkspace = wsDefault, viewId } = useParams();
  const [actionMenu, setActionMenu] = useState('');

  const object = objects?.find((o) => o.id === clearObj);
  const { mutate: mutateObj } = useObject(currWorkspace, clearObj);
  const activeView = useMemo(() => object?.views?.[0], [object]);
  const { mutate: mutateRecord } = useData(currWorkspace, viewId || activeView?.id);

  const createObject = async (data) => {
    const objRes = await ObjectAPI.save(currWorkspace as string, { ...data, fields: [] });
    mutate();
    return objRes;
  };

  const updateObject = async (id: string, data) => {
    let obj = objects?.find((obj) => obj.id === id);
    setClearObj(id);
    if (obj) {
      obj = await ObjectAPI.update(currWorkspace, { ...data, fields: [] });
      await mutate();
      await mutateObj();
    }
    return obj;
  };

  const duplicateObject = async (id: string) => {
    const obj = await ObjectAPI.get(currWorkspace, id, true);
    // create new object
    const objRes = await ObjectAPI.save(currWorkspace, {
      ...obj,
      id: undefined,
      name: { ...obj?.name, singular: `${obj?.name.singular} copy` },
    });
    // add default 1 record to new object
    const newRow = { id: uuidV4() };
    obj?.fields?.forEach((col) => {
      newRow[`${col.id}`] = col.type === FieldTypes.CHECKBOX ? false : '';
    });
    RecordAPI.save(currWorkspace, objRes?.id || '', newRow);

    mutate();
    return objRes;
  };

  const clearData = async (id: string) => {
    setClearObj(id);
    setReloadObject(id);
    await ObjectAPI.clearData(currWorkspace, id);
    await mutateObj();
    await mutateRecord([], false);
  };

  const delObject = async (id: string) => {
    await ObjectAPI.delete(currWorkspace, id);
    mutate();
  };
  const onCloseSetting = () => {
    settingClose();
    setCurrObj(undefined);
  };

  return {
    createObject,
    updateObject,
    duplicateObject,
    clearData,
    delObject,
    currWorkspace,
    settingOpened,
    settingOpen,
    onCloseSetting,
    currObj,
    setCurrObj,
    actionMenu,
    setActionMenu,
  };
};

export type NavbarTypeContext = ReturnType<typeof useNavbar>;

const context = createContext<NavbarTypeContext | null>(null);

export const NavbarContextProvider = ({ children }: { children: React.ReactNode }) => {
  const value = useNavbar();

  return <context.Provider value={value}>{children}</context.Provider>;
};

export const useNavbarContext = () => {
  const value = useContext(context);

  if (!value) {
    throw new Error('useNavbarContext must be used inside NavbarContextProvider');
  }

  return value;
};
