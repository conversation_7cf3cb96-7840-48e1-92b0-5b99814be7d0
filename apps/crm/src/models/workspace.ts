import type { StatusColor } from '@resola-ai/ui';
import type { TableColumnItem, View, ViewGroup } from '@resola-ai/ui/components';
import type { Permissions } from '@resola-ai/ui/components/DecaTable/types';

export interface Workspace {
  id: string;
  name: string;
  icon?: string;
  description?: string;
  active?: boolean;
}

export interface BoxCard {
  id: string;
  height?: string;
}

export interface WSObject {
  id: string;
  name: {
    singular: string;
    plural: string;
  };
  icon?: string;
  description?: string;
  profileSettings?: {
    type: 'activities' | 'identities' | 'attachments' | 'tasks' | 'pinLongText' | 'pinCustomObject';
    enabled: boolean;
  }[];
  messaging?: {
    sms: boolean;
    email: boolean;
    line: boolean;
  };
  fields?: ObjectColumn[];
  views?: View[];
  hasAvatar?: boolean;
  hasTags?: boolean;
  childObjects?: {
    id: string;
    pinned: boolean;
  }[];
  references?: any;
  displaySettings?: {
    boxCards?: BoxCard[];
  };
  attachmentCategories?: {
    id: string;
    name: string;
  }[];
  viewGroups?: ViewGroup[];
  userconfig: {
    viewId?: string;
  };
  enableRecordCounter?: boolean;
  permission: Permissions;
}

export interface ObjectTemplate {
  content: string;
  type: string;
}

export interface Choice {
  color: StatusColor;
  id: string;
  label: string;
  time?: string | number;
}

export interface SingleSelectOptions {
  order: string;
  choices: Choice[];
  enableColor: boolean;
}
export interface DateTimeConfig {
  date?: {
    format?: string;
  };
  time?: {
    format?: string;
    enabled?: boolean;
  };
  displayTimezone?: boolean;
  useSameTimezone?: boolean;
  useCurrentDate?: boolean;
  dateRange?: boolean;
  timezone?: {
    enabled?: boolean;
    format?: string;
  };
}
export interface NumberConfig {
  decimalFormat: 'decimal' | 'integer';
  decimalPlaces?: string;
}

export interface RelationshipConfig {
  objectId?: string;
  fieldId: string;
  recordId: string;
  value?: string;
}

export type FieldValue = string | string[] | boolean | RelationshipConfig;

export interface FieldOptions {
  defaultValue?: string;
  [key: string]: any | SingleSelectOptions | DateTimeConfig | NumberConfig;
}

export interface ObjectColumn extends TableColumnItem<any> {
  id: string;
  name?: string;
  type: string;
  description?: string;
  options?: FieldOptions;
  system?: boolean;
  isProtected?: boolean;
}
