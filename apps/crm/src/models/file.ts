import type { ReactElement } from 'react';

import type { FileUploadItemStatus } from '@resola-ai/ui/components';

interface FilePropertyBagEnhanced extends FilePropertyBag {
  controller?: AbortController;
  status?: FileUploadItemStatus | ReactElement;
}

export class FileEnhanced extends File {
  controller?: AbortController;
  status?: FileUploadItemStatus | ReactElement;

  constructor(fileBits: BlobPart[], fileName: string, options: FilePropertyBagEnhanced = {}) {
    const { controller, status, ...fileOptions } = options;

    super(fileBits, fileName, fileOptions);
    this.controller = controller;
    this.status = status;
  }
}
