import type { ITag } from './tag';
import type { View } from './view';
import type { ObjectColumn } from './workspace';

export interface Record {
  id: string;
  [key: string]: any;
  tags?: ITag[];
}

export interface RecordData {
  records: Record[];
  views: View;
  object: ObjectColumn;
  totalRecordsCount: number;
}

export interface RecordsCount {
  datetime: string;
  values: {
    [key: string]: number;
  }[];
}

export interface MessagePayload {
  to: any[];
  channel: 'sms' | 'mail' | 'line';
  cc?: string[];
  bcc?: string[];
  subject?: string;
  html?: string;
  body?: string;
  attachments?: {
    path: string;
    type: string;
    filename: string;
    url: string;
    disposition: string;
  }[];
}
