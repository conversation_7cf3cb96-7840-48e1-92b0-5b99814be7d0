import type { ITag } from '@/models';
import type { ISuccessListNextResponse, ISuccessResponse } from '../../../../../packages/models';
import { axiosService, logger } from '../../../../../packages/services';

export const TagAPI = {
  getList: async (wsId: string, objectId: string, recordId?: string) => {
    try {
      const response = await axiosService.instance.get<ISuccessListNextResponse<ITag>>(
        `data/workspaces/${wsId}/tags?objectId=${objectId}${recordId ? `&recordId=${recordId}` : ''}`
      );
      return response.data.data;
    } catch (error) {
      logger.error(error);
      throw error;
    }
  },
  save: async (wsId: string, payload) => {
    try {
      const response = await axiosService.instance.post<ISuccessResponse<ITag>>(
        `data/workspaces/${wsId}/tags`,
        payload
      );
      return response.data.data;
    } catch (error) {
      logger.error(error);
      throw error;
    }
  },
  delete: async (wsId: string, tagId: string, recordId: string) => {
    try {
      const response = await axiosService.instance.delete(
        `data/workspaces/${wsId}/tags/${tagId}/${recordId}`
      );
      return response.data.data;
    } catch (error) {
      logger.error(error);
      throw error;
    }
  },
};
