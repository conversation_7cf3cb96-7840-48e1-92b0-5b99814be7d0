import type { MessageType } from '@/constants/workspace';
import type { Workspace } from '@/models';
import type {
  ISuccessListResponseWithoutPagination,
  ISuccessResponse,
} from '../../../../../packages/models';
import { axiosService, logger } from '../../../../../packages/services';

export type TemplatePayload = {
  name: string;
  type: 'email' | 'sms' | 'line';
  file?: File;
  description?: string;
  content?: string;
  objectId?: string;
};

export const WorkspaceAPI = {
  getFirst: async () => {
    try {
      const response =
        await axiosService.instance.get<ISuccessListResponseWithoutPagination<Workspace>>(
          '/core/workspaces'
        );

      return response.data.data[0];
    } catch (error) {
      logger.error(error);
      throw error;
    }
  },
  getById: async (wsId: string) => {
    try {
      const response = await axiosService.instance.get<ISuccessResponse<Workspace>>(
        `/core/workspaces/${wsId}`
      );
      return response.data.data;
    } catch (error) {
      logger.error(error);
      throw error;
    }
  },
  getTemplates: async (wsId: string, filters?: string) => {
    try {
      const response = await axiosService.instance.get(
        `/core/${wsId}/templates`,
        filters ? { params: { filters } } : undefined
      );
      return response.data.data;
    } catch (error) {
      logger.error(error);
      throw error;
    }
  },
  createTemplate: async (wsId: string, payload: TemplatePayload) => {
    try {
      const response = await axiosService.instance.post(`/core/${wsId}/templates`, payload);
      return response.data.data;
    } catch (error) {
      logger.error(error);
      throw error;
    }
  },
  updateTemplate: async (wsId: string, templateId: string, payload: TemplatePayload) => {
    try {
      const response = await axiosService.instance.put(
        `/core/${wsId}/templates/${templateId}`,
        payload
      );
      return response.data.data;
    } catch (error) {
      logger.error(error);
      throw error;
    }
  },
  deleteTemplate: async (wsId: string, templateId: string) => {
    try {
      const response = await axiosService.instance.delete(`/core/${wsId}/templates/${templateId}`);
      return response.data.data;
    } catch (error) {
      logger.error(error);
      throw error;
    }
  },
  getTemplateById: async (wsId: string, templateId: string) => {
    try {
      const response = await axiosService.instance.get(`/core/${wsId}/templates/${templateId}`);
      return response.data.data;
    } catch (error) {
      logger.error(error);
      throw error;
    }
  },
  getChannels: async (type?: MessageType) => {
    try {
      const response = await axiosService.instance.get(`/channels`, {
        params: { type: type || 'line' },
      });
      return response.data.data;
    } catch (error) {
      logger.error(error);
      throw error;
    }
  },
  updateUserConfig: async (wsId: string, payload: { viewId: string; objectId: string }) => {
    try {
      const response = await axiosService.instance.put(
        `/data/${wsId}/userConfigs/defaultView`,
        payload
      );
      return response.data.data;
    } catch (error) {
      logger.error(error);
      throw error;
    }
  },
};
