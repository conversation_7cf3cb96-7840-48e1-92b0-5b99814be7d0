import { isAbortError } from '@/utils/error';
import axios, { type AxiosRequestConfig } from 'axios';

import { logger } from '@resola-ai/services-shared';
import { checkRequiredProperties } from '@resola-ai/utils';

export const UploadAPI = {
  update: async (params: { file: File; url: string; config?: AxiosRequestConfig<File> }) => {
    // Progress tracking with fetch can be implemented with ReadableStream as request body
    // which is only supported by Chrome at the time of implementation so XHR is used instead.
    // References:
    // - https://stackoverflow.com/a/69400632
    // - https://bugzilla.mozilla.org/show_bug.cgi?id=1387483
    const { config, file, url } = params;

    if (!checkRequiredProperties(params, ['url'])) {
      throw new Error('url is required.');
    }

    try {
      // This is used for Amazon S3 presigned URLs
      return await axios.put(url, file, config);
    } catch (err) {
      if (!isAbortError(err as Error)) {
        logger.error(err);
      }

      throw err;
    }
  },
};
