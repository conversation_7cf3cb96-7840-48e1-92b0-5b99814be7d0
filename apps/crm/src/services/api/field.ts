import type { ObjectColumn } from '@/models';
import type { ISuccessResponse } from '../../../../../packages/models';
import { axiosService, logger } from '../../../../../packages/services';

export const FieldAPI = {
  save: async (wsId: string, objId: string, payload) => {
    try {
      const response = await axiosService.instance.post<ISuccessResponse<ObjectColumn>>(
        `core/workspaces/${wsId}/objects/${objId}/fields`,
        payload
      );
      return response.data.data;
    } catch (error) {
      logger.error(error);
      throw error;
    }
  },
  update: async (wsId: string, objId: string, fieldId: string, payload) => {
    try {
      const response = await axiosService.instance.patch<ISuccessResponse<ObjectColumn>>(
        `core/workspaces/${wsId}/objects/${objId}/fields/${fieldId}`,
        payload
      );
      return response.data.data;
    } catch (error) {
      logger.error(error);
      throw error;
    }
  },
  delete: async (wsId: string, objId: string, fieldId: string) => {
    try {
      const response = await axiosService.instance.delete(
        `core/workspaces/${wsId}/objects/${objId}/fields/${fieldId}`
      );
      return response.data.data;
    } catch (error) {
      logger.error(error);
      throw error;
    }
  },
};
