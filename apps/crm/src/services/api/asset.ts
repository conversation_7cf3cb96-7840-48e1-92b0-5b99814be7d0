import type { Asset } from '@/models/asset';
import type { ISuccessResponse } from '@resola-ai/models';
import { axiosService, logger } from '@resola-ai/services-shared';

export const AssetAPI = {
  save: async (file: File, assetType?: 'attachment') => {
    try {
      const response = await axiosService.instance.post<
        ISuccessResponse<{ file: Asset; uploadUrl: string }>
      >('assets', {
        name: file.name,
        size: file.size,
        mimeType: file.type,
        assetType,
      });

      return response.data.data;
    } catch (error) {
      logger.error(error);
      throw error;
    }
  },
};
