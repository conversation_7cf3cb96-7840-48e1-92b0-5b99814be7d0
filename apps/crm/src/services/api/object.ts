import type { ObjectTemplate, WSObject } from '@/models';
import type { ISuccessListNextResponse, ISuccessResponse } from '../../../../../packages/models';
import { axiosService, logger } from '../../../../../packages/services';

export const ObjectAPI = {
  getList: async (wsId: string) => {
    try {
      const response = await axiosService.instance.get<ISuccessListNextResponse<WSObject>>(
        `core/workspaces/${wsId}/objects`
      );
      return response.data.data;
    } catch (error) {
      logger.error(error);
      throw error;
    }
  },
  get: async (wsId: string, objId: string, includeViewSettings?: boolean) => {
    try {
      const response = await axiosService.instance.get<ISuccessResponse<WSObject>>(
        `core/workspaces/${wsId}/objects/${objId}?includeViewSettings=${includeViewSettings}`
      );

      return response.data.data;
    } catch (error) {
      logger.error(error);
      throw error;
    }
  },
  save: async (wsId: string, payload) => {
    try {
      const response = await axiosService.instance.post<ISuccessResponse<WSObject>>(
        `core/workspaces/${wsId}/objects`,
        {
          ...payload,
          name: {
            // TODO confirm with BE
            ...payload.name,
            plural: payload.name.singular,
          },
        }
      );
      return response.data.data;
    } catch (error) {
      logger.error(error);
      throw error;
    }
  },
  update: async (wsId: string, payload) => {
    try {
      const response = await axiosService.instance.patch<ISuccessResponse<WSObject>>(
        `core/workspaces/${wsId}/objects/${payload.id}`,
        {
          ...payload,
        }
      );
      return response.data.data;
    } catch (error) {
      logger.error(error);
      throw error;
    }
  },
  delete: async (wsId: string, objId: string) => {
    try {
      const response = await axiosService.instance.delete(
        `core/workspaces/${wsId}/objects/${objId}`
      );
      return response.data.data;
    } catch (error) {
      logger.error(error);
      throw error;
    }
  },
  clearData: async (wsId: string, objId: string) => {
    try {
      const response = await axiosService.instance.delete(`data/${wsId}/${objId}`);
      return response.data.data;
    } catch (error) {
      logger.error(error);
      throw error;
    }
  },
  findTemplate: async (wsId: string, objId: string) => {
    try {
      const response = await axiosService.instance.get<ISuccessResponse<ObjectTemplate>>(
        `core/workspaces/${wsId}/objects/${objId}/template`
      );
      return response.data.data;
    } catch (error) {
      logger.error(error);
      throw error;
    }
  },
};
