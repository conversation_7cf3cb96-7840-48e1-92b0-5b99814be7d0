import type { Attachment } from '@/models/attachment';
import type { ISuccessListNextResponse, ISuccessResponse } from '@resola-ai/models';
import { axiosService, logger } from '@resola-ai/services-shared';
import { checkRequiredProperties } from '@resola-ai/utils';

export const AttachmentAPI = {
  get: async (params: {
    workspaceId: string;
    objectId: string;
    recordId: string;
    attachmentId: string;
  }) => {
    const { workspaceId, objectId, recordId, attachmentId } = params;
    const url = `data/${workspaceId}/${objectId}/${recordId}/attachments/${attachmentId}`;

    if (!checkRequiredProperties(params)) {
      throw new Error(`${Object.keys(params).join(', ')} are required.`);
    }

    try {
      const response = await axiosService.instance.get<ISuccessResponse<Attachment>>(url);
      return response.data.data;
    } catch (err) {
      logger.error(err);
      throw err;
    }
  },
  getList: async (params: {
    workspaceId: string;
    objectId: string;
    recordId: string;
    sort?: Partial<Record<keyof Attachment, 'asc' | 'desc'>>;
  }) => {
    const { objectId, recordId, workspaceId, sort } = params;
    let url = `data/${workspaceId}/${objectId}/${recordId}/attachments`;

    if (sort && Object.keys(sort).length) {
      url += `?sort=${JSON.stringify(sort)}`;
    }

    try {
      const response = await axiosService.instance.get<ISuccessListNextResponse<Attachment>>(url);

      return response.data.data;
    } catch (err) {
      logger.error(err);
      throw err;
    }
  },
  save: async (
    params: { workspaceId: string; objectId: string; recordId: string },
    attachment: Omit<Attachment, 'id' | 'createdAt' | 'updatedAt'> & {
      assetId: string;
      assetType?: 'attachment';
    }
  ) => {
    const { objectId, recordId, workspaceId } = params;
    const url = `data/${workspaceId}/${objectId}/${recordId}/attachments`;

    if (!checkRequiredProperties(params)) {
      throw new Error(`${Object.keys(params).join(', ')} are required.`);
    }

    try {
      const response = await axiosService.instance.post<ISuccessResponse<Attachment>>(
        url,
        attachment
      );

      return response.data.data;
    } catch (err) {
      logger.error(err);
      throw err;
    }
  },
  delete: async (params: {
    workspaceId: string;
    objectId: string;
    recordId: string;
    attachmentId: string;
  }) => {
    const { attachmentId, objectId, recordId, workspaceId } = params;
    const url = `data/${workspaceId}/${objectId}/${recordId}/attachments/${attachmentId}`;

    if (!checkRequiredProperties(params)) {
      throw new Error(`${Object.keys(params).join(', ')} are required.`);
    }

    try {
      await axiosService.instance.delete(url);
    } catch (err) {
      logger.error(err);
      throw err;
    }
  },
  update: async (params: {
    workspaceId: string;
    objectId: string;
    recordId: string;
    attachmentId: string;
    categoryId?: string;
  }) => {
    const { objectId, recordId, workspaceId, attachmentId, categoryId } = params;
    const requiredParams = { workspaceId, objectId, recordId, attachmentId };

    if (!checkRequiredProperties(requiredParams)) {
      throw new Error(`${Object.keys(requiredParams).join(', ')} are required.`);
    }

    try {
      const response = await axiosService.instance.patch<ISuccessResponse<Attachment>>(
        `data/${workspaceId}/${objectId}/${recordId}/attachments/${attachmentId}`,
        { categoryId }
      );

      return response.data.data;
    } catch (err) {
      logger.error(err);
      throw err;
    }
  },
};
