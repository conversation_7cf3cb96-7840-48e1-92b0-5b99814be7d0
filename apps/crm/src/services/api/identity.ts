import type { Identity } from '@/models/identity';
import type { ISuccessListNextResponse } from '@resola-ai/models';
import { axiosService, logger } from '@resola-ai/services-shared';

export const IdentityAPI = {
  getList: async (params: {
    workspaceId: string;
    objectId: string;
    recordId: string;
    sort?: Partial<Record<keyof Identity, 'asc' | 'desc'>>;
  }) => {
    const { objectId, recordId, workspaceId, sort } = params;
    let url = `data/${workspaceId}/${objectId}/${recordId}/identities`;

    if (sort && Object.keys(sort).length) {
      url += `?sort=${JSON.stringify(sort)}`;
    }

    try {
      const response = await axiosService.instance.get<ISuccessListNextResponse<Identity>>(url);

      return response.data.data;
    } catch (err) {
      logger.error(err);
      throw err;
    }
  },
};
