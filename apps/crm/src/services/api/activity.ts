import type { IActivity } from '@/models';
import type { ISuccessListNextResponse, ISuccessResponse } from '@resola-ai/models';
import { axiosService, logger } from '@resola-ai/services-shared';

export const ActivityAPI = {
  create: async (wsId: string, objectId: string, recordId: string, activity: IActivity) => {
    try {
      const response = await axiosService.instance.post<ISuccessResponse<IActivity>>(
        `data/${wsId}/${objectId}/${recordId}/activities`,
        activity
      );
      return response.data.data;
    } catch (error) {
      logger.error(error);
      throw error;
    }
  },
  getList: async (wsId: string, objectId: string, recordId: string) => {
    try {
      const response = await axiosService.instance.get<ISuccessListNextResponse<IActivity>>(
        `data/${wsId}/${objectId}/${recordId}/activities`
      );
      return response.data.data;
    } catch (error) {
      logger.error(error);
      throw error;
    }
  },
};
