import type { IHistory } from '@/models';
import type { ISuccessListNextResponse } from '@resola-ai/models';
import { axiosService, logger } from '@resola-ai/services-shared';

export const HistoryAPI = {
  getList: async (wsId: string, objectId: string, recordId: string) => {
    try {
      const response = await axiosService.instance.get<ISuccessListNextResponse<IHistory>>(
        `data/${wsId}/${objectId}/${recordId}/history`
      );
      return response.data.data;
    } catch (error) {
      logger.error(error);
      throw error;
    }
  },
};
