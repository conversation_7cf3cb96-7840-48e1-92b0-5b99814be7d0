import type { View as TableView } from '@resola-ai/ui/components';
import { NEWCOL_ID } from '@resola-ai/ui/components/DecaTable/constants';
import type { ISuccessListNextResponse, ISuccessResponse } from '../../../../../packages/models';
import { axiosService, logger } from '../../../../../packages/services';

export const ViewAPI = {
  get: async (wsId: string, objId: string, viewId: string) => {
    try {
      const response = await axiosService.instance.get<ISuccessResponse<TableView>>(
        `core/workspaces/${wsId}/objects/${objId}/views/${viewId}`
      );
      return response.data.data;
    } catch (error) {
      logger.error(error);
      throw error;
    }
  },
  getList: async (wsId: string, objId: string) => {
    try {
      const response = await axiosService.instance.get<ISuccessListNextResponse<TableView>>(
        `core/workspaces/${wsId}/objects/${objId}/views`
      );

      return response.data.data;
    } catch (error) {
      logger.error(error);
      throw error;
    }
  },
  save: async (wsId: string, objId: string, payload) => {
    try {
      const response = await axiosService.instance.post<ISuccessResponse<TableView>>(
        `core/workspaces/${wsId}/objects/${objId}/views`,
        {
          ...payload,
          workspaceId: wsId,
          type: 'grid',
          icon: 'window',
          filters: payload.filters || '',
          sort: payload.sort || [],
        }
      );
      return response.data.data;
    } catch (error) {
      logger.error(error);
      throw error;
    }
  },
  update: async (wsId: string, objId: string, viewId: string, payload: TableView) => {
    const fields = payload.fields
      .filter((f) => f.accessorKey !== NEWCOL_ID)
      .map((field) => {
        return {
          fieldMetaId: field.id || field.fieldMetaId || field.accessorKey,
          isVisible: field.isVisible || false,
          size: field.size,
        };
      });
    try {
      const response = await axiosService.instance.patch<ISuccessResponse<TableView>>(
        `core/workspaces/${wsId}/objects/${objId}/views/${viewId}`,
        {
          ...payload,
          fields,
        }
      );
      return response.data.data;
    } catch (error) {
      logger.error(error);
      throw error;
    }
  },
  updateFieldOrder: async (wsId: string, objId: string, viewId: string, payload: string[]) => {
    try {
      const response = await axiosService.instance.put<ISuccessResponse<TableView>>(
        `core/workspaces/${wsId}/objects/${objId}/views/${viewId}/fieldOrder`,
        payload
      );
      return response.data.data;
    } catch (error) {
      logger.error(error);
      throw error;
    }
  },
  delete: async (wsId: string, objId: string, viewId: string) => {
    try {
      const response = await axiosService.instance.delete<ISuccessResponse<TableView>>(
        `core/workspaces/${wsId}/objects/${objId}/views/${viewId}`
      );
      return response.data.data;
    } catch (error) {
      logger.error(error);
      throw error;
    }
  },
};
