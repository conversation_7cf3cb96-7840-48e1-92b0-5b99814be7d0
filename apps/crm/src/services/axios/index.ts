import { notifications } from '@mantine/notifications';
import { axiosService } from '@resola-ai/services-shared';
import { HTTP_ERROR_STATUS } from '@resola-ai/ui/constants';
import { IconAlertCircle } from '@tabler/icons-react';
import { type AxiosError, CanceledError } from 'axios';
import React from 'react';
import type { IFailResponse } from '../../models/response';
import { tolgee } from '../../tolgee';
import { customNotificationStyles } from '../../utils/workspace';

const IGNORED_NOTIFICATION_ERROR_CODES = ['crmViewListDenied'];

class AxiosService {
  private isInterceptorAdded = false;

  init(apiServerUrl: string) {
    // Initialize the global axios service
    axiosService.init(apiServerUrl, tolgee.t);

    // Add our custom CRM interceptor if not already added
    if (!this.isInterceptorAdded) {
      axiosService.instance.interceptors.response.use(
        (response) => {
          return response;
        },
        (error: AxiosError<IFailResponse>) => {
          const errorMessage = error?.response?.data?.message;
          const errorCode = error?.response?.data?.code || error.code;

          if (
            (errorMessage &&
              error.response?.status !== HTTP_ERROR_STATUS.UNAUTHORIZED &&
              !IGNORED_NOTIFICATION_ERROR_CODES.includes(errorCode ?? '')) ||
            error.code === 'ERR_NETWORK' ||
            error instanceof CanceledError
          ) {
            notifications.show({
              message: tolgee.t(`errors.${errorCode}`) || errorMessage,
              icon: React.createElement(IconAlertCircle, { size: 24 }),
              autoClose: 3000,
              styles: (theme) =>
                customNotificationStyles(
                  theme,
                  theme.colors.decaRed[0],
                  theme.colors.decaRed[9]
                ) as any,
            });
          }
          return Promise.reject(error);
        }
      );
      this.isInterceptorAdded = true;
    }
  }

  setAccessToken(accessToken: string) {
    axiosService.setAccessToken(accessToken);
  }

  getAccessToken() {
    return axiosService.getAccessToken();
  }

  setOrgId(orgId: string) {
    axiosService.setOrgId(orgId);
  }

  getOrgId() {
    return axiosService.getOrgId();
  }

  get instance() {
    return axiosService.instance;
  }
}

const crmAxiosService = new AxiosService();
export default crmAxiosService;
