import './polyfills/promiseWithResolvers';
import { MantineProvider } from '@mantine/core';
import { ModalsProvider } from '@mantine/modals';
import { Notifications } from '@mantine/notifications';
import React from 'react';
import ReactDOM from 'react-dom/client';
import { BrowserRouter } from 'react-router-dom';
import { AppContextProvider } from './contexts/AppContext';
import './index.css';
import '@mantine/core/styles.css';
import '@mantine/dates/styles.css';
import '@mantine/notifications/styles.css';
import { MantineEmotionProvider, emotionTransform } from '@mantine/emotion';
import { datadogService } from '@resola-ai/services-shared';
import { AuthenticationLayer } from '@resola-ai/ui';
import { themeConfigurations } from '@resola-ai/ui/constants';
import { Auth0ProviderConfiguration, BrowserNavigationProvider } from '@resola-ai/ui/providers';
import { TolgeeProvider } from '@tolgee/react';
import { logger, shareAppConfigService } from '../../../packages/services';
import { version } from '../package.json';
import App from './App';
import { SwrCustomConfig } from './components';
import GlobalStyles from './components/GlobalStyles';
import AppConfig from './configs';
import crmAxiosService from './services/axios';
import { tolgee } from './tolgee';

crmAxiosService.init(AppConfig.API_SERVER_URL);
logger.init(AppConfig.IS_PRODUCTION);
shareAppConfigService.init(AppConfig);

// Initialize Datadog service
datadogService.init({
  applicationId: import.meta.env.VITE_DATADOG_APPLICATION_ID ?? '',
  clientToken: import.meta.env.VITE_DATADOG_CLIENT_TOKEN ?? '',
  site: import.meta.env.VITE_DATADOG_SITE ?? '',
  service: import.meta.env.VITE_DATADOG_SERVICE ?? '',
  env: import.meta.env.VITE_DATADOG_ENV ?? '',
  version: version,
});

ReactDOM.createRoot(document.getElementById('root')!).render(
  <React.StrictMode>
    <MantineProvider stylesTransform={emotionTransform} theme={themeConfigurations}>
      <MantineEmotionProvider>
        <GlobalStyles />
        <ModalsProvider>
          <BrowserRouter window={window}>
            <BrowserNavigationProvider>
              <SwrCustomConfig>
                <Auth0ProviderConfiguration>
                  <TolgeeProvider tolgee={tolgee} fallback='Loading...'>
                    <AuthenticationLayer>
                      <AppContextProvider>
                        <Notifications position='top-right' className='notification' />
                        <App />
                      </AppContextProvider>
                    </AuthenticationLayer>
                  </TolgeeProvider>
                </Auth0ProviderConfiguration>
              </SwrCustomConfig>
            </BrowserNavigationProvider>
          </BrowserRouter>
        </ModalsProvider>
      </MantineEmotionProvider>
    </MantineProvider>
  </React.StrictMode>
);
