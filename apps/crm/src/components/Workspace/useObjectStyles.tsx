import { rem } from '@mantine/core';
import { createStyles } from '@mantine/emotion';
import { DECA_TABLE_CLASSES } from '@resola-ai/ui/components/DecaTable/constants';
import { useMemo } from 'react';

const useStyles = createStyles((theme) => ({
  container: {
    overflow: 'hidden',
    backgroundColor: theme.colors.decaLight[0],
    [`.${DECA_TABLE_CLASSES.TABLE_CONTAINER}`]: {
      maxHeight: `calc(100vh - ${rem(106)}) !important`,
      overflow: 'auto',
      position: 'relative',

      [`.${DECA_TABLE_CLASSES.TABLE_ROOT} > thead > tr`]: {
        'th:nth-of-type(3)': {
          marginLeft: rem(-40),
        },
        'th:nth-of-type(2), th:last-of-type': {
          display: 'none',
        },
      },
      [`.${DECA_TABLE_CLASSES.TABLE_ROOT} > tbody > tr`]: {
        'td:last-of-type': {
          display: 'none',
        },
      },
    },
  },
}));
export default function useObjectStyles() {
  const { classes } = useStyles();

  return useMemo(
    () => ({
      classes,
    }),
    [classes]
  );
}
