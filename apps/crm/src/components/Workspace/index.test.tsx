import { renderWithMantine } from '@/tests/utils/testUtils';
import { beforeEach, describe, expect, it, vi } from 'vitest';
import { Workspace } from './index';

// Mock all external dependencies to avoid complex context issues
vi.mock('react-router-dom', () => ({
  useParams: () => ({
    wsId: 'workspace-123',
    id: 'object-456',
    recordId: undefined,
  }),
  useNavigate: () => vi.fn(),
  Link: ({ children, to, className, target }: any) => (
    <a href={to} className={className} target={target}>
      {children}
    </a>
  ),
}));

vi.mock('@tolgee/react', () => ({
  useTranslate: () => ({
    t: vi.fn((key: string) => key),
  }),
}));

vi.mock('@/contexts/AppContext', () => ({
  useAppContext: () => ({
    isManager: true,
  }),
}));

vi.mock('@/contexts/BreadcrumbContext', () => ({
  BreadcrumbProvider: ({ children }: any) => children,
  useBreadcrumbContext: () => ({
    breadcrumbs: [],
    addBreadcrumb: vi.fn(),
    navigateToBreadcrumb: vi.fn(),
    clearBreadcrumbs: vi.fn(),
  }),
}));

vi.mock('@/hooks/useBreadcrumbNavigation', () => ({
  useBreadcrumbNavigation: () => ({
    navigateToLinkedRecord: vi.fn(),
  }),
}));

vi.mock('@resola-ai/ui/hooks', () => ({
  usePathParams: () => ({
    createPathWithLngParam: (path: string) => path,
  }),
}));

vi.mock('@/contexts/WorkspaceContext', () => ({
  WorkspaceContextProvider: ({ children }: any) => children,
  useWorkspaceContext: () => ({
    columns: [],
    data: [],
    handleAddRow: vi.fn(),
    activeView: null,
    views: [],
    loading: false,
    handleActionRow: vi.fn(),
    openProfile: vi.fn(),
    recordsLoading: false,
    object: null,
    rowSelection: {},
    setRowSelection: vi.fn(),
    selectedRowDetails: [],
    tags: [],
    size: 1,
    setSize: vi.fn(),
    totalRecords: 0,
    handleViewChange: vi.fn(),
    viewLoading: false,
    viewGroups: [],
    handleApplyManageView: vi.fn(),
    onSavingCell: vi.fn(),
  }),
}));

vi.mock('@/services/api', () => ({
  TasksAPI: {
    exportCSV: vi.fn(),
  },
}));

// Mock all UI components to avoid deep dependency issues
vi.mock('@resola-ai/ui/components', () => ({
  DecaTable: () => <div data-testid='deca-table'>Mocked DecaTable</div>,
  FieldTypes: {
    CHECKBOX: 'checkbox',
    AUTONUMBER: 'autonumber',
    CREATED_TIME: 'createdTime',
    MODIFIED_TIME: 'modifiedTime',
    CREATED_BY: 'createdBy',
    MODIFIED_BY: 'modifiedBy',
  },
  SortOrder: {
    Ascending: 'asc',
    Descending: 'desc',
  },
}));

vi.mock('@resola-ai/ui/components/DecaTable/components', () => ({
  AddNewRowToolbarItem: () => <div>AddNewRowToolbarItem</div>,
  CustomFieldsToolbarItem: () => <div>CustomFieldsToolbarItem</div>,
  ExportCSVToolbarItem: () => <div>ExportCSVToolbarItem</div>,
  MenuViewToolbarItem: () => <div>MenuViewToolbarItem</div>,
  SearchBoxToolbarItem: () => <div>SearchBoxToolbarItem</div>,
  SelectViewToolbarItem: () => <div>SelectViewToolbarItem</div>,
  TableFilterToolbarItem: () => <div>TableFilterToolbarItem</div>,
  TableSortToolbarItem: () => <div>TableSortToolbarItem</div>,
  TableHeightToolbarItem: () => <div>TableHeightToolbarItem</div>,
}));

vi.mock('@resola-ai/ui/components/DecaTable/components/ContextMenu', () => ({
  ConfirmHeaderContextMenuItem: () => <div>ConfirmHeaderContextMenuItem</div>,
  ConfirmRowContextMenuItem: () => <div>ConfirmRowContextMenuItem</div>,
  HeaderContextMenuItem: () => <div>HeaderContextMenuItem</div>,
  HeaderContextMenuSeparator: () => <div>HeaderContextMenuSeparator</div>,
}));

vi.mock('../Common/MainContainer', () => ({
  default: ({ children }: any) => <div data-testid='main-container'>{children}</div>,
}));

vi.mock('./Profile', () => ({
  default: () => <div data-testid='profile'>Profile Component</div>,
}));

vi.mock('./MergeProfile', () => ({
  MergeProfile: ({ opened }: any) =>
    opened ? <div data-testid='merge-profile'>Merge Profile</div> : null,
}));

vi.mock('./EmptyState/WorkspaceEmptyState', () => ({
  WorkspaceEmptyState: () => <div data-testid='workspace-empty-state'>Workspace Empty State</div>,
}));

vi.mock('./EmptyState/FilterEmptyState', () => ({
  FilterEmptyState: () => <div data-testid='filter-empty-state'>Filter Empty State</div>,
}));

vi.mock('./FieldSettings/AddFieldForm/AddNewColumnButton', () => ({
  default: () => <div data-testid='add-new-column-btn'>Add New Column Button</div>,
}));

vi.mock('./FieldSettings/EditFieldForm', () => ({
  default: () => <div data-testid='edit-column-field'>Edit Column Field</div>,
}));

vi.mock('./TableCellRendering/Cell', () => ({
  default: () => <div data-testid='table-cell'>Table Cell</div>,
}));

vi.mock('./TableCellRendering/EditCell', () => ({
  default: () => <div data-testid='edit-cell'>Edit Cell</div>,
}));

vi.mock('./useObjectStyles', () => ({
  default: () => ({
    classes: {
      container: 'mock-container-class',
    },
  }),
}));

vi.mock('@/utils', () => ({
  handleColumnOrderChange: vi.fn(),
  handleColumnSizingChange: vi.fn(),
  mapColumnsToView: vi.fn(() => ({
    fieldOrder: ['col1', 'col2'],
    fields: [
      { id: 'col1', size: 200 },
      { id: 'col2', size: 250 },
    ],
  })),
}));

vi.mock('@/constants/workspace', () => ({
  ColumnIcon: {},
  ColumnWidth: {},
}));

// Mock @resola-ai/ui constants to avoid theme configuration issues
vi.mock('@resola-ai/ui', () => ({
  DecaStatus: ({ text, variant }: any) => (
    <span>
      {text} ({variant})
    </span>
  ),
}));

vi.mock('@resola-ai/ui/constants', async (importOriginal) => {
  const actual = (await importOriginal()) as any;
  return {
    ...actual,
    // Override if needed
  };
});

vi.mock('@resola-ai/ui/providers', () => ({
  TableProvider: ({ children }: any) => children,
}));

describe('Workspace Component', () => {
  beforeEach(() => {
    vi.clearAllMocks();
  });

  describe('Component Structure', () => {
    it('should render the Workspace component', () => {
      expect(() => renderWithMantine(<Workspace />)).not.toThrow();
    });

    it('should wrap content with WorkspaceContextProvider', () => {
      const { container } = renderWithMantine(<Workspace />);
      expect(container).toBeInTheDocument();
    });
  });

  describe('Basic Functionality', () => {
    it('should export the Workspace component', () => {
      expect(Workspace).toBeDefined();
      expect(typeof Workspace).toBe('function');
    });

    it('should be the default export', () => {
      // Test that the component can be imported and used
      const component = Workspace;
      expect(component).toBeTruthy();
    });
  });

  describe('Component Integration', () => {
    it('should integrate with context providers', () => {
      // This test verifies the component structure and provider integration
      const { container } = renderWithMantine(<Workspace />);
      expect(container.firstChild).toBeInTheDocument();
    });

    it('should handle component lifecycle', () => {
      const { unmount } = renderWithMantine(<Workspace />);
      expect(() => unmount()).not.toThrow();
    });
  });

  describe('Error Boundaries', () => {
    it('should handle rendering without crashing', () => {
      expect(() => {
        renderWithMantine(<Workspace />);
      }).not.toThrow();
    });

    it('should be mountable and unmountable', () => {
      const { unmount } = renderWithMantine(<Workspace />);
      expect(() => unmount()).not.toThrow();
    });
  });

  describe('Component Props and Configuration', () => {
    it('should render with default configuration', () => {
      const { container } = renderWithMantine(<Workspace />);
      expect(container.firstChild).toBeTruthy();
    });

    it('should maintain component structure', () => {
      const { container } = renderWithMantine(<Workspace />);
      // The component should render without errors
      expect(container).toBeInTheDocument();
    });
  });

  describe('Mocking Verification', () => {
    it('should properly mock external dependencies', () => {
      // Verify that our mocks are working correctly
      expect(() => renderWithMantine(<Workspace />)).not.toThrow();
    });

    it('should handle context dependencies', () => {
      // Test that context providers are properly mocked
      const { container } = renderWithMantine(<Workspace />);
      expect(container).toBeDefined();
    });
  });

  describe('Component Export', () => {
    it('should export the main Workspace component', () => {
      expect(Workspace).toBeDefined();
      expect(Workspace.name).toBe('Workspace');
    });

    it('should be a valid React component', () => {
      expect(typeof Workspace).toBe('function');
    });
  });

  describe('Rendering Tests', () => {
    it('should render without props', () => {
      expect(() => renderWithMantine(<Workspace />)).not.toThrow();
    });

    it('should render consistently across multiple calls', () => {
      // Test multiple separate render calls instead of rerender
      expect(() => renderWithMantine(<Workspace />)).not.toThrow();
      expect(() => renderWithMantine(<Workspace />)).not.toThrow();
    });
  });
});
