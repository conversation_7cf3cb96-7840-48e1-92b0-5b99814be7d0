import { Flex, Text } from '@mantine/core';
import { rem } from '@mantine/core';
import { CustomImage } from '@resola-ai/ui/components/CustomImage';
import { useTranslate } from '@tolgee/react';

const NoPermissionAccess = ({ customText }: { customText?: string }) => {
  const { t } = useTranslate('common');

  return (
    <Flex
      w='100%'
      px={rem(24)}
      py={rem(8)}
      align='center'
      justify={'center'}
      gap={rem(16)}
      direction='column'
      h={'60vh'}
      data-testid='no-view-access'
    >
      <CustomImage url={'images/no_view_access.png'} />
      <Text
        c='decaGrey.9'
        fw={500}
        ta='center'
        style={{ whiteSpace: 'pre-line' }}
        data-testid='no-view-access-text'
      >
        {customText || t('errors.crmViewReadDenied')}
      </Text>
    </Flex>
  );
};

export default NoPermissionAccess;
