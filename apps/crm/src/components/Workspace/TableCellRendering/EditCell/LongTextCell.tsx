import { useWorkspaceContext } from '@/contexts/WorkspaceContext';
import { Textarea } from '@mantine/core';
import type { MRT_Cell } from 'mantine-react-table';
import { useState } from 'react';

type Props = {
  cell: MRT_Cell<any, any>;
  table: any;
};

export const LongTextCell = ({ cell, table }: Props) => {
  const [value, setValue] = useState(cell.getValue() as string);
  const { onSavingCell } = useWorkspaceContext();

  return (
    <Textarea
      autoFocus
      value={value}
      minRows={2}
      onBlur={() => {
        table.setEditingCell(false);
        cell.getValue() !== value && onSavingCell(value, cell);
      }}
      onChange={(e) => {
        setValue(e.target.value);
      }}
    />
  );
};
