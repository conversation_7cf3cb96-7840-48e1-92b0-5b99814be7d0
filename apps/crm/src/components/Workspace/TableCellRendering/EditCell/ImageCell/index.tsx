import { AssetAPI, UploadAPI } from '@/services/api';
import { downloadImage } from '@/utils';
import {
  Avatar,
  Box,
  Divider,
  FileButton,
  Flex,
  Loader,
  Menu,
  Text,
  TextInput,
  rem,
  useMantineTheme,
} from '@mantine/core';
import { CustomImageBackground, DecaButton } from '@resola-ai/ui';
import { IconDots, IconPlus } from '@tabler/icons-react';
import { useTranslate } from '@tolgee/react';
import { useState } from 'react';
type Props = {
  value: string;
  onChange: (value: string) => void;
  isEditable?: boolean;
};

export const ImageCell = ({ value, onChange, isEditable = false }: Props) => {
  const { t } = useTranslate('workspace');
  const [loading, setLoading] = useState(false);
  const theme = useMantineTheme();
  const [isValidImage, setIsValidImage] = useState(true);
  const [imageUrl, setImageUrl] = useState('');

  const validateImageUrl = (url) => {
    const img = new Image();
    img.src = url;
    img.onload = () => {
      setIsValidImage(true);
      onChange(url);
    };
    img.onerror = () => setIsValidImage(false);
  };
  const handleInputChange = (e) => {
    const url = e.target.value;
    setImageUrl(url);
  };

  const handleChange = async (file: File | null) => {
    if (!file) return;
    setLoading(true);
    if (!file) return;
    const { file: asset, uploadUrl } = await AssetAPI.save(file);
    await UploadAPI.update({ file, url: uploadUrl });
    setLoading(false);
    onChange(asset.url);
  };

  const handleDownload = () => {
    if (!value) return;
    downloadImage(value as string);
  };

  const handleDelete = () => {
    onChange('');
  };

  return (
    <>
      <Menu withinPortal defaultOpened={true} width={rem(280)}>
        <Menu.Target>
          <Flex w={'100%'} h={'100%'} align={'center'}>
            {loading ? (
              <Loader size={rem(18)} />
            ) : (
              <>
                {value ? (
                  <CustomImageBackground height={rem(28)} width={rem(28)} url={value} />
                ) : (
                  <>{isEditable ? <Avatar size={28} /> : <Text />}</>
                )}
              </>
            )}
          </Flex>
        </Menu.Target>
        {!loading && (
          <Menu.Dropdown>
            {value && !isEditable ? (
              <Flex direction={'column'} m={rem(10)}>
                <Flex justify={'space-between'}>
                  <Text sx={{}}>{t('image')}</Text>
                  <Menu shadow='md' position='right-start'>
                    <Menu.Target>
                      <IconDots size={18} />
                    </Menu.Target>
                    <Menu.Dropdown
                      sx={{ width: `${rem(255)} !important` }}
                      p={rem(12)}
                      onClick={(e) => {
                        e.stopPropagation();
                      }}
                    >
                      <Menu.Item
                        onClick={() => {
                          value && window.open(value as string, '_blank');
                        }}
                      >
                        {t('view')}
                      </Menu.Item>
                      <Menu.Item onClick={handleDownload}>{t('download')}</Menu.Item>
                      <Divider my={rem(5)} />
                      <Menu.Item
                        sx={{ color: `${theme.colors.decaRed[5]} !important` }}
                        onClick={handleDelete}
                      >
                        {t('delete')}
                      </Menu.Item>
                    </Menu.Dropdown>
                  </Menu>
                </Flex>
                <Divider mb={rem(15)} mt={rem(5)} mx={rem(-15)} />
                <Box sx={{ border: `1px ` }}>
                  <CustomImageBackground
                    height={rem(100)}
                    width={rem(100)}
                    style={{ borderRadius: rem(8) }}
                    url={value}
                  />
                </Box>
                <Divider my={rem(15)} mx={rem(-15)} />
                <FileButton onChange={handleChange}>
                  {(props) => (
                    <DecaButton {...props} variant='neutral' w={'100%'}>
                      {t('changePicture')}
                    </DecaButton>
                  )}
                </FileButton>
              </Flex>
            ) : (
              <Flex p={rem(10)} direction={'column'} gap={rem(10)} w={'100%'}>
                <Text fw={500}>{t('upload')}</Text>
                <FileButton onChange={handleChange} accept='image/png,image/jpeg'>
                  {(props) => (
                    <DecaButton {...props} leftSection={<IconPlus />} w={'100%'}>
                      {t('chooseImage')}
                    </DecaButton>
                  )}
                </FileButton>
                <Text>{t('maximumSize')}</Text>
                <Divider />
                <Text fw={500}>{t('inputFromUrl')}</Text>
                <TextInput
                  placeholder={t('enterUrl')}
                  onChange={handleInputChange}
                  onKeyDown={(e) => {
                    if (e.key === 'Enter') {
                      validateImageUrl(imageUrl);
                    }
                  }}
                />
                {isEditable && (
                  <>
                    <Divider />
                    <DecaButton variant='negative_text' onClick={handleDelete} disabled={!value}>
                      {t('delImage')}
                    </DecaButton>
                  </>
                )}
                {!isValidImage && <Text>{t('invalidImageUrl')}</Text>}
              </Flex>
            )}
          </Menu.Dropdown>
        )}
      </Menu>
    </>
  );
};
