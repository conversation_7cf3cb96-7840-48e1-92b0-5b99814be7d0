import * as WorkspaceContext from '@/contexts/WorkspaceContext';
import { renderWithMantine } from '@/tests/utils/testUtils';
import * as utils from '@/utils';
import { FieldTypes } from '@resola-ai/ui/components';
import { fireEvent, screen } from '@testing-library/react';
import { vi } from 'vitest';
import { InputCell } from './InputCell';

vi.mock('@/utils', () => ({
  validateInput: vi.fn(() => undefined),
}));

describe('InputCell', () => {
  const mockCell = {
    getValue: vi.fn(() => 'test value'),
    id: 'test-cell-id',
    column: { id: 'test' },
    row: { id: 'row-1' },
  } as any;

  const mockTable = {
    setEditingCell: vi.fn(),
  };

  const mockOnSavingCell = vi.fn();
  const mockSetValidationErrors = vi.fn();

  beforeEach(() => {
    vi.spyOn(WorkspaceContext, 'useWorkspaceContext').mockReturnValue({
      onSavingCell: mockOnSavingCell,
      validationErrors: {},
      setValidationErrors: mockSetValidationErrors,
    } as any);
  });

  afterEach(() => {
    vi.clearAllMocks();
  });

  it('renders with initial value from cell', () => {
    renderWithMantine(<InputCell cell={mockCell} table={mockTable} />);

    const input = screen.getByRole('textbox');
    expect(input).toBeInTheDocument();
    expect(input).toHaveValue('test value');
  });

  it('updates value when typing and validates input', () => {
    renderWithMantine(<InputCell cell={mockCell} table={mockTable} />);

    const input = screen.getByRole('textbox');
    fireEvent.change(input, { target: { value: 'new value' } });

    expect(input).toHaveValue('new value');
    expect(utils.validateInput).toHaveBeenCalled();
    expect(mockSetValidationErrors).toHaveBeenCalled();
  });

  it('calls onSavingCell with string value and setEditingCell on blur when valid', () => {
    renderWithMantine(<InputCell cell={mockCell} table={mockTable} />);

    const input = screen.getByRole('textbox');
    fireEvent.change(input, { target: { value: 'new value' } });
    fireEvent.blur(input);

    expect(mockOnSavingCell).toHaveBeenCalledWith('new value', mockCell);
    expect(mockTable.setEditingCell).toHaveBeenCalledWith(false);
    expect(mockSetValidationErrors).toHaveBeenCalled();
  });

  it('calls onSavingCell with numeric value for number types', () => {
    renderWithMantine(<InputCell cell={mockCell} table={mockTable} type={FieldTypes.NUMBER} />);

    const input = screen.getByRole('textbox');
    fireEvent.change(input, { target: { value: '123' } });
    fireEvent.blur(input);

    expect(mockOnSavingCell).toHaveBeenCalledWith(123, mockCell);
    expect(mockTable.setEditingCell).toHaveBeenCalledWith(false);
  });

  it('calls onSavingCell with numeric value for currency types', () => {
    renderWithMantine(<InputCell cell={mockCell} table={mockTable} type={FieldTypes.CURRENCY} />);

    const input = screen.getByRole('textbox');
    fireEvent.change(input, { target: { value: '99.99' } });
    fireEvent.blur(input);

    expect(mockOnSavingCell).toHaveBeenCalledWith(99.99, mockCell);
  });

  it('calls onSavingCell with numeric value for percent types', () => {
    renderWithMantine(<InputCell cell={mockCell} table={mockTable} type={FieldTypes.PERCENT} />);

    const input = screen.getByRole('textbox');
    fireEvent.change(input, { target: { value: '75' } });
    fireEvent.blur(input);

    expect(mockOnSavingCell).toHaveBeenCalledWith(75, mockCell);
  });

  it('handles empty value correctly', () => {
    renderWithMantine(<InputCell cell={mockCell} table={mockTable} />);

    const input = screen.getByRole('textbox');
    fireEvent.change(input, { target: { value: '' } });
    fireEvent.blur(input);

    expect(mockOnSavingCell).toHaveBeenCalledWith('', mockCell);
  });

  it('handles Enter key press to trigger blur', () => {
    renderWithMantine(<InputCell cell={mockCell} table={mockTable} />);

    const input = screen.getByRole('textbox');
    fireEvent.change(input, { target: { value: 'new value' } });
    fireEvent.keyDown(input, { key: 'Enter' });

    expect(mockOnSavingCell).toHaveBeenCalledWith('new value', mockCell);
    expect(mockTable.setEditingCell).toHaveBeenCalledWith(false);
  });

  it('does not call onSavingCell when there is a validation error', () => {
    vi.spyOn(WorkspaceContext, 'useWorkspaceContext').mockReturnValue({
      onSavingCell: mockOnSavingCell,
      validationErrors: { 'test-cell-id': 'Error message' },
      setValidationErrors: mockSetValidationErrors,
    } as any);

    renderWithMantine(<InputCell cell={mockCell} table={mockTable} />);

    const input = screen.getByRole('textbox');
    fireEvent.change(input, { target: { value: 'invalid value' } });
    fireEvent.blur(input);

    expect(mockOnSavingCell).not.toHaveBeenCalled();
    expect(mockTable.setEditingCell).toHaveBeenCalledWith(false);
  });

  it('shows error message when there is a validation error', () => {
    vi.spyOn(WorkspaceContext, 'useWorkspaceContext').mockReturnValue({
      onSavingCell: mockOnSavingCell,
      validationErrors: { 'test-cell-id': 'Error message' },
      setValidationErrors: mockSetValidationErrors,
    } as any);

    renderWithMantine(<InputCell cell={mockCell} table={mockTable} />);

    expect(screen.getByText('Error message')).toBeInTheDocument();
  });
});
