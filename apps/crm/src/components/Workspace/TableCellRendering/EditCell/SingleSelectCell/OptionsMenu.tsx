import { useWorkspaceContext } from '@/contexts/WorkspaceContext';
import type { FieldOptions } from '@/models';
import { handleDragOption } from '@/utils';
import {
  type Active,
  DndContext,
  type Over,
  PointerSensor,
  useSensor,
  useSensors,
} from '@dnd-kit/core';
import { SortableContext } from '@dnd-kit/sortable';
import { Box, Flex, Menu, Text, TextInput, rem } from '@mantine/core';
import { createStyles } from '@mantine/emotion';
import { DecaTooltip } from '@resola-ai/ui/components';
import { IconPlus } from '@tabler/icons-react';
import { useTranslate } from '@tolgee/react';
import debounce from 'lodash/debounce';
import { useCallback, useEffect, useState } from 'react';
import { v4 as uuidV4 } from 'uuid';
import Item from './Item';

type Props = {
  colId: string;
  config?: FieldOptions;
  onUpdateCell: (value: string) => void;
};

const useStyles = createStyles((theme) => ({
  search: {
    '& input': {
      outline: 'none',
      border: 'none',
    },
  },
  options: {
    overflowY: 'auto',
  },
  addOption: {
    borderRadius: rem(4),
    cursor: 'pointer',
    backgroundColor: theme.colors.decaLight[1],
  },
  addIcon: {
    color: theme.colors.decaBlue[5],
  },
  optionItem: {
    padding: rem(8),
    '&:hover': {
      borderRadius: rem(4),
      cursor: 'pointer',
      backgroundColor: theme.colors.decaLight[1],
    },
  },
}));

export const OptionsMenu = ({ colId, config, onUpdateCell }: Props) => {
  const { t } = useTranslate('workspace');
  const { handleUpdateColumn } = useWorkspaceContext();
  const { classes } = useStyles();
  const [search, setSearch] = useState('');
  const initialOptions = config?.choices || [];
  const [options, setOptions] = useState(initialOptions);

  const sensors = useSensors(
    useSensor(PointerSensor, {
      activationConstraint: {
        distance: 8,
      },
    })
  );

  useEffect(() => {
    setOptions(initialOptions);
  }, [initialOptions]);

  const debounceSearch = debounce((_value: string) => {
    const _val = _value.trim().toUpperCase();
    const _options = initialOptions.filter((option) =>
      (option.label as string).toUpperCase().includes(_val)
    );

    setOptions(_options);
  }, 300);

  const handleSearch = useCallback(
    (val: string) => {
      setSearch(val);
      return debounceSearch(val);
    },
    [debounceSearch]
  );

  const handleUpdateChoices = useCallback(
    (choices) => {
      const options = {
        ...config,
        choices,
      };
      handleUpdateColumn(colId, { options });
    },
    [colId, config, handleUpdateColumn]
  );

  const onDragEnd = useCallback(
    (active: Active, over: Over) => {
      const arrayMoved = handleDragOption(active, over, options);
      setOptions(arrayMoved);
      handleUpdateChoices(arrayMoved);
    },
    [setOptions, options, handleUpdateChoices]
  );

  const handeUpdateOption = useCallback(
    (option) => {
      const updatedOptions = initialOptions.map((opt) => {
        if (opt.id === option.id) {
          return { ...opt, ...option };
        }
        return opt;
      });
      handleUpdateChoices(updatedOptions);
      setSearch('');
    },
    [initialOptions, handleUpdateChoices]
  );

  const handleAddOption = useCallback(() => {
    const newOption = { id: uuidV4(), label: search, color: 'grey', time: new Date().getTime() };
    handleUpdateChoices([...(config?.choices || []), { ...newOption }]);
    setTimeout(() => {
      setSearch('');
    }, 10);
  }, [handleUpdateChoices, config, search, setSearch]);

  const handleRemove = useCallback(
    (id: string) => {
      const updatedOptions = initialOptions.filter((option) => option.id !== id);
      handleUpdateChoices(updatedOptions);
    },
    [handleUpdateChoices]
  );

  return (
    <Menu.Dropdown miw={rem(200)} w='auto' maw={rem(450)}>
      <TextInput
        placeholder={t('searchOrAddAnOpt')}
        value={search}
        className={classes.search}
        onChange={(e) => handleSearch(e.target.value)}
      />
      <Text c={'decaNavy.4'} p={rem(7)}>
        {t('selectOrAddAnOpt')}
      </Text>
      {search && !options.length ? (
        <Flex
          align={'center'}
          gap={rem(6)}
          py={rem(7)}
          px={rem(12)}
          className={classes.addOption}
          onClick={() => {
            handleAddOption();
          }}
        >
          <IconPlus className={classes.addIcon} size={16} />
          <Text c={'decaBlue.5'} mr={rem(10)}>
            {t('add')}
          </Text>
          <Text>{search}</Text>
        </Flex>
      ) : (
        <Flex direction={'column'} mah={rem(220)} className={classes.options}>
          <DndContext
            sensors={sensors}
            onDragStart={() => {}}
            onDragEnd={({ active, over }) => {
              if (over && active.id !== over?.id) {
                onDragEnd(active, over);
              }
            }}
            onDragCancel={() => {}}
          >
            <SortableContext items={options || []} disabled={!!search}>
              <Flex direction={'column'} mt={options?.length ? rem(10) : 0}>
                {options?.map((option, index) => {
                  const originalText = option.label || 'Name';
                  const shouldShowTooltip = originalText.length > 20;

                  return (
                    <DecaTooltip
                      key={index}
                      label={originalText}
                      disabled={!shouldShowTooltip}
                      position='left'
                      offset={10}
                    >
                      <Box
                        className={classes.optionItem}
                        onClick={() => {
                          onUpdateCell(option.id);
                        }}
                        w={'100%'}
                        maw={rem(350)}
                      >
                        <Item
                          option={option}
                          onRemove={() => handleRemove(option.id)}
                          onSave={handeUpdateOption}
                          enableColor={config?.enableColor}
                        />
                      </Box>
                    </DecaTooltip>
                  );
                })}
              </Flex>
            </SortableContext>
          </DndContext>
        </Flex>
      )}
    </Menu.Dropdown>
  );
};
