import type { FieldOptions } from '@/models';
import { renderWithMantine } from '@/tests/utils/testUtils';
import { fireEvent, screen } from '@testing-library/react';
import { beforeEach, describe, expect, it, vi } from 'vitest';
import { SingleSelectCell } from './index';

// Mock window.matchMedia to fix media query issues
Object.defineProperty(window, 'matchMedia', {
  writable: true,
  value: vi.fn().mockImplementation((query) => ({
    matches: false,
    media: query,
    onchange: null,
    addListener: vi.fn(),
    removeListener: vi.fn(),
    addEventListener: vi.fn(),
    removeEventListener: vi.fn(),
    dispatchEvent: vi.fn(),
  })),
});

// Mock DecaStatus component
vi.mock('@resola-ai/ui', () => ({
  DecaStatus: ({ text, variant, onRemove, showRemove }: any) => (
    <div data-testid='deca-status'>
      <span data-testid='status-text'>{text}</span>
      <span data-testid='status-variant'>{variant}</span>
      {showRemove && (
        <button data-testid='remove-button' onClick={onRemove}>
          Remove
        </button>
      )}
    </div>
  ),
}));

// Mock OptionsMenu component
vi.mock('./OptionsMenu', () => ({
  OptionsMenu: ({ colId, config, onUpdateCell }: any) => (
    <div data-testid='options-menu'>
      <div data-testid='options-colId'>{colId}</div>
      <div data-testid='options-config'>{JSON.stringify(config?.choices || [])}</div>
      <button data-testid='option-button-1' onClick={() => onUpdateCell('option-1')}>
        Select Option 1
      </button>
      <button data-testid='option-button-2' onClick={() => onUpdateCell('option-2')}>
        Select Option 2
      </button>
      <button data-testid='option-button-clear' onClick={() => onUpdateCell('')}>
        Clear Selection
      </button>
    </div>
  ),
}));

describe('SingleSelectCell Component', () => {
  const mockOnChange = vi.fn();
  const mockSetEditingCell = vi.fn();

  const mockCell = {
    getValue: vi.fn(),
    column: {
      id: 'test-column',
    },
  };

  const mockTable = {
    setEditingCell: mockSetEditingCell,
  };

  const mockConfig: FieldOptions = {
    choices: [
      { id: 'option-1', label: 'Option 1', color: 'blue' },
      { id: 'option-2', label: 'Option 2', color: 'green' },
      { id: 'option-3', label: 'Option 3', color: 'red' },
    ],
  };

  beforeEach(() => {
    vi.clearAllMocks();
  });

  describe('Basic Rendering', () => {
    it('should render the component with default menu opened', () => {
      mockCell.getValue.mockReturnValue('option-1');

      renderWithMantine(
        <SingleSelectCell
          cell={mockCell as any}
          table={mockTable}
          config={mockConfig}
          onChange={mockOnChange}
        />
      );

      // Should render the OptionsMenu
      expect(screen.getByTestId('options-menu')).toBeInTheDocument();
      expect(screen.getByTestId('options-colId')).toHaveTextContent('test-column');
    });

    it('should render DecaStatus when default option exists', () => {
      mockCell.getValue.mockReturnValue('option-1');

      renderWithMantine(
        <SingleSelectCell
          cell={mockCell as any}
          table={mockTable}
          config={mockConfig}
          onChange={mockOnChange}
        />
      );

      expect(screen.getByTestId('deca-status')).toBeInTheDocument();
      expect(screen.getByTestId('status-text')).toHaveTextContent('Option 1');
      expect(screen.getByTestId('status-variant')).toHaveTextContent('blue');
      expect(screen.getByTestId('remove-button')).toBeInTheDocument();
    });

    it('should render empty text when no default option exists', () => {
      mockCell.getValue.mockReturnValue('non-existent-option');

      renderWithMantine(
        <SingleSelectCell
          cell={mockCell as any}
          table={mockTable}
          config={mockConfig}
          onChange={mockOnChange}
        />
      );

      // Should not render DecaStatus
      expect(screen.queryByTestId('deca-status')).not.toBeInTheDocument();

      // Should render empty text area with specific class
      const textElement = document.querySelector('.mantine-Text-root');
      expect(textElement).toBeInTheDocument();
    });

    it('should render empty text when cell value is undefined', () => {
      mockCell.getValue.mockReturnValue(undefined);

      renderWithMantine(
        <SingleSelectCell
          cell={mockCell as any}
          table={mockTable}
          config={mockConfig}
          onChange={mockOnChange}
        />
      );

      expect(screen.queryByTestId('deca-status')).not.toBeInTheDocument();
    });
  });

  describe('Props Integration', () => {
    it('should pass correct props to OptionsMenu', () => {
      mockCell.getValue.mockReturnValue('option-1');

      renderWithMantine(
        <SingleSelectCell
          cell={mockCell as any}
          table={mockTable}
          config={mockConfig}
          onChange={mockOnChange}
        />
      );

      expect(screen.getByTestId('options-colId')).toHaveTextContent('test-column');

      const configElement = screen.getByTestId('options-config');
      expect(configElement).toHaveTextContent(JSON.stringify(mockConfig.choices));
    });

    it('should work without table prop', () => {
      mockCell.getValue.mockReturnValue('option-1');

      expect(() => {
        renderWithMantine(
          <SingleSelectCell cell={mockCell as any} config={mockConfig} onChange={mockOnChange} />
        );
      }).not.toThrow();
    });

    it('should work without config prop', () => {
      mockCell.getValue.mockReturnValue('option-1');

      expect(() => {
        renderWithMantine(
          <SingleSelectCell cell={mockCell as any} table={mockTable} onChange={mockOnChange} />
        );
      }).not.toThrow();
    });
  });

  describe('Menu Functionality', () => {
    it('should render with defaultOpened menu', () => {
      mockCell.getValue.mockReturnValue('option-1');

      renderWithMantine(
        <SingleSelectCell
          cell={mockCell as any}
          table={mockTable}
          config={mockConfig}
          onChange={mockOnChange}
        />
      );

      // Should render the OptionsMenu which indicates the menu is open
      expect(screen.getByTestId('options-menu')).toBeInTheDocument();

      // Menu target should have aria-expanded="true"
      const menuTarget = document.querySelector('[aria-expanded="true"]');
      expect(menuTarget).toBeInTheDocument();
    });
  });

  describe('Option Selection', () => {
    it('should call onChange when option is selected from OptionsMenu', () => {
      mockCell.getValue.mockReturnValue('option-1');

      renderWithMantine(
        <SingleSelectCell
          cell={mockCell as any}
          table={mockTable}
          config={mockConfig}
          onChange={mockOnChange}
        />
      );

      const optionButton = screen.getByTestId('option-button-2');
      fireEvent.click(optionButton);

      expect(mockOnChange).toHaveBeenCalledWith('option-2');
    });

    it('should call onChange when option is cleared from OptionsMenu', () => {
      mockCell.getValue.mockReturnValue('option-1');

      renderWithMantine(
        <SingleSelectCell
          cell={mockCell as any}
          table={mockTable}
          config={mockConfig}
          onChange={mockOnChange}
        />
      );

      const clearButton = screen.getByTestId('option-button-clear');
      fireEvent.click(clearButton);

      expect(mockOnChange).toHaveBeenCalledWith('');
    });

    it('should call onChange when remove button is clicked on DecaStatus', () => {
      mockCell.getValue.mockReturnValue('option-1');

      renderWithMantine(
        <SingleSelectCell
          cell={mockCell as any}
          table={mockTable}
          config={mockConfig}
          onChange={mockOnChange}
        />
      );

      const removeButton = screen.getByTestId('remove-button');
      fireEvent.click(removeButton);

      expect(mockOnChange).toHaveBeenCalledWith('');
    });
  });

  describe('Configuration Handling', () => {
    it('should handle empty choices array', () => {
      const emptyConfig: FieldOptions = {
        choices: [],
      };
      mockCell.getValue.mockReturnValue('option-1');

      renderWithMantine(
        <SingleSelectCell
          cell={mockCell as any}
          table={mockTable}
          config={emptyConfig}
          onChange={mockOnChange}
        />
      );

      // Should not render DecaStatus since no matching option exists
      expect(screen.queryByTestId('deca-status')).not.toBeInTheDocument();
      expect(screen.getByTestId('options-config')).toHaveTextContent('[]');
    });

    it('should handle missing color in option', () => {
      const configWithoutColor: FieldOptions = {
        choices: [
          { id: 'option-1', label: 'Option 1' }, // No color property
        ],
      };
      mockCell.getValue.mockReturnValue('option-1');

      renderWithMantine(
        <SingleSelectCell
          cell={mockCell as any}
          table={mockTable}
          config={configWithoutColor}
          onChange={mockOnChange}
        />
      );

      expect(screen.getByTestId('deca-status')).toBeInTheDocument();
      expect(screen.getByTestId('status-text')).toHaveTextContent('Option 1');
      // Color should be undefined/empty
      expect(screen.getByTestId('status-variant')).toHaveTextContent('');
    });

    it('should handle missing label in option', () => {
      const configWithoutLabel: FieldOptions = {
        choices: [
          { id: 'option-1', color: 'blue' }, // No label property
        ],
      };
      mockCell.getValue.mockReturnValue('option-1');

      renderWithMantine(
        <SingleSelectCell
          cell={mockCell as any}
          table={mockTable}
          config={configWithoutLabel}
          onChange={mockOnChange}
        />
      );

      expect(screen.getByTestId('deca-status')).toBeInTheDocument();
      expect(screen.getByTestId('status-variant')).toHaveTextContent('blue');
      // Label should be undefined/empty
      expect(screen.getByTestId('status-text')).toHaveTextContent('');
    });
  });

  describe('Edge Cases', () => {
    it('should handle null cell value', () => {
      mockCell.getValue.mockReturnValue(null);

      expect(() => {
        renderWithMantine(
          <SingleSelectCell
            cell={mockCell as any}
            table={mockTable}
            config={mockConfig}
            onChange={mockOnChange}
          />
        );
      }).not.toThrow();

      expect(screen.queryByTestId('deca-status')).not.toBeInTheDocument();
    });

    it('should handle empty string cell value', () => {
      mockCell.getValue.mockReturnValue('');

      renderWithMantine(
        <SingleSelectCell
          cell={mockCell as any}
          table={mockTable}
          config={mockConfig}
          onChange={mockOnChange}
        />
      );

      expect(screen.queryByTestId('deca-status')).not.toBeInTheDocument();
    });

    it('should handle missing cell column id', () => {
      const cellWithoutColumn = {
        getValue: vi.fn().mockReturnValue('option-1'),
        column: {}, // No id property
      };

      expect(() => {
        renderWithMantine(
          <SingleSelectCell
            cell={cellWithoutColumn as any}
            table={mockTable}
            config={mockConfig}
            onChange={mockOnChange}
          />
        );
      }).not.toThrow();

      expect(screen.getByTestId('options-colId')).toHaveTextContent('');
    });

    it('should handle choices with special characters in labels', () => {
      const specialConfig: FieldOptions = {
        choices: [{ id: 'option-1', label: 'Option with "quotes" & symbols!', color: 'blue' }],
      };
      mockCell.getValue.mockReturnValue('option-1');

      renderWithMantine(
        <SingleSelectCell
          cell={mockCell as any}
          table={mockTable}
          config={specialConfig}
          onChange={mockOnChange}
        />
      );

      expect(screen.getByTestId('status-text')).toHaveTextContent(
        'Option with "quotes" & symbols!'
      );
    });
  });

  describe('Accessibility', () => {
    it('should render with proper structure for screen readers', () => {
      mockCell.getValue.mockReturnValue('option-1');

      renderWithMantine(
        <SingleSelectCell
          cell={mockCell as any}
          table={mockTable}
          config={mockConfig}
          onChange={mockOnChange}
        />
      );

      // The remove button should be accessible
      const removeButton = screen.getByTestId('remove-button');
      expect(removeButton).toBeInTheDocument();
      expect(removeButton.tagName).toBe('BUTTON');
    });
  });
});
