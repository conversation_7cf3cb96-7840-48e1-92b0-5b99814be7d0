import type { FieldOptions } from '@/models';
import { Box, Flex, Menu, rem } from '@mantine/core';
import { DecaStatus } from '@resola-ai/ui';
import type { MRT_Cell } from 'mantine-react-table';
import { useCallback, useMemo } from 'react';
import { OptionsMenu } from '../SingleSelectCell/OptionsMenu';

type Props = {
  cell: MRT_Cell<any, any>;
  table?: any;
  config?: FieldOptions;
  onChange: (value: string[] | undefined, closedModal?: boolean) => void;
};

export const MultiSelectCell = ({ cell, config, onChange }: Props) => {
  const values = useMemo(() => {
    return ((cell.getValue() as string[]) || []).filter((v) => v !== '') || [];
  }, [cell, config]);

  const handleUpdateCell = useCallback(
    async (value: string) => {
      if (values.includes(value)) {
        onChange(undefined, false);
        return;
      }
      const newValues = [...values, value];
      onChange(newValues, false);
    },
    [values]
  );

  const handleRemoveOption = async (value: string) => {
    const newValues = values.filter((v) => v !== value);
    onChange(newValues, false);
  };

  const defaultOpts = useMemo(() => {
    const options = config?.choices ? config.choices : [];
    return options.filter((option) => values.includes(option.id));
  }, [cell, config]);

  return (
    <Flex
      gap={rem(10)}
      sx={{
        userSelect: 'none',
        flexWrap: 'wrap',
      }}
    >
      <Menu position={'bottom'} defaultOpened={true} onClose={() => onChange(undefined, true)}>
        <Menu.Target>
          <Box>
            {defaultOpts?.length ? (
              <Flex gap={rem(10)}>
                {defaultOpts.map((defaultOpt: any) => (
                  <DecaStatus
                    key={defaultOpt.value}
                    size='small'
                    variant={defaultOpt?.color}
                    text={defaultOpt?.label}
                    showRemove
                    onRemove={() => handleRemoveOption(defaultOpt.id)}
                  />
                ))}
              </Flex>
            ) : (
              <Flex w={rem(200)} h={rem(20)} />
            )}
          </Box>
        </Menu.Target>
        <OptionsMenu
          colId={cell.column.id}
          config={config}
          onUpdateCell={(value) => {
            handleUpdateCell(value);
          }}
        />
      </Menu>
    </Flex>
  );
};
