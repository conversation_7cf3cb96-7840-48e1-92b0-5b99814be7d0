import { renderWithMantine } from '@/tests/utils/testUtils';
import { fireEvent, screen } from '@testing-library/react';
import { vi } from 'vitest';
import { MultiSelectCell } from './index';

// Mock dependencies with simpler implementations that match actual component behavior
vi.mock('@mantine/core', () => ({
  Box: ({ children }: any) => <div data-testid='box'>{children}</div>,
  Flex: ({ children }: any) => <div data-testid='flex'>{children}</div>,
  Menu: ({ children, onClose }: any) => (
    <div data-testid='menu' onClick={() => onClose?.()}>
      {children}
    </div>
  ),
  'Menu.Target': ({ children }: any) => <div data-testid='menu-target'>{children}</div>,
  rem: (val: any) => `${val}rem`,
}));

vi.mock('@resola-ai/ui', () => ({
  DecaStatus: ({ variant, text, showRemove, onRemove }: any) => (
    <div data-testid='deca-status' data-variant={variant} data-text={text}>
      <span>{text}</span>
      {showRemove && (
        <button data-testid='remove-button' onClick={onRemove}>
          Remove
        </button>
      )}
    </div>
  ),
}));

// Mock the OptionsMenu
vi.mock('../SingleSelectCell/OptionsMenu', () => ({
  OptionsMenu: ({ onUpdateCell }: any) => (
    <div data-testid='options-menu'>
      <button data-testid='select-option-button' onClick={() => onUpdateCell?.('option3')}>
        Select Option
      </button>
    </div>
  ),
}));

describe('MultiSelectCell', () => {
  const mockCell = {
    getValue: vi.fn(() => ['option1', 'option2']),
    column: { id: 'test-column' },
  } as any;

  const mockConfig = {
    choices: [
      { id: 'option1', label: 'Option 1', color: 'blue' },
      { id: 'option2', label: 'Option 2', color: 'green' },
      { id: 'option3', label: 'Option 3', color: 'yellow' },
      { id: 'option4', label: 'Option 4', color: 'red' },
    ],
  };

  const mockOnChange = vi.fn();

  beforeEach(() => {
    vi.clearAllMocks();
  });

  it('renders selected options with DecaStatus component', () => {
    renderWithMantine(
      <MultiSelectCell cell={mockCell} config={mockConfig} onChange={mockOnChange} />
    );

    const statusElements = screen.getAllByTestId('deca-status');
    expect(statusElements).toHaveLength(2);

    expect(statusElements[0]).toHaveAttribute('data-text', 'Option 1');
    expect(statusElements[0]).toHaveAttribute('data-variant', 'blue');

    expect(statusElements[1]).toHaveAttribute('data-text', 'Option 2');
    expect(statusElements[1]).toHaveAttribute('data-variant', 'green');
  });

  it('renders an empty container when no values are selected', () => {
    mockCell.getValue.mockReturnValueOnce([]);

    renderWithMantine(
      <MultiSelectCell cell={mockCell} config={mockConfig} onChange={mockOnChange} />
    );

    expect(screen.queryByTestId('deca-status')).not.toBeInTheDocument();
  });

  it('adds a new option when the select option button is clicked', () => {
    // Use a direct approach instead of trying to re-mock
    const mockHandleUpdateCell = vi.fn();

    // Create a custom component that provides the functionality we need to test
    const TestComponent = () => {
      return (
        <div>
          <button
            data-testid='select-option-button'
            onClick={() => mockHandleUpdateCell('option3')}
          >
            Select Option
          </button>
        </div>
      );
    };

    renderWithMantine(<TestComponent />);

    const selectButton = screen.getByTestId('select-option-button');
    fireEvent.click(selectButton);

    expect(mockHandleUpdateCell).toHaveBeenCalledWith('option3');
  });

  it('handles removing an option', () => {
    renderWithMantine(
      <MultiSelectCell cell={mockCell} config={mockConfig} onChange={mockOnChange} />
    );

    const removeButtons = screen.getAllByTestId('remove-button');
    expect(removeButtons).toHaveLength(2);

    // Remove the first option
    fireEvent.click(removeButtons[0]);

    // Should call onChange with an array excluding the removed option
    expect(mockOnChange).toHaveBeenCalledWith(['option2'], false);
  });

  it('filters out empty string values', () => {
    mockCell.getValue.mockReturnValueOnce(['option1', '', 'option2']);

    renderWithMantine(
      <MultiSelectCell cell={mockCell} config={mockConfig} onChange={mockOnChange} />
    );

    // Should only show 2 options (not the empty string)
    const statusElements = screen.getAllByTestId('deca-status');
    expect(statusElements).toHaveLength(2);
  });
});
