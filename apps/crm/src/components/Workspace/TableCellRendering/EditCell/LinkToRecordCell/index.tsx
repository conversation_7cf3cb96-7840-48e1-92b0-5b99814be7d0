import { useAppContext } from '@/contexts/AppContext';
import { useRecords } from '@/hooks';
import type { FieldOptions, Record, RelationshipConfig } from '@/models';
import { Box, Flex, Loader, Menu, Text, TextInput, rem, useMantineTheme } from '@mantine/core';
import { FieldTypes } from '@resola-ai/ui/components';
import { IconX } from '@tabler/icons-react';
import { useVirtualizer } from '@tanstack/react-virtual';
import { useTranslate } from '@tolgee/react';
import { debounce } from 'lodash';
import React, {
  type BaseSyntheticEvent,
  useCallback,
  useEffect,
  useMemo,
  useState,
  useRef,
} from 'react';
import { useParams } from 'react-router-dom';
import FieldValue from './FieldValue';
type Props = {
  value: RelationshipConfig;
  config?: FieldOptions;
  onChange: (item: any) => void;
};

type LinkToRecordCellUIProps = {
  value: string;
  mainField: any;
  displayFields: any[];
  filterRecords: Record[];
  search: string;
  recordsLoading: boolean;
  onSearch: (value: string) => void;
  onClearSearch: (e: BaseSyntheticEvent) => void;
  onSaveCell: (item: Record) => void;
  onClose: () => void;
};

const LinkToRecordCellUI = React.memo(
  ({
    value,
    mainField,
    displayFields,
    filterRecords,
    search,
    recordsLoading,
    onSearch,
    onClearSearch,
    onSaveCell,
    onClose,
  }: LinkToRecordCellUIProps) => {
    const theme = useMantineTheme();
    const parentRef = useRef<HTMLDivElement>(null);
    const { t } = useTranslate('workspace');
    const virtualizer = useVirtualizer({
      count: filterRecords.length,
      getScrollElement: () => parentRef.current,
      estimateSize: () => 30,
      overscan: 3,
    });

    return (
      <Menu position='bottom' withinPortal defaultOpened={true} onClose={onClose}>
        <Menu.Target>
          <Flex h={'100%'} sx={{ userSelect: 'none' }} align={'center'}>
            {value ? <FieldValue field={mainField} value={value} /> : ''}
          </Flex>
        </Menu.Target>
        <Menu.Dropdown miw={rem(600)}>
          <TextInput
            placeholder={t('search')}
            value={search}
            sx={{
              '& input': {
                outline: 'none',
                border: 'none',
              },
            }}
            onChange={(e) => onSearch(e.target.value)}
            rightSection={search && <IconX cursor={'pointer'} size={16} onClick={onClearSearch} />}
          />
          <Box ref={parentRef} mah={rem(400)} sx={{ overflowY: 'auto' }}>
            {filterRecords.length > 0 ? (
              <Box w={'100%'} h={rem(virtualizer.getTotalSize())} pos={'relative'}>
                {virtualizer.getVirtualItems().map((virtualRow) => {
                  const item = filterRecords[virtualRow.index];
                  return (
                    <Box
                      key={virtualRow.key}
                      data-index={virtualRow.index}
                      ref={virtualizer.measureElement}
                      w={'100%'}
                      pos={'absolute'}
                      top={0}
                      left={0}
                      style={{
                        transform: `translateY(${virtualRow.start}px)`,
                      }}
                    >
                      <Menu.Item onClick={() => onSaveCell(item)}>
                        <Flex gap={rem(20)}>
                          {displayFields.map((field) => (
                            <Flex
                              key={field.id}
                              direction={'column'}
                              w={field.type === FieldTypes.DATETIME ? rem(170) : rem(120)}
                              gap={rem(5)}
                              sx={{
                                '&:first-of-type': {
                                  borderRight: `1px solid ${theme.colors.decaLight[4]}`,
                                  paddingRight: rem(20),
                                },
                              }}
                            >
                              <Text>{field.name}</Text>
                              <FieldValue field={field} value={item?.[field.id] || ''} />
                            </Flex>
                          ))}
                        </Flex>
                      </Menu.Item>
                    </Box>
                  );
                })}
              </Box>
            ) : (
              <Text p='sm' ta='center' c='dimmed'>
                {t('filterNoResultFound')}
              </Text>
            )}
            {recordsLoading && filterRecords.length > 0 && (
              <Flex justify='center' p='xs'>
                <Loader size='sm' />
              </Flex>
            )}
          </Box>
        </Menu.Dropdown>
      </Menu>
    );
  }
);

export const LinkToRecordCell = React.memo(({ value: initValue, config, onChange }: Props) => {
  const { wsId } = useParams();
  const { objects } = useAppContext();
  const objectId = config?.objectId;

  // Memoize fields to avoid recalculating on every render
  const fields = useMemo(
    () => objects?.find((o) => o.id === objectId)?.fields || [],
    [objects, objectId]
  );

  const fieldId = config?.fieldId;

  // Memoize mainField
  const mainField = useMemo(() => fields.find((field) => field.id === fieldId), [fields, fieldId]);

  // Define search state
  const [search, setSearch] = useState('');

  // Combined state for filtered records
  const [filterRecords, setFilterRecords] = useState<Record[]>([]);

  // Calculate displayFields once fields change
  const displayFields = useMemo(() => {
    if (!fields?.length || !mainField) return [];
    const remainingFields = fields.filter((field) => field.id !== fieldId).slice(0, 4);
    return [mainField, ...remainingFields];
  }, [fields, fieldId, mainField]);

  // Use the hook with minimal parameters
  const { records, recordsLoading } = useRecords(wsId, objectId, false);

  // Memoize the full filtered dataset to avoid recalculating on every render
  const filteredDataset = useMemo(() => {
    if (!records || !records.length) return [];

    if (!search || search.length < 2 || !mainField?.id) return records;

    const searchTerm = search.trim().toUpperCase();
    return records.filter(
      (item) => item[mainField.id] && String(item[mainField.id]).toUpperCase().includes(searchTerm)
    );
  }, [records, search, mainField?.id]);

  // Update filtered records when filtered dataset or page changes
  useEffect(() => {
    if (!filteredDataset.length) {
      setFilterRecords([]);
      return;
    }
    setFilterRecords(filteredDataset);
  }, [filteredDataset]);

  // Create stable debounce function reference with useRef to avoid recreating on each render
  const debounceSearch = useRef(
    debounce((value: string) => {
      setSearch(value);
    }, 300)
  ).current;

  // Create stable search handler
  const handleSearch = useCallback(
    (val: string) => {
      debounceSearch(val);
      return val; // Return for immediate UI update
    },
    [debounceSearch]
  );

  const clearSearch = useCallback((e: BaseSyntheticEvent) => {
    e.stopPropagation();
    setSearch('');
  }, []);

  // Memoize the current value
  const value = useMemo(() => {
    const recordId = initValue?.recordId;
    if (!recordId || !mainField?.id) return '';

    // First try to find in current filtered records to avoid loading all
    const foundRecord = filterRecords.find((record) => record.id === recordId);
    if (foundRecord) return foundRecord[mainField.id] || '';

    // If not found in current filtered records, look in all records
    const foundInAllRecords = records?.find((record) => record.id === recordId);
    return foundInAllRecords?.[mainField.id] || '';
  }, [initValue?.recordId, mainField?.id, filterRecords, records]);

  const handleSavingCell = useCallback(
    (item: Record) => {
      onChange({
        fieldId,
        recordId: item.id,
      });
    },
    [onChange, fieldId]
  );

  // Show loading indicator only for initial load, not when loading more
  if (recordsLoading && filterRecords.length === 0) {
    return <Loader size='sm' />;
  }

  return (
    <LinkToRecordCellUI
      value={value}
      mainField={mainField}
      displayFields={displayFields}
      filterRecords={filterRecords}
      search={search}
      recordsLoading={recordsLoading}
      onSearch={(val) => setSearch(handleSearch(val))}
      onClearSearch={clearSearch}
      onSaveCell={handleSavingCell}
      onClose={() => onChange(undefined)}
    />
  );
});

LinkToRecordCell.displayName = 'LinkToRecordCell';
LinkToRecordCellUI.displayName = 'LinkToRecordCellUI';
