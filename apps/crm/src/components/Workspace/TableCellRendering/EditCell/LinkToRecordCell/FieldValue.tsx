import type { ObjectColumn } from '@/models';
import { Avatar, Box } from '@mantine/core';
import { DecaCheckbox } from '@resola-ai/ui';
import { FieldTypes } from '@resola-ai/ui/components';
import isEmpty from 'lodash/isEmpty';
import React from 'react';
import { MultiSelectCell } from '../../Cell/MultiSelectCell';
import { SingleSelectCell } from '../../Cell/SingleSelectCell';

const FieldValue = ({ field, value: fieldValue }: { field?: ObjectColumn; value?: any }) => {
  const renderItem = () => {
    switch (field?.type) {
      case FieldTypes.CHECKBOX:
        return <DecaCheckbox checked={fieldValue as boolean} readOnly />;
      case FieldTypes.SINGLE_SELECT: {
        const choices = field?.options?.choices || [];
        return <SingleSelectCell value={fieldValue} choices={choices} />;
      }
      case FieldTypes.IMAGE:
        return <Avatar src={fieldValue} alt='avatar' />;
      case FieldTypes.CREATED_BY:
      case FieldTypes.MODIFIED_BY:
        return <Avatar src={fieldValue?.picture} alt='avatar' radius={'100%'} />;
      case FieldTypes.MULTI_SELECT: {
        const choices = field?.options?.choices || [];
        return (
          <MultiSelectCell showRemove={false} choices={choices || []} value={fieldValue || []} />
        );
      }
      default:
        return (
          <Box
            sx={{
              height: '20px',
              textOverflow: 'ellipsis',
              overflow: 'hidden',
              whiteSpace: 'nowrap',
              maxWidth: '230px',
            }}
          >
            {FieldTypes.RELATIONSHIP === field?.type ? (
              <>
                {typeof fieldValue === 'object' && !isEmpty(fieldValue) && 'recordId' in fieldValue
                  ? fieldValue.recordId
                  : null}
              </>
            ) : (
              fieldValue
            )}
          </Box>
        );
    }
  };

  return <Box sx={{ userSelect: 'none' }}>{renderItem()}</Box>;
};

export default React.memo(FieldValue);
