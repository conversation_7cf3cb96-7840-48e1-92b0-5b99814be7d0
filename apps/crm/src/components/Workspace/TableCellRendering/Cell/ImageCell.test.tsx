import { ImageCell } from '@/components/Workspace/TableCellRendering/Cell/ImageCell';
import { renderWithMantine } from '@/tests/utils/testUtils';

describe('ImageCell', () => {
  it('renders image component when value is provided', () => {
    const cell = { getValue: () => 'http://example.com/image.png' };
    const { container } = renderWithMantine(<ImageCell cell={cell as any} />);
    expect(container.firstChild).toBeTruthy();
  });

  it('renders empty when no value', () => {
    const cell = { getValue: () => '' };
    const { queryByTestId } = renderWithMantine(<ImageCell cell={cell as any} />);
    expect(queryByTestId('image-background')).not.toBeInTheDocument();
  });
});
