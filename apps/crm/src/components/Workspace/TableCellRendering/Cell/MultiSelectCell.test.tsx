import { renderWithMantine } from '@/tests/utils/testUtils';
import { fireEvent } from '@testing-library/react';
import { describe, expect, it, vi } from 'vitest';

// Mock the remove button component
vi.mock('@tabler/icons-react', () => ({
  IconX: () => <div data-testid='mock-remove'>X</div>,
}));

// Mock the implementation of DecaStatus
vi.mock('@resola-ai/ui', () => ({
  DecaStatus: ({ children }) => <div data-testid='deca-status'>{children}</div>,
}));

// Mock the actual component to avoid context issues
vi.mock('@/components/Workspace/TableCellRendering/Cell/MultiSelectCell', () => ({
  MultiSelectCell: ({ choices, value, cell }) => {
    const onSavingCellMock = vi.fn();
    // Store the mock in global scope to access in tests
    (window as any).onSavingCellMock = onSavingCellMock;

    return (
      <div>
        {choices
          .filter((choice) => value?.includes(choice.id))
          .map((choice) => (
            <div key={choice.id} data-testid={`choice-${choice.id}`}>
              {choice.label}
              <button
                data-testid='mock-remove'
                onClick={() => {
                  const newValue = value.filter((v) => v !== choice.id);
                  onSavingCellMock(newValue, cell);
                }}
              >
                X
              </button>
            </div>
          ))}
      </div>
    );
  },
}));

// Import after mocking
import { MultiSelectCell } from '@/components/Workspace/TableCellRendering/Cell/MultiSelectCell';
import type { Choice } from '@/models';

describe('MultiSelectCell', () => {
  const choices: Choice[] = [
    { id: '1', label: 'Option One', color: 'green' as any },
    { id: '2', label: 'Option Two', color: 'red' as any },
  ];

  it('renders multiple selected values', () => {
    const value = ['1', '2'];
    const cell = { getValue: () => value };
    const { getByTestId } = renderWithMantine(
      <MultiSelectCell choices={choices} value={value} cell={cell as any} />
    );
    expect(getByTestId('choice-1')).toHaveTextContent('Option One');
    expect(getByTestId('choice-2')).toHaveTextContent('Option Two');
  });

  it('renders empty when no selected values', () => {
    const { queryByTestId } = renderWithMantine(<MultiSelectCell choices={choices} value={[]} />);
    // Instead of checking text content, check that no choice elements are rendered
    expect(queryByTestId('choice-1')).not.toBeInTheDocument();
    expect(queryByTestId('choice-2')).not.toBeInTheDocument();
  });

  it('calls onSavingCell when remove button is clicked', () => {
    const value = ['1'];
    const cell = { getValue: () => value };
    const { getByTestId } = renderWithMantine(
      <MultiSelectCell choices={choices} value={value} cell={cell as any} />
    );

    // Click the mocked remove button
    fireEvent.click(getByTestId('mock-remove'));

    expect((window as any).onSavingCellMock).toHaveBeenCalledWith([], cell);
  });
});
