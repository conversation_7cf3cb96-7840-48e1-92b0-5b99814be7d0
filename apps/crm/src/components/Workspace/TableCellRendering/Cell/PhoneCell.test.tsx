import { PhoneCell } from '@/components/Workspace/TableCellRendering/Cell/PhoneCell';
import { renderWithMantine } from '@/tests/utils/testUtils';

describe('PhoneCell', () => {
  it('renders the phone number when value is provided', () => {
    const { getByText } = renderWithMantine(<PhoneCell value='1234567890' />);
    expect(getByText('1234567890')).toBeInTheDocument();
  });

  it('renders empty string when value is empty', () => {
    const { queryByText } = renderWithMantine(<PhoneCell value='' />);
    expect(queryByText(/./)).not.toBeInTheDocument();
  });
});
