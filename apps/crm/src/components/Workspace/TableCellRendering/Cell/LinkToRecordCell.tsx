import { useAppContext } from '@/contexts/AppContext';
import type { FieldOptions, ObjectColumn, RelationshipConfig } from '@/models';
import React from 'react';
import FieldValue from '../EditCell/LinkToRecordCell/FieldValue';

type Props = {
  objectValue?: RelationshipConfig;
  config?: FieldOptions;
};

const Cell = ({ objectValue, config, fields }: Props & { fields: ObjectColumn[] }) => {
  const fieldId = config?.fieldId;
  const mainField = fields.find((field) => field.id === fieldId);

  return <FieldValue field={mainField} value={objectValue?.value} />;
};
const LinkToRecordCellUI = ({ config, objectValue }: Props) => {
  const { objects } = useAppContext();
  const object = objects?.find((obj) => obj.id === config?.objectId);

  if (object?.fields?.length) {
    return <Cell config={config} objectValue={objectValue} fields={object?.fields} />;
  }

  return <></>;
};

export const LinkToRecordCell = React.memo(LinkToRecordCellUI);
LinkToRecordCell.displayName = 'LinkToRecordCell';
