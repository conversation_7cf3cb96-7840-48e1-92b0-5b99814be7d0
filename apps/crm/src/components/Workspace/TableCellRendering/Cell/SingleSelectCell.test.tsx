import { renderWithMantine } from '@/tests/utils/testUtils';
import { fireEvent } from '@testing-library/react';
import { describe, expect, it, vi } from 'vitest';

// Mock the remove button component
vi.mock('@tabler/icons-react', () => ({
  IconX: () => <div data-testid='mock-remove'>X</div>,
}));

// Mock the actual component to avoid context issues
vi.mock('@/components/Workspace/TableCellRendering/Cell/SingleSelectCell', () => ({
  SingleSelectCell: ({ value, choices, cell, showRemove }) => {
    const onSavingCellMock = vi.fn();
    // Store the mock in global scope to access in tests
    (window as any).onSavingCellMock = onSavingCellMock;

    const selectedChoice = choices.find((choice) => choice.id === value);

    return (
      <div>
        {selectedChoice && (
          <div data-testid={`choice-${selectedChoice.id}`}>
            {selectedChoice.label}
            {showRemove && (
              <button data-testid='mock-remove' onClick={() => onSavingCellMock('', cell)}>
                X
              </button>
            )}
          </div>
        )}
      </div>
    );
  },
}));

// Import after mocking
import { SingleSelectCell } from '@/components/Workspace/TableCellRendering/Cell/SingleSelectCell';

describe('SingleSelectCell', () => {
  const choices = [
    { id: '1', label: 'Option One', color: 'green' as any },
    { id: '2', label: 'Option Two', color: 'red' as any },
  ];

  it('renders the selected choice', () => {
    const { getByTestId } = renderWithMantine(
      <SingleSelectCell value='1' choices={choices} showRemove={false} />
    );
    expect(getByTestId('choice-1')).toHaveTextContent('Option One');
  });

  it('renders nothing when no matching choice', () => {
    const { queryByTestId } = renderWithMantine(
      <SingleSelectCell value='3' choices={choices} showRemove={false} />
    );
    // Check that none of the choice elements are rendered
    expect(queryByTestId('choice-1')).not.toBeInTheDocument();
    expect(queryByTestId('choice-2')).not.toBeInTheDocument();
  });

  it('calls onSavingCell when remove button is clicked', () => {
    const cell = { getValue: () => '1' };
    const { getByTestId } = renderWithMantine(
      <SingleSelectCell value='1' choices={choices} cell={cell as any} showRemove={true} />
    );

    // Click the mocked remove button
    fireEvent.click(getByTestId('mock-remove'));
    expect((window as any).onSavingCellMock).toHaveBeenCalledWith('', cell);
  });
});
