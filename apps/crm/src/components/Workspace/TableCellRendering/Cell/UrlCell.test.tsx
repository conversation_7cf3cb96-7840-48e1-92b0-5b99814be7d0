import { UrlCell } from '@/components/Workspace/TableCellRendering/Cell/UrlCell';
import { renderWithMantine } from '@/tests/utils/testUtils';
import { screen } from '@testing-library/react';
import { BrowserRouter } from 'react-router-dom';

describe('UrlCell', () => {
  it('renders link with correct href and text', () => {
    const cell = { getValue: () => 'http://example.com' };
    renderWithMantine(
      <BrowserRouter>
        <UrlCell cell={cell as any} />
      </BrowserRouter>
    );
    const link = screen.getByRole('link');
    expect(link).toHaveAttribute('href', 'http://example.com');
    expect(link).toHaveTextContent('http://example.com');
    expect(link).toHaveAttribute('target', '_blank');
    expect(link).toHaveClass('url-cell');
  });

  it('renders empty when value is empty', () => {
    const cell = { getValue: () => '' };
    const { queryByRole } = renderWithMantine(
      <BrowserRouter>
        <UrlCell cell={cell as any} />
      </BrowserRouter>
    );
    expect(queryByRole('link')).not.toBeInTheDocument();
  });
});
