import { LongTextCell } from '@/components/Workspace/TableCellRendering/Cell/LongTextCell';
import { renderWithMantine } from '@/tests/utils/testUtils';

describe('LongTextCell', () => {
  it('renders the text with ellipsis styling', () => {
    const text = 'This is a long text that should be truncated with ellipsis';
    const { getByText } = renderWithMantine(<LongTextCell value={text} />);
    // Should render the full text but CSS will apply ellipsis overflow
    expect(getByText(text)).toBeInTheDocument();
  });

  it('renders empty when value is empty', () => {
    const { queryByText } = renderWithMantine(<LongTextCell value='' />);
    expect(queryByText(/./)).not.toBeInTheDocument();
  });
});
