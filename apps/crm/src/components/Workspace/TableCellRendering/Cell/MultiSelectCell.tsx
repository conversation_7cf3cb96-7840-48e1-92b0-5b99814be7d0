import { useWorkspaceContext } from '@/contexts/WorkspaceContext';
import type { Choice } from '@/models';
import { Flex, rem } from '@mantine/core';
import { DecaStatus } from '@resola-ai/ui';
import type { MRT_Cell } from 'mantine-react-table';
import React, { useMemo } from 'react';

interface Props {
  cell?: MRT_Cell<any, any>;
  showRemove?: boolean;
  choices: Choice[];
  value: string[];
}

const MultiSelectCellUI = ({ value, cell, showRemove = true, choices }: Props) => {
  const { onSavingCell } = useWorkspaceContext();

  const values = useMemo(() => {
    return value?.filter((v) => v !== '');
  }, [value]);

  const defaultOpts = useMemo(() => {
    return choices?.filter((option) => values?.includes(option.id)) || [];
  }, [choices, values]);

  const handleRemoveOption = async (value: string) => {
    const newValues = values.filter((v) => v !== value);
    cell && (await onSavingCell(newValues, cell));
  };

  return (
    <div>
      {defaultOpts?.length ? (
        <Flex
          sx={{
            userSelect: 'none',
            flexWrap: 'wrap',
            WebkitLineClamp: 1,
            overflow: 'hidden',
            textOverflow: 'ellipsis',
            display: '-webkit-box',
            WebkitBoxOrient: 'vertical',
          }}
          gap={rem(5)}
        >
          {defaultOpts.map((defaultOpt: any) => (
            <DecaStatus
              key={defaultOpt.id}
              size='small'
              variant={defaultOpt?.color}
              text={defaultOpt?.label}
              showRemove={showRemove}
              onRemove={() => handleRemoveOption(defaultOpt.id)}
              sx={{ marginRight: rem(10) }}
            />
          ))}
        </Flex>
      ) : null}
    </div>
  );
};

export const MultiSelectCell = React.memo(MultiSelectCellUI);
