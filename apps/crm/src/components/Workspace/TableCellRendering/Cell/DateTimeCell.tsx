import type { FieldOptions } from '@/models';
import { getDateTime } from '@/utils';
import React from 'react';
import { useTranslation } from 'react-i18next';

interface Props {
  config?: FieldOptions;
  value?: string | string[];
}

const DateTimeCellUI = ({ config, value }: Props) => {
  const { i18n } = useTranslation();

  // Handle date range display
  if (config?.dateRange && Array.isArray(value)) {
    const [startValue, endValue] = value;
    if (!startValue && !endValue) return <></>;

    const startDate = startValue
      ? getDateTime(
          config?.date?.format,
          config?.time?.enabled ? config?.time?.format : '',
          config?.timezone?.format,
          config?.displayTimezone,
          startValue,
          i18n.language === 'en' ? 'en' : 'ja'
        )
      : '';

    const endDate = endValue
      ? getDateTime(
          config?.date?.format,
          config?.time?.enabled ? config?.time?.format : '',
          config?.timezone?.format,
          config?.displayTimezone,
          endValue,
          i18n.language === 'en' ? 'en' : 'ja'
        )
      : '';

    return <>{startDate && endDate ? `${startDate} - ${endDate}` : startDate || endDate}</>;
  }

  // Handle single date display
  const singleValue = Array.isArray(value) ? value[0] : value;
  const date = getDateTime(
    config?.date?.format,
    config?.time?.enabled ? config?.time?.format : '',
    config?.timezone?.format,
    config?.displayTimezone,
    singleValue,
    i18n.language === 'en' ? 'en' : 'ja'
  );

  return <>{singleValue ? date : ''}</>;
};

export const DateTimeCell = React.memo(DateTimeCellUI);
