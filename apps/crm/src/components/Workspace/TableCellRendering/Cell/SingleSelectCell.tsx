import { useWorkspaceContext } from '@/contexts/WorkspaceContext';
import type { Choice } from '@/models';
import { Box } from '@mantine/core';
import { DecaStatus } from '@resola-ai/ui';
import type { MRT_Cell } from 'mantine-react-table';
import React from 'react';

interface Props {
  value: string;
  choices: Choice[];
  cell?: MRT_Cell<any, any>;
  showRemove?: boolean;
}

const SingleSelectCellUI = ({ value, choices, cell, showRemove }: Props) => {
  const { onSavingCell } = useWorkspaceContext();
  const defaultOpt = choices?.find((o) => o.id === value);

  const handleRemoveOption = async (_: string) => {
    cell && (await onSavingCell('', cell));
  };

  return (
    <div>
      {defaultOpt ? (
        <Box sx={{ userSelect: 'none' }}>
          <DecaStatus
            size='small'
            variant={defaultOpt?.color}
            text={defaultOpt?.label}
            showRemove={showRemove}
            onRemove={() => handleRemoveOption(defaultOpt.id)}
          />
        </Box>
      ) : null}
    </div>
  );
};

export const SingleSelectCell = React.memo(SingleSelectCellUI);
