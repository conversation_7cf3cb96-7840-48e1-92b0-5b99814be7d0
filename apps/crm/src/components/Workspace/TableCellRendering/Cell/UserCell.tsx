import { Text, useMantineTheme } from '@mantine/core';
import { Box, Flex, rem } from '@mantine/core';
import { CustomImageBackground } from '@resola-ai/ui';
import type { MRT_Cell } from 'mantine-react-table';
import React from 'react';

type User = {
  name: string;
  email: string;
  picture?: string;
};

const UserCellUI = ({ cell }: { cell: MRT_Cell<any, any> }) => {
  const theme = useMantineTheme();
  const renderCell = () => {
    const user = cell.getValue() as User;
    if (user?.name) {
      return (
        <Flex gap={rem(10)} align={'center'}>
          {user?.picture ? (
            <CustomImageBackground url={user?.picture} rounded width={rem(24)} height={rem(24)} />
          ) : (
            <Box
              w={rem(24)}
              h={rem(24)}
              sx={{ border: `1px solid ${theme.colors.decaLight[5]}`, borderRadius: '100%' }}
            />
          )}
          <Text
            sx={{
              whiteSpace: 'nowrap',
              overflow: 'hidden',
              textOverflow: 'ellipsis',
              width: 'calc(100% - 34px)',
            }}
          >
            {user.name}
          </Text>
        </Flex>
      );
    }
    return null;
  };

  return <div>{renderCell()}</div>;
};

export const UserCell = React.memo(UserCellUI);
