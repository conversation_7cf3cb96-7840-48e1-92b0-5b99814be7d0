import { Currency, CurrencyFormatTypes, CurrencySymbol } from '@/constants/workspace';
import type { FieldOptions } from '@/models';
import { getCurrencyFormat } from '@/utils';
import React, { useMemo } from 'react';

interface Props {
  config?: FieldOptions;
  value: string;
}

const CurrencyCellUI = ({ config, value: cellValue }: Props) => {
  const value = useMemo(() => {
    if (!cellValue) {
      return <></>;
    }
    const symbol = CurrencySymbol[config?.currency ?? Currency.yen];
    const separatorFormat = config?.separator?.enabled
      ? config?.separator?.format
      : CurrencyFormatTypes.periodOnly;

    return getCurrencyFormat(+cellValue, symbol, separatorFormat, config?.decimalPlaces);
  }, [config, cellValue]);

  return <>{value ?? ''}</>;
};

export const CurrencyCell = React.memo(CurrencyCellUI);
