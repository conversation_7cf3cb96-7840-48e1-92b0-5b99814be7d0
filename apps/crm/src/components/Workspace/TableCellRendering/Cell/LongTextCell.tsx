import { Text, rem } from '@mantine/core';
import React from 'react';

const LongTextCellUI = ({ value }: { value: string }) => {
  return (
    <Text
      fz={rem(12)}
      sx={{
        textOverflow: 'ellipsis',
        overflow: 'hidden',
        whiteSpace: 'nowrap',
        maxWidth: rem(230),
      }}
    >
      {value}
    </Text>
  );
};

export const LongTextCell = React.memo(LongTextCellUI);
