import type { ObjectColumn, RelationshipConfig } from '@/models';
import { Box } from '@mantine/core';
import { FieldTypes } from '@resola-ai/ui/components';
import type { MRT_Cell } from 'mantine-react-table';
import React from 'react';
import { CheckboxCell } from './CheckboxCell';
import { CurrencyCell } from './CurrencyCell';
import { DateTimeCell } from './DateTimeCell';
import { ImageCell } from './ImageCell';
import { LinkToRecordCell } from './LinkToRecordCell';
import { LongTextCell } from './LongTextCell';
import { MultiSelectCell } from './MultiSelectCell';
import { NumberCell } from './NumberCell';
import { PercentCell } from './PercentCell';
import { PhoneCell } from './PhoneCell';
import { SingleSelectCell } from './SingleSelectCell';
import { UrlCell } from './UrlCell';
import { UserCell } from './UserCell';

type Props = {
  cell: MRT_Cell<any, any>;
  type: string;
  column?: ObjectColumn;
};

const Cell = ({ cell, type, column }: Props) => {
  const config = column?.options;
  const renderCell = () => {
    switch (type) {
      case FieldTypes.CHECKBOX:
        return <CheckboxCell cell={cell} type={type} />;
      case FieldTypes.SINGLE_SELECT:
        return (
          <SingleSelectCell
            choices={config?.choices || []}
            value={cell.getValue() as string}
            cell={cell}
            showRemove={true}
          />
        );
      case FieldTypes.MULTI_SELECT:
        return (
          <MultiSelectCell
            cell={cell}
            choices={config?.choices || []}
            value={(cell.getValue() as string[]) || []}
          />
        );
      case FieldTypes.CREATED_TIME:
      case FieldTypes.MODIFIED_TIME:
      case FieldTypes.DATETIME:
        // For date range fields, construct array with start and end values
        if (config?.dateRange) {
          const record = cell.row.original;
          const fieldId = cell.column.id;
          const startValue = record[fieldId];
          const endValue = record[`${fieldId}_end`];
          const rangeValue = [startValue, endValue].filter(Boolean);
          return <DateTimeCell config={config} value={rangeValue as string[]} />;
        }
        return <DateTimeCell config={config} value={cell.getValue() as string} />;
      case FieldTypes.CREATED_BY:
      case FieldTypes.MODIFIED_BY:
        return <UserCell cell={cell} />;
      case FieldTypes.IMAGE:
        return <ImageCell cell={cell} />;
      case FieldTypes.URL:
        return <UrlCell config={config} cell={cell} />;
      case FieldTypes.LONG_TEXT:
        return <LongTextCell value={cell.getValue() as string} />;
      case FieldTypes.NUMBER:
        return <NumberCell value={cell.getValue() as string} config={config} />;
      case FieldTypes.PHONE_NUMBER:
        return <PhoneCell value={cell.getValue() as string} config={config} />;
      case FieldTypes.CURRENCY:
        return <CurrencyCell config={config} value={cell.getValue() as string} />;
      case FieldTypes.RELATIONSHIP:
        return (
          <LinkToRecordCell config={config} objectValue={cell.getValue() as RelationshipConfig} />
        );
      case FieldTypes.PERCENT:
        return <PercentCell config={config} value={cell.getValue() as string} />;
      default:
        if (typeof cell.getValue() === 'object') {
          return '';
        }
        return <>{cell.getValue()}</>;
    }
  };
  return (
    <Box sx={{ userSelect: 'none' }} w={'100%'}>
      {renderCell()}
    </Box>
  );
};

export default React.memo(Cell);

export * from './CheckboxCell';
export * from './CurrencyCell';
export * from './DateTimeCell';
export * from './ImageCell';
export * from './LinkToRecordCell';
export * from './LongTextCell';
export * from './MultiSelectCell';
export * from './NumberCell';
export * from './PercentCell';
export * from './PhoneCell';
export * from './SingleSelectCell';
export * from './UrlCell';
export * from './UserCell';
