import { CurrencyCell } from '@/components/Workspace/TableCellRendering/Cell/CurrencyCell';
import { renderWithMantine } from '@/tests/utils/testUtils';
import { screen } from '@testing-library/react';

describe('CurrencyCell', () => {
  it('renders formatted currency when value is provided', () => {
    const config = {
      currency: 'usd',
      separator: { enabled: true, format: 'commaPeriod' },
      decimalPlaces: 2,
    };
    renderWithMantine(<CurrencyCell config={config} value='1234.567' />);
    expect(screen.getByText('$1,234.57')).toBeInTheDocument();
  });

  it('renders empty string when value is empty', () => {
    const { queryByText } = renderWithMantine(<CurrencyCell config={{} as any} value='' />);
    expect(queryByText(/./)).not.toBeInTheDocument();
  });
});
