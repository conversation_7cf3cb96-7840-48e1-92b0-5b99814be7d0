import type { FieldOptions } from '@/models';
import { getCurrencyFormat } from '@/utils';
import { Box, Progress, rem, useMantineTheme } from '@mantine/core';
import React from 'react';

interface Props {
  config?: FieldOptions;
  value: string;
}

const CircleProgressBar = ({ progress = 0 }: { progress: number }) => {
  const radius = 15; // Radius of the circle
  const strokeWidth = 3; // Width of the stroke
  const normalizedRadius = radius - strokeWidth * 2; // Adjusted radius for stroke
  const circumference = normalizedRadius * 2 * Math.PI; // Circumference of the circle
  const strokeDashoffset = circumference - (progress / 100) * circumference; // Offset for progress
  const theme = useMantineTheme();

  return (
    <svg height={radius * 2} width={radius * 2} style={{ transform: 'rotate(-90deg)' }}>
      <circle
        stroke={theme.colors.decaNavy[3]}
        fill='transparent'
        strokeWidth={strokeWidth}
        r={normalizedRadius}
        cx={radius}
        cy={radius}
      />
      <circle
        stroke={theme.colors.decaNavy[4]}
        fill='transparent'
        strokeWidth={strokeWidth}
        strokeDasharray={`${circumference} ${circumference}`}
        style={{ strokeDashoffset }}
        r={normalizedRadius}
        cx={radius}
        cy={radius}
      />
    </svg>
  );
};

const PercentCellUI = ({ config, value: cellValue }: Props) => {
  if (!cellValue) {
    return <></>;
  }

  const value = +(cellValue || 0);

  const renderValue = () => {
    if (config?.presentation?.enabled) {
      return config?.presentation?.type === 'bar' ? (
        <Progress color='decaGreen.6' value={value} mt={rem(6)} />
      ) : (
        <CircleProgressBar progress={+value} />
      );
    }
    const precision = config?.decimalPlaces || 1;
    const separatorFormat = config?.separator?.enabled ? config?.separator?.format : 'commaPeriod';

    return getCurrencyFormat(value, '%', separatorFormat, precision, true);
  };

  return <Box w={'100%'}>{renderValue()}</Box>;
};

export const PercentCell = React.memo(PercentCellUI);
