import Cell from '@/components/Workspace/TableCellRendering/Cell';
import { renderWithMantine } from '@/tests/utils/testUtils';

describe('Cell component default rendering', () => {
  it('renders primitive value for unknown type', () => {
    const cell = { getValue: () => 'primitive value' };
    const { getByText } = renderWithMantine(<Cell cell={cell as any} type='UNKNOWN' />);
    expect(getByText('primitive value')).toBeInTheDocument();
  });

  it('renders empty for object value on unknown type', () => {
    const cell = { getValue: () => ({ foo: 'bar' }) };
    const { queryByText } = renderWithMantine(<Cell cell={cell as any} type='UNKNOWN' />);
    expect(queryByText(/./)).not.toBeInTheDocument();
  });
});
