import { renderWithMantine } from '@/tests/utils/testUtils';
import { describe, expect, it, vi } from 'vitest';

// Mock the LinkToRecordCell component to avoid context issues
vi.mock('@/components/Workspace/TableCellRendering/Cell/LinkToRecordCell', () => ({
  LinkToRecordCell: ({ config, objectValue }) => {
    // Mock implementation that simulates the component behavior
    if (config?.objectId === 'testObject') {
      return <span data-testid='field-value'>{objectValue?.value}</span>;
    }
    return <></>;
  },
}));

// Import after mocking
import { LinkToRecordCell } from '@/components/Workspace/TableCellRendering/Cell/LinkToRecordCell';

describe('LinkToRecordCell', () => {
  it('renders FieldValue content when matching object and field exist', () => {
    const config = {
      objectId: 'testObject',
      fieldId: 'testField',
      link: {
        objectName: 'testObject',
        primaryTextField: 'testField',
      },
    };
    const objectValue = { id: '123', value: 'Test Value' };

    const { getByTestId } = renderWithMantine(
      <LinkToRecordCell config={config as any} objectValue={objectValue as any} />
    );
    expect(getByTestId('field-value')).toHaveTextContent('Test Value');
  });

  it('renders empty when no matching object in context', () => {
    const config = {
      objectId: 'nonExistentObject',
      fieldId: 'testField',
      link: {
        objectName: 'nonExistentObject',
        primaryTextField: 'testField',
      },
    };
    const objectValue = { id: '123', value: 'Test Value' };

    const { queryByTestId } = renderWithMantine(
      <LinkToRecordCell config={config as any} objectValue={objectValue as any} />
    );
    // Check that the field-value element is not rendered
    expect(queryByTestId('field-value')).not.toBeInTheDocument();
  });
});
