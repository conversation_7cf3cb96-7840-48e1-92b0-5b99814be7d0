import { DateTimeCell } from '@/components/Workspace/TableCellRendering/Cell/DateTimeCell';
import { renderWithMantine } from '@/tests/utils/testUtils';

describe('DateTimeCell', () => {
  it('renders date string when value is provided', () => {
    const config = {
      date: { format: 'YYYY/MM/DD' },
      time: { enabled: false, format: '' },
      timezone: { format: 'UTC' },
      displayTimezone: false,
    };
    const { container } = renderWithMantine(
      <DateTimeCell config={config as any} value='2023-01-01T00:00:00Z' />
    );
    expect(container.textContent).toBeTruthy();
  });

  it('renders empty string when value is undefined', () => {
    const { queryByText } = renderWithMantine(<DateTimeCell config={{} as any} />);
    expect(queryByText(/./)).not.toBeInTheDocument();
  });
});
