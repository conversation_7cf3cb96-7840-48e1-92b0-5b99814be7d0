import { rem } from '@mantine/core';
import { CustomImageBackground } from '@resola-ai/ui';
import type { MRT_Cell } from 'mantine-react-table';
import React from 'react';

type Props = {
  cell: MRT_Cell<any, any>;
};

const ImageCellUI = ({ cell }: Props) => {
  return cell.getValue() ? (
    <CustomImageBackground
      height={rem(28)}
      width={rem(28)}
      url={cell.getValue()}
      data-testid='image-background'
    />
  ) : (
    <></>
  );
};

export const ImageCell = React.memo(ImageCellUI);
