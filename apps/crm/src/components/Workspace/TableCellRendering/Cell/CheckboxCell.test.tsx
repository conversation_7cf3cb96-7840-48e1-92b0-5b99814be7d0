import { renderWithMantine } from '@/tests/utils/testUtils';
import { fireEvent } from '@testing-library/react';
import React from 'react';
import { describe, expect, it, vi } from 'vitest';

// Mock the actual component to avoid context issues
vi.mock('@/components/Workspace/TableCellRendering/Cell/CheckboxCell', () => ({
  CheckboxCell: ({ cell }) => {
    const [checked, setChecked] = React.useState(cell?.getValue() || false);
    const onSavingCellMock = vi.fn((newValue, _cellArg) => {
      // This will ensure our state gets updated
      setChecked(newValue);
    });

    return (
      <input
        type='checkbox'
        data-testid='checkbox-cell'
        checked={checked}
        onChange={(e) => {
          const newValue = e.target.checked;
          onSavingCellMock(newValue, cell);
          // Store the mock in global scope to access in tests
          (window as any).onSavingCellMock = onSavingCellMock;
        }}
      />
    );
  },
}));

// Import after mocking
import { CheckboxCell } from '@/components/Workspace/TableCellRendering/Cell/CheckboxCell';

describe('CheckboxCell', () => {
  it('renders checked when cell value is true', () => {
    const cell = { getValue: () => true };
    const { getByTestId } = renderWithMantine(<CheckboxCell cell={cell as any} type='checkbox' />);
    expect(getByTestId('checkbox-cell')).toBeChecked();
  });

  it('renders unchecked when cell value is false', () => {
    const cell = { getValue: () => false };
    const { getByTestId } = renderWithMantine(<CheckboxCell cell={cell as any} type='checkbox' />);
    expect(getByTestId('checkbox-cell')).not.toBeChecked();
  });

  it('calls onSavingCell and updates checked state on click', () => {
    const cell = { getValue: () => false };
    const { getByTestId } = renderWithMantine(<CheckboxCell cell={cell as any} type='checkbox' />);
    const checkbox = getByTestId('checkbox-cell');

    // Set checked to be true
    fireEvent.click(checkbox);

    // Access the mock from the window object
    expect((window as any).onSavingCellMock).toHaveBeenCalledWith(true, cell);
    expect(checkbox).toBeChecked();
  });
});
