import { NumberCell } from '@/components/Workspace/TableCellRendering/Cell/NumberCell';
import { renderWithMantine } from '@/tests/utils/testUtils';

describe('NumberCell', () => {
  it('renders formatted number when decimal format', () => {
    const config = { numberFormat: 'decimal', decimalPlaces: 2 };
    const { container } = renderWithMantine(<NumberCell config={config as any} value='123.456' />);
    const divContent = container.querySelector('div')?.textContent;
    expect(divContent).toBe('123.46');
  });

  it('renders integer when integer format', () => {
    const config = { numberFormat: 'integer' }; // decimalPlaces ignored
    const { container } = renderWithMantine(<NumberCell config={config as any} value='789.123' />);
    const divContent = container.querySelector('div')?.textContent;
    expect(divContent).toBe('789');
  });

  it('renders empty when no value', () => {
    const { queryByText } = renderWithMantine(<NumberCell config={{} as any} value='' />);
    expect(queryByText(/./)).not.toBeInTheDocument();
  });
});
