import type { FieldOptions } from '@/models';
import type { MRT_Cell } from 'mantine-react-table';
import React from 'react';
import { Link } from 'react-router-dom';

const UrlCellUI = ({ cell }: { config?: FieldOptions; cell: MRT_Cell<any, any> }) => {
  const value = cell.getValue() as string;

  if (!value) {
    return null;
  }

  return (
    <Link to={value} target='_blank' className='url-cell'>
      {value}
    </Link>
  );
};

export const UrlCell = React.memo(UrlCellUI);
