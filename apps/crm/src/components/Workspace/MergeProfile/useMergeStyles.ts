import { rem } from '@mantine/core';
import { createStyles } from '@mantine/emotion';

export const useMergeStyles = createStyles((theme) => ({
  drawer: {
    '.mantine-Drawer-header': {
      display: 'none',
    },
    '.mantine-Drawer-body': {
      borderTop: `${rem(1)} solid ${theme.colors.gray[2]}`,
      padding: `${rem(16)} !important`,
    },
    '.mantine-Drawer-content': {
      height: 'fit-content',
    },
  },
  radioGroup: {
    margin: `${rem(10)} 0`,
    backgroundColor: theme.colors.decaLight[0],
    padding: `${rem(10)} 0`,
    borderRadius: theme.radius.md,
    border: `1px solid ${theme.colors.decaLight[1]}`,
  },
  fieldGroup: {
    borderRadius: theme.radius.md,
    border: `1px solid ${theme.colors.decaLight[1]}`,
    borderCollapse: 'unset',
  },
  tableWrapper: {
    borderRadius: theme.radius.md,
    border: `1px solid ${theme.colors.gray[2]}`,
    overflow: 'hidden',
  },
  table: {
    width: '100%',
    borderCollapse: 'separate',
    borderSpacing: '0',
    fontSize: rem(14),

    '& thead': {
      backgroundColor: theme.colors.decaLight[0],
    },
    '& thead > tr > th': {
      fontWeight: 500,
      textAlign: 'center',
    },
    '& td, & th': {
      padding: rem(12),
    },
    '& tbody > tr': {
      height: rem(50),
    },
    '& tbody > tr > td': {
      border: 'none',
      '& div': {
        fontSize: `${rem(14)} !important`,
      },
    },
    '& tbody tr:nth-of-type(even)': {
      backgroundColor: theme.colors.decaLight[0],
    },

    '& .mantine-Radio-labelWrapper': {
      width: '80%',
    },
    '& .mantine-Radio-inner': {
      alignSelf: 'center',
    },
  },
  preferencesTable: {
    '& tbody > tr': {
      height: 'unset',
    },
  },
  link: {
    color: theme.colors.blue[6],
    cursor: 'pointer',
    fontWeight: 500,
  },
  badge: {
    textTransform: 'none',
  },
}));
