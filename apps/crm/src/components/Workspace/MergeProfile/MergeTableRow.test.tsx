import type { ObjectColumn } from '@/models/workspace';
import { renderWithMantine } from '@/tests/utils/testUtils';
import { FieldTypes } from '@resola-ai/ui/components';
import { fireEvent, screen } from '@testing-library/react';
import { beforeEach, describe, expect, it, vi } from 'vitest';
import { MergeProfileContextProvider } from './MergeProfileContext';
import { MergeTableRow } from './MergeTableRow';

// Mock required hooks and components
const mockUseWorkspaceContext = vi.fn();
const mockUseMergeProfileContext = vi.fn();

vi.mock('@/contexts/WorkspaceContext', () => ({
  useWorkspaceContext: () => mockUseWorkspaceContext(),
}));

vi.mock('./MergeProfileContext', () => ({
  MergeProfileContextProvider: ({ children }) => children,
  useMergeProfileContext: () => mockUseMergeProfileContext(),
  ALLOWED_OVERWRITE_TYPES: ['text', 'multi-select', 'number', 'phone', 'email', 'url', 'image'],
  OverwriteValue: 'overwrite',
}));

// Mock useMergeStyles
vi.mock('./useMergeStyles', () => ({
  useMergeStyles: () => ({
    classes: {
      link: 'link',
    },
  }),
}));

// Mock RenderProfileTypes
vi.mock('../Profile/ObjectFields/RenderProfileTypes', () => ({
  RenderProfileTypes: ({ field }) => <div>{field.mapValue}</div>,
}));

vi.mock('@tolgee/react', () => ({
  useTranslate: () => ({
    t: (key) => {
      if (key === 'upTo50Characters') return 'upTo50Characters';
      if (key === '8to15Digits') return '8to15Digits';
      if (key === 'uploadImage') return 'uploadImage';
      return key;
    },
  }),
}));

// Mock Mantine components
vi.mock('@mantine/core', () => ({
  Box: ({ children, ...props }) => <div {...props}>{children}</div>,
  Radio: ({ checked, onChange, label, value, name, ...props }) => (
    <input
      type='radio'
      checked={checked}
      onChange={onChange}
      value={value}
      name={name}
      data-testid={props['data-testid']}
      {...props}
    />
  ),
  TextInput: ({ value, onChange, placeholder, ...props }) => (
    <input
      type='text'
      value={value}
      onChange={onChange}
      placeholder={placeholder}
      data-testid={props['data-testid']}
      {...props}
    />
  ),
  FileButton: ({ children, onChange }) => (
    <div>
      {children({ onClick: () => onChange(new File([], 'test.jpg', { type: 'image/jpeg' })) })}
    </div>
  ),
  rem: (value) => value,
}));

// Mock UI components
vi.mock('@resola-ai/ui', () => ({
  CustomImageBackground: ({ url, ...props }) => <div {...props}>{url}</div>,
  ErrorMessage: ({ message, ...props }) => (
    <div data-testid='error-message' {...props}>
      {message}
    </div>
  ),
}));

// Mock API calls
vi.mock('@/services/api', () => ({
  AssetAPI: {
    save: vi.fn().mockResolvedValue({ file: { url: 'test.jpg' }, uploadUrl: 'test.jpg' }),
  },
  UploadAPI: {
    update: vi.fn().mockResolvedValue({}),
  },
}));

describe('MergeTableRow Component', () => {
  const mockSelectedRowDetails = [
    {
      id: 'record-1',
      name: 'Record 1',
      field1: 'Value 1',
      field2: 100,
      recordIndex: 1,
    },
    {
      id: 'record-2',
      name: 'Record 2',
      field1: 'Value 2',
      field2: 200,
      recordIndex: 2,
    },
  ];

  const mockColumn: ObjectColumn = {
    id: 'field1',
    name: 'Field 1',
    type: 'text',
    options: { isPrimary: true },
    header: 'Field 1',
  };

  beforeEach(() => {
    vi.clearAllMocks();
    mockUseWorkspaceContext.mockReturnValue({
      selectedRowDetails: mockSelectedRowDetails,
    });

    mockUseMergeProfileContext.mockReturnValue({
      mergeSelectionData: {
        field1: { id: 'record-1', type: 'text' },
      },
      handleSelect: vi.fn(),
      overwriteRecord: {},
      handleOverwriteChange: vi.fn(),
      setIsLoading: vi.fn(),
      inputErrors: {},
      setInputErrors: vi.fn(),
    });
  });

  it('renders the row correctly', () => {
    renderWithMantine(
      <MergeProfileContextProvider>
        <MergeTableRow col={mockColumn} />
      </MergeProfileContextProvider>
    );
    expect(screen.getByTestId('merge-table-row-field1')).toBeInTheDocument();
  });

  it('renders radio buttons for each record', () => {
    renderWithMantine(
      <MergeProfileContextProvider>
        <MergeTableRow col={mockColumn} />
      </MergeProfileContextProvider>
    );
    expect(screen.getByTestId('merge-radio-field1-record-1')).toBeInTheDocument();
    expect(screen.getByTestId('merge-radio-field1-record-2')).toBeInTheDocument();
  });

  it('handles radio button selection', () => {
    const handleSelect = vi.fn();
    mockUseMergeProfileContext.mockReturnValue({
      ...mockUseMergeProfileContext(),
      handleSelect,
    });

    renderWithMantine(
      <MergeProfileContextProvider>
        <MergeTableRow col={mockColumn} />
      </MergeProfileContextProvider>
    );

    const radioButton = screen.getByTestId('merge-radio-field1-record-2');
    fireEvent.click(radioButton);

    expect(handleSelect).toHaveBeenCalledWith('field1', FieldTypes.SINGLE_LINE_TEXT, 'record-2');
  });

  it('renders overwrite option for allowed field types', () => {
    renderWithMantine(
      <MergeProfileContextProvider>
        <MergeTableRow col={mockColumn} />
      </MergeProfileContextProvider>
    );
    expect(screen.getByTestId('merge-text-input-field1')).toBeInTheDocument();
  });

  it('handles overwrite field selection', () => {
    const handleSelect = vi.fn();

    mockUseMergeProfileContext.mockReturnValue({
      ...mockUseMergeProfileContext(),
      handleSelect,
      mergeSelectionData: {
        field1: { id: 'record-1', type: FieldTypes.SINGLE_LINE_TEXT },
      },
    });

    renderWithMantine(
      <MergeProfileContextProvider>
        <MergeTableRow col={mockColumn} />
      </MergeProfileContextProvider>
    );

    const overwriteRadio = screen.getByTestId('merge-radio-field1-overwrite');
    fireEvent.click(overwriteRadio);

    expect(handleSelect).toHaveBeenCalledWith('field1', FieldTypes.SINGLE_LINE_TEXT, 'overwrite');
  });

  it('displays error messages for invalid inputs', () => {
    mockUseMergeProfileContext.mockReturnValue({
      ...mockUseMergeProfileContext(),
      mergeSelectionData: {
        field1: { id: 'overwrite', type: FieldTypes.SINGLE_LINE_TEXT },
      },
      inputErrors: {
        field1: 'Invalid input',
      },
    });

    renderWithMantine(
      <MergeProfileContextProvider>
        <MergeTableRow col={mockColumn} />
      </MergeProfileContextProvider>
    );

    const errorMessage = screen.getByTestId('error-message');
    expect(errorMessage).toBeInTheDocument();
    expect(errorMessage).toHaveTextContent('Invalid input');
  });

  it('handles input changes for overwrite fields', () => {
    const handleOverwriteChange = vi.fn();
    const setInputErrors = vi.fn();
    mockUseMergeProfileContext.mockReturnValue({
      ...mockUseMergeProfileContext(),
      handleOverwriteChange,
      setInputErrors,
      mergeSelectionData: {
        field1: { id: 'overwrite', type: FieldTypes.SINGLE_LINE_TEXT },
      },
    });

    renderWithMantine(
      <MergeProfileContextProvider>
        <MergeTableRow col={mockColumn} />
      </MergeProfileContextProvider>
    );

    const input = screen.getByTestId('merge-text-input-field1');
    fireEvent.change(input, { target: { value: 'new value' } });

    expect(handleOverwriteChange).toHaveBeenCalled();
  });

  it('handles number field type with validation', () => {
    const handleOverwriteChange = vi.fn();
    const setInputErrors = vi.fn();
    const numberColumn: ObjectColumn = {
      ...mockColumn,
      type: FieldTypes.NUMBER,
    };

    mockUseMergeProfileContext.mockReturnValue({
      ...mockUseMergeProfileContext(),
      handleOverwriteChange,
      setInputErrors,
      mergeSelectionData: {
        field1: { id: 'overwrite', type: FieldTypes.NUMBER },
      },
    });

    renderWithMantine(
      <MergeProfileContextProvider>
        <MergeTableRow col={numberColumn} />
      </MergeProfileContextProvider>
    );

    const input = screen.getByTestId('merge-text-input-field1');
    fireEvent.change(input, { target: { value: '123' } });

    expect(handleOverwriteChange).toHaveBeenCalled();
  });

  it('handles email field type with validation', () => {
    const handleOverwriteChange = vi.fn();
    const setInputErrors = vi.fn();
    const emailColumn: ObjectColumn = {
      ...mockColumn,
      type: FieldTypes.EMAIL,
    };

    mockUseMergeProfileContext.mockReturnValue({
      ...mockUseMergeProfileContext(),
      handleOverwriteChange,
      setInputErrors,
      mergeSelectionData: {
        field1: { id: 'overwrite', type: FieldTypes.EMAIL },
      },
    });

    renderWithMantine(
      <MergeProfileContextProvider>
        <MergeTableRow col={emailColumn} />
      </MergeProfileContextProvider>
    );

    const input = screen.getByTestId('merge-text-input-field1');
    fireEvent.change(input, { target: { value: '<EMAIL>' } });

    expect(handleOverwriteChange).toHaveBeenCalled();
  });

  it('handles phone number field type with validation', () => {
    const handleOverwriteChange = vi.fn();
    const setInputErrors = vi.fn();
    const phoneColumn: ObjectColumn = {
      ...mockColumn,
      type: FieldTypes.PHONE_NUMBER,
    };

    mockUseMergeProfileContext.mockReturnValue({
      ...mockUseMergeProfileContext(),
      handleOverwriteChange,
      setInputErrors,
      mergeSelectionData: {
        field1: { id: 'overwrite', type: FieldTypes.PHONE_NUMBER },
      },
    });

    renderWithMantine(
      <MergeProfileContextProvider>
        <MergeTableRow col={phoneColumn} />
      </MergeProfileContextProvider>
    );

    const input = screen.getByTestId('merge-text-input-field1');
    fireEvent.change(input, { target: { value: '**********' } });

    expect(handleOverwriteChange).toHaveBeenCalled();
  });

  it('handles URL field type with validation', () => {
    const handleOverwriteChange = vi.fn();
    const setInputErrors = vi.fn();
    const urlColumn: ObjectColumn = {
      ...mockColumn,
      type: FieldTypes.URL,
    };

    mockUseMergeProfileContext.mockReturnValue({
      ...mockUseMergeProfileContext(),
      handleOverwriteChange,
      setInputErrors,
      mergeSelectionData: {
        field1: { id: 'overwrite', type: FieldTypes.URL },
      },
    });

    renderWithMantine(
      <MergeProfileContextProvider>
        <MergeTableRow col={urlColumn} />
      </MergeProfileContextProvider>
    );

    const input = screen.getByTestId('merge-text-input-field1');
    fireEvent.change(input, { target: { value: 'https://example.com' } });

    expect(handleOverwriteChange).toHaveBeenCalled();
  });

  it('renders image upload button when field type is image', () => {
    const imageColumn: ObjectColumn = {
      ...mockColumn,
      type: FieldTypes.IMAGE,
    };

    mockUseMergeProfileContext.mockReturnValue({
      ...mockUseMergeProfileContext(),
      mergeSelectionData: {
        field1: { id: 'overwrite', type: FieldTypes.IMAGE },
      },
      overwriteRecord: {},
    });

    renderWithMantine(
      <MergeProfileContextProvider>
        <MergeTableRow col={imageColumn} />
      </MergeProfileContextProvider>
    );

    expect(screen.getByTestId('merge-file-button-field1')).toBeInTheDocument();
    expect(screen.getByText('uploadImage')).toBeInTheDocument();
  });

  it('displays uploaded image when image is selected', () => {
    const imageColumn: ObjectColumn = {
      ...mockColumn,
      type: FieldTypes.IMAGE,
    };

    mockUseMergeProfileContext.mockReturnValue({
      ...mockUseMergeProfileContext(),
      mergeSelectionData: {
        field1: { id: 'overwrite', type: FieldTypes.IMAGE },
      },
      overwriteRecord: {
        field1: 'test.jpg',
      },
    });

    renderWithMantine(
      <MergeProfileContextProvider>
        <MergeTableRow col={imageColumn} />
      </MergeProfileContextProvider>
    );

    expect(screen.getByText('test.jpg')).toBeInTheDocument();
  });
});
