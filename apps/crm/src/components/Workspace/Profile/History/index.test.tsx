import { useWorkspaceContext } from '@/contexts/WorkspaceContext';
import type { IHistory } from '@/models';
import { renderWithMantine } from '@/tests/utils/testUtils';
import { screen } from '@testing-library/react';
import { useTranslate } from '@tolgee/react';
import dayjs from 'dayjs';
import { useParams } from 'react-router-dom';
import useSWR from 'swr';
import { beforeEach, describe, expect, it, vi } from 'vitest';
import History from './index';

// Mock SWR
vi.mock('swr', () => ({
  __esModule: true,
  default: vi.fn(),
}));

// Mock hooks
vi.mock('react-router-dom', () => ({
  useParams: vi.fn(),
}));

vi.mock('@/contexts/WorkspaceContext', () => ({
  useWorkspaceContext: vi.fn(),
}));

// Mock Tolgee
vi.mock('@tolgee/react');
vi.mock('@tolgee/web/tools');

// Mock AppConfig
vi.mock('@/configs', () => ({
  default: {
    TOLGEE_TOOLS_ENABLED: false,
    TOLGEE_URL: 'https://example.com',
    TOLGEE_KEY: 'test-key',
  },
}));

// Mock shared constants
vi.mock('@resola-ai/shared-constants', () => ({
  DEFAULT_LANGUAGE: 'en',
}));

// Mock local tolgee module
vi.mock('@/tolgee/index', () => ({
  tolgee: {
    use: vi.fn().mockReturnThis(),
    init: vi.fn().mockReturnThis(),
    run: vi.fn(),
  },
}));

// Mock APIs
vi.mock('@/services/api', () => ({
  HistoryAPI: {
    getList: vi.fn(),
  },
}));

// Mock dayjs to control date comparisons
vi.mock('dayjs', () => {
  const dayjsMock = vi.fn(() => ({
    isSame: vi.fn().mockReturnValue(false),
    format: vi.fn().mockReturnValue('01/01/2023 - 10:00:00 AM'),
  }));

  return {
    __esModule: true,
    default: dayjsMock,
  };
});

// Mock RenderProfileTypes component
vi.mock('../ObjectFields/RenderProfileTypes', () => ({
  RenderProfileTypes: ({ field }) => {
    return <span data-testid='profile-type-renderer'>{field.mapValue || 'No value'}</span>;
  },
  ProfileData: {},
}));

describe('History Component', () => {
  const mockT = vi.fn((key) => key);
  const mockFields = [
    { id: 'name', name: 'Full Name', type: 'text' },
    { id: 'email', name: 'Email Address', type: 'email' },
    { id: 'avatar', name: 'Profile Image', type: 'image' },
    { id: 'record', name: 'Record', type: 'text' },
  ];

  // Create properly typed mock history items
  const mockHistories: IHistory[] = [
    {
      recordId: 'record-1',
      action: 'UPDATE_FIELD',
      fieldId: 'name',
      changes: {
        oldValue: 'John Doe',
        newValue: { name: 'John Smith' },
      },
      user: {
        id: 'user-1',
        email: '<EMAIL>',
        name: 'John',
        picture: 'https://example.com/avatar.jpg',
      },
      sentAt: '2023-01-01T10:00:00.000Z',
    },
    {
      recordId: 'record-2',
      action: 'ATTACHMENT_CREATED',
      fieldId: 'file',
      options: {
        id: 'file-1',
        path: '/path/to/file',
        title: 'Document.pdf',
      },
      user: {
        id: 'user-2',
        email: '<EMAIL>',
        name: 'Jane',
        picture: 'https://example.com/jane.jpg',
      },
      sentAt: '2023-01-02T10:00:00.000Z',
    },
    {
      recordId: 'record-3',
      action: 'CREATE_RECORD',
      fieldId: 'record',
      changes: {
        oldValue: '',
        newValue: { record: 'New Record Created' },
      },
      user: {
        id: 'user-1',
        email: '<EMAIL>',
        name: 'John',
        picture: 'https://example.com/avatar.jpg',
      },
      sentAt: '2023-01-03T10:00:00.000Z',
    },
  ];

  beforeEach(() => {
    vi.clearAllMocks();

    // Setup mock return values
    (useParams as unknown as ReturnType<typeof vi.fn>).mockReturnValue({
      wsId: 'workspace-1',
      id: 'object-1',
      recordId: 'record-1',
    });

    (useWorkspaceContext as unknown as ReturnType<typeof vi.fn>).mockReturnValue({
      object: {
        fields: mockFields,
      },
    });

    (useTranslate as unknown as ReturnType<typeof vi.fn>).mockReturnValue({
      t: mockT,
    });

    (useSWR as unknown as ReturnType<typeof vi.fn>).mockImplementation(() => {
      return {
        data: mockHistories,
        error: undefined,
        isLoading: false,
      };
    });
  });

  it('renders the history title', () => {
    renderWithMantine(<History />);

    expect(screen.getByText('history')).toBeInTheDocument();
  });

  it('renders history items correctly', () => {
    renderWithMantine(<History />);

    // Check if history items are rendered
    expect(screen.getAllByText(/01\/01\/2023 - 10:00:00 AM/).length).toBe(3);

    // Check if user names are displayed
    expect(screen.getAllByText('John:').length).toBe(2);
    expect(screen.getByText('Jane:')).toBeInTheDocument();

    // Check action names
    expect(screen.getByText('updatedField')).toBeInTheDocument();
    expect(screen.getByText('createdAttachment')).toBeInTheDocument();
    expect(screen.getByText('createdProfile')).toBeInTheDocument();
  });

  it('renders image avatar when user has a picture', () => {
    renderWithMantine(<History />);

    const avatars = document.querySelectorAll('img');
    expect(avatars.length).toBeGreaterThan(0);
  });

  it('handles field attachments correctly', () => {
    renderWithMantine(<History />);

    // For attachment created, it should display the attachment title
    expect(screen.getByText('Document.pdf')).toBeInTheDocument();
  });

  it('handles object changes correctly', () => {
    renderWithMantine(<History />);

    // Check if field name is displayed
    expect(screen.getByText('Full Name:')).toBeInTheDocument();

    // Check if profile renderer is used
    const renderers = screen.getAllByTestId('profile-type-renderer');
    expect(renderers.length).toBeGreaterThan(0);
  });

  it('renders string value changes correctly', () => {
    // Modified history item with string value as an object property
    const stringValueHistory: IHistory[] = [...mockHistories];
    stringValueHistory[2] = {
      ...mockHistories[2],
      changes: {
        oldValue: '',
        newValue: { record: 'Plain text value' },
      },
    };

    (useSWR as unknown as ReturnType<typeof vi.fn>).mockReturnValueOnce({
      data: stringValueHistory,
      error: undefined,
      isLoading: false,
    });

    renderWithMantine(<History />);

    // Look for rendered value in the profile-type-renderer
    const renderers = screen.getAllByTestId('profile-type-renderer');
    const plainTextRenderer = Array.from(renderers).find(
      (element) => element.textContent === 'Plain text value'
    );
    expect(plainTextRenderer).toBeInTheDocument();
  });

  it('handles empty history data', () => {
    (useSWR as unknown as ReturnType<typeof vi.fn>).mockReturnValueOnce({
      data: undefined,
      error: undefined,
      isLoading: false,
    });

    renderWithMantine(<History />);

    // Should still render the title but no history items
    expect(screen.getByText('history')).toBeInTheDocument();
    expect(screen.queryByText('John:')).not.toBeInTheDocument();
  });

  it('displays today for current day items', () => {
    // Update the mock implementation for this test only
    (dayjs as unknown as ReturnType<typeof vi.fn>).mockImplementationOnce(() => ({
      isSame: vi.fn().mockReturnValue(true),
      format: vi.fn().mockReturnValue('10:00:00 AM'),
    }));

    renderWithMantine(<History />);

    expect(mockT).toHaveBeenCalledWith('today');
  });

  it('shows image for image URLs in field values', () => {
    // Modified history item with image value in an object property
    const imageValueHistory: IHistory[] = [...mockHistories];
    imageValueHistory[0] = {
      ...mockHistories[0],
      changes: {
        oldValue: '',
        newValue: { avatar: 'https://example.com/image.jpg' },
      },
      fieldId: 'avatar',
    };

    (useSWR as unknown as ReturnType<typeof vi.fn>).mockReturnValueOnce({
      data: imageValueHistory,
      error: undefined,
      isLoading: false,
    });

    renderWithMantine(<History />);

    // Should render avatar component
    const avatarImages = document.querySelectorAll('.mantine-Avatar-root');
    expect(avatarImages.length).toBeGreaterThan(0);
  });
});
