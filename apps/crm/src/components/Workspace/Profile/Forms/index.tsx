import { useWorkspaceContext } from '@/contexts/WorkspaceContext';
import { useBreadcrumbNavigation } from '@/hooks/useBreadcrumbNavigation';
import type { Record, WSObject } from '@/models';
import { ObjectAPI } from '@/services/api';
import { syncContextData } from '@/utils';
import {
  Accordion,
  ActionIcon,
  Box,
  Button,
  Flex,
  Loader,
  ScrollArea,
  Stack,
  Text,
  rem,
} from '@mantine/core';
import { createStyles } from '@mantine/emotion';
import { Modal } from '@resola-ai/ui';
import { FieldTypes } from '@resola-ai/ui/components';
import { IconPin, IconPinnedOff } from '@tabler/icons-react';
import { useTranslate } from '@tolgee/react';
import dayjs from 'dayjs';
import DOMPurify from 'dompurify';
import { startCase } from 'lodash';
import React, { useCallback, useEffect, useRef, useState } from 'react';
import { useParams } from 'react-router-dom';

type Form = {
  linkedObj?: WSObject;
  records?: Record[];
  template?: string;
};

const useStyles = createStyles((theme) => ({
  root: {
    border: 'none',
  },

  item: {
    border: 'none',
    padding: `${rem(10)} ${rem(0)}`,
    '&[data-active]': {
      zIndex: 1,
    },
  },
  control: {
    width: 'fit-content',
    ['.mantine-Accordion-label']: {
      padding: `${rem(4)} ${rem(12)}`,
      backgroundColor: theme.colors.decaYellow[1],
      borderRadius: rem(4),
      fontSize: rem(16),
    },
    ['&:hover']: {
      backgroundColor: 'unset',
    },
  },
  panel: {
    padding: `0 ${rem(5)}`,
  },
  content: {
    padding: `${rem(5)} ${rem(8)} `,
  },
  chevron: {
    transform: 'rotate(-90deg)',
    '&[data-rotate]': {
      transform: 'rotate(0deg)',
    },
  },
  header: {
    wordBreak: 'break-word',
    '.go-to-object': {
      visibility: 'hidden',
      borderRadius: 4,
      whiteSpace: 'nowrap',
      cursor: 'pointer',
    },
    '&:hover': {
      '.go-to-object': {
        visibility: 'visible',
      },
    },
  },
  pinnedFormContainer: {
    backgroundColor: 'white',
    borderRadius: rem(16),
    padding: rem(16),
    border: `1px solid ${theme.colors.gray[2]}`,
  },
  formHeader: {
    display: 'flex',
    alignItems: 'center',
    justifyContent: 'space-between',
    marginBottom: rem(16),
  },
}));
const Forms = ({ form }: { form: any }) => {
  const formRef = useRef<any>();
  const { classes } = useStyles();
  const { t } = useTranslate('workspace');
  const linkedObj = form?.linkedObj;
  const { navigateToLinkedRecord } = useBreadcrumbNavigation();
  const { object, refetchObject } = useWorkspaceContext();
  const [isUnpinModalOpen, setIsUnpinModalOpen] = useState(false);

  // Get workspace ID from URL params
  const { wsId } = useParams<{ wsId: string }>();

  // Check if this form's linked object is already pinned
  const isPinned =
    object?.childObjects?.find((childObj) => childObj.id === linkedObj?.id)?.pinned || false;

  const handleTogglePin = useCallback(async () => {
    if (!object || !linkedObj?.id || !wsId) return;

    try {
      // Update the childObjects array
      const updatedChildObjects =
        object.childObjects?.map((childObj) => {
          if (childObj.id === linkedObj.id) {
            return { ...childObj, pinned: !isPinned };
          }
          return childObj;
        }) || [];

      // Update the object with the modified childObjects
      await ObjectAPI.update(wsId, {
        ...object,
        childObjects: updatedChildObjects,
      });

      // Refetch the object to get the updated data
      refetchObject();
    } catch (error) {
      console.error('Failed to update pin status:', error);
    }
  }, [object, linkedObj?.id, isPinned, refetchObject, wsId]);

  useEffect(() => {
    if (formRef.current) {
      const toggleButtons = formRef.current.querySelectorAll('.toggle-btn');
      toggleButtons.forEach((toggleButton) => {
        toggleButton.addEventListener('click', () => {
          const formId = toggleButton.getAttribute('data-id');
          const formDetails = document.getElementById(`form-details-${formId}`);
          const chevronDown = toggleButton.querySelector('.chevron-down');
          const chevronUp = toggleButton.querySelector('.chevron-up');
          if (formDetails) {
            if (formDetails.style.display === 'none') {
              formDetails.style.display = 'block';
              chevronDown!.style.display = 'none';
              chevronUp!.style.display = 'inline';
            } else {
              formDetails.style.display = 'none';
              chevronDown!.style.display = 'inline';
              chevronUp!.style.display = 'none';
            }
          }
        });
      });

      return () => {
        toggleButtons.forEach((toggleButton) => {
          toggleButton.removeEventListener('click', () => {});
        });
      };
    }
  }, [formRef, form]);

  const goToObjectRecord = useCallback(
    (objectId: string, recordId: string, record?: any) => {
      navigateToLinkedRecord(objectId, recordId, record);
    },
    [navigateToLinkedRecord]
  );

  useEffect(() => {
    if (formRef.current) {
      const goToObjects = formRef.current.querySelectorAll('.go-to-object');
      goToObjects.forEach((goToObject) => {
        goToObject.addEventListener('click', () => {
          const url = goToObject.getAttribute('data-id');
          if (url) {
            // Parse the URL to extract objectId and recordId
            const urlParts = url.split('/').filter(Boolean);
            if (urlParts.length >= 2) {
              const objectId = urlParts[0];
              const recordId = urlParts[1];
              // For template-based forms, we don't have the full record data
              // So we'll pass a minimal record object with just the id
              goToObjectRecord(objectId, recordId);
            }
          }
        });
      });

      return () => {
        goToObjects.forEach((goToObject) => {
          goToObject.removeEventListener('click', () => {});
        });
      };
    }
  }, [formRef, form, goToObjectRecord]);

  const getType = (key: string) => {
    const field = linkedObj?.fields?.find((field) => field.name === key);
    return field?.type;
  };

  const findFieldChoices = (fieldName: string, value: string) => {
    return linkedObj?.fields
      ?.find((field) => field.name === fieldName)
      ?.options?.choices?.find((opt) => opt.id === value);
  };

  const renderValue = (key: string, value: any) => {
    const type = getType(key);
    if (
      key === 'createdAt' ||
      key === 'updatedAt' ||
      [FieldTypes.DATETIME, FieldTypes.CREATED_TIME, FieldTypes.MODIFIED_TIME].includes(type)
    )
      return dayjs(value).format('DD/MM/YYYY HH:mm');
    if (getType(key) === FieldTypes.RELATIONSHIP) return value?.name || '';
    if (type === FieldTypes.SINGLE_SELECT) {
      return findFieldChoices(key, value)?.label || '';
    }
    if (type === FieldTypes.MULTI_SELECT) {
      return value?.map((val: string) => findFieldChoices(key, val)?.label).join(', ');
    }
    if (typeof value === 'object') return value?.name || '';
    return value;
  };

  const renderDefaultFields = (form: Form, record: Record) => {
    if (!form?.records) return <></>;
    Object.entries(record).forEach(([key, value]) => {
      const field = form.linkedObj?.fields?.find((field) => field.id === key);
      if (field) {
        if (field.type === FieldTypes.RELATIONSHIP) delete record[field.id!];
        else {
          delete record[key];
          record[field.name!] = value;
        }
      } else record[key] = value;
    });
    return (
      <Accordion.Item key={record.id} value={`${form.linkedObj?.name.singular} - ${record.id}`}>
        <Flex fz={rem(14)} fw={500} className={classes.header}>
          <Accordion.Control>{`${form.linkedObj?.name.singular} - ${record.id}`}</Accordion.Control>
          <Box
            px={rem(8)}
            py={rem(2)}
            c='decaBlue.5'
            bg={'decaBlue.0'}
            h={'fit-content'}
            className='go-to-object'
            onClick={() => {
              goToObjectRecord(record.objectId, record.id, record);
            }}
          >
            {t('editObject')}
          </Box>
        </Flex>
        {Object.entries(record).map(([key, value]) => (
          <Accordion.Panel key={key}>
            <Flex direction='row' gap={rem(8)}>
              <Text w='30%' fz='md' fw={400}>
                {startCase(key)}
              </Text>
              <Text w='70%' fz='md' fw={500} c='decaGrey.4'>
                {renderValue(key, value)}
              </Text>
            </Flex>
          </Accordion.Panel>
        ))}
      </Accordion.Item>
    );
  };

  if (!form) return <Loader />;
  return (
    <>
      {(form.records?.length ?? 0) > 0 && (
        <Box>
          <Stack gap='xs' h='100%'>
            <ScrollArea type='hover'>
              <Box>
                {isPinned ? (
                  <>
                    <Box className={classes.formHeader}>
                      <Text fz={rem(18)} fw={500}>
                        {form?.linkedObj?.name.singular ?? form?.linkedObj?.name.plural ?? ''}
                      </Text>
                      <Button
                        onClick={() => setIsUnpinModalOpen(true)}
                        leftSection={<IconPinnedOff size={16} />}
                        fz={rem(14)}
                        fw={500}
                        variant='subtle'
                        color='decaGrey.6'
                      >
                        {t('unpinTabbar')}
                      </Button>
                    </Box>
                    <Box className={classes.pinnedFormContainer}>
                      {form.template && form.template !== '' ? (
                        <div
                          ref={formRef}
                          dangerouslySetInnerHTML={{
                            __html: DOMPurify.sanitize(
                              syncContextData(form.template, { forms: form.records })
                            ),
                          }}
                        />
                      ) : (
                        <Accordion
                          classNames={classes}
                          className={classes.root}
                          chevronPosition='left'
                        >
                          {form.records?.map((record) => renderDefaultFields(form, record))}
                        </Accordion>
                      )}
                    </Box>
                  </>
                ) : (
                  <Box>
                    <Flex align='center' gap={rem(8)} mb={rem(8)}>
                      <Text fz={rem(18)} fw={500}>
                        {form?.linkedObj?.name.singular ?? form?.linkedObj?.name.plural ?? ''}
                      </Text>
                      <ActionIcon variant='subtle' size='sm' onClick={handleTogglePin} color='gray'>
                        <IconPin size={16} />
                      </ActionIcon>
                    </Flex>
                    {form.template && form.template !== '' ? (
                      <div
                        ref={formRef}
                        dangerouslySetInnerHTML={{
                          __html: DOMPurify.sanitize(
                            syncContextData(form.template, { forms: form.records })
                          ),
                        }}
                      />
                    ) : (
                      <Accordion
                        classNames={classes}
                        className={classes.root}
                        chevronPosition='left'
                      >
                        {form.records?.map((record) => renderDefaultFields(form, record))}
                      </Accordion>
                    )}
                  </Box>
                )}
              </Box>
            </ScrollArea>
          </Stack>
          <Modal
            centered
            title={t('unpinTabbar')}
            onOk={handleTogglePin}
            okText={t('confirm')}
            cancelText={t('cancel')}
            opened={isUnpinModalOpen}
            onClose={() => setIsUnpinModalOpen(false)}
            onCancel={() => setIsUnpinModalOpen(false)}
            okButtonProps={{
              bg: 'decaRed.4',
            }}
          >
            <Text>{t('unpinTabbarDescription')}</Text>
          </Modal>
        </Box>
      )}
    </>
  );
};

export default React.memo(Forms);
