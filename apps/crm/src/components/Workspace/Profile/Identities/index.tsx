import { useIdentities } from '@/hooks';
import { Box, Center, Flex, Loader, Title, rem } from '@mantine/core';
import { useTranslate } from '@tolgee/react';
import type { FC } from 'react';
import { useParams } from 'react-router-dom';
import { groupDataByDate } from '../helpers';
import IdentityRow from './components/IdentityRow';

const Identities: FC = () => {
  const { wsId: workspaceId, id: objectId, recordId } = useParams();
  const {
    data = [],
    error,
    isLoading,
  } = useIdentities({
    objectId: objectId!,
    recordId: recordId!,
    workspaceId: workspaceId!,
    sort: { createdAt: 'desc' },
  });
  const { t: tWorkspace } = useTranslate('workspace');

  if (error) return <Box>{error.toString()}</Box>;

  if (isLoading)
    return (
      <Center maw='100%' h='100%' mx='auto'>
        <Loader />
      </Center>
    );

  const identities = groupDataByDate(data, 'createdAt');
  const thisWeekIdentities = identities.get('thisWeek') ?? [];

  return (
    <Flex direction='column' rowGap={rem(18)}>
      <Title order={5} fw={500}>
        {tWorkspace('identities')}
      </Title>

      {thisWeekIdentities.length > 0 && (
        <Box>
          <Title order={6} fw={500}>
            {tWorkspace('thisWeek')}
          </Title>
          {thisWeekIdentities.map((identity) => (
            <IdentityRow key={identity.id} {...identity} />
          ))}
        </Box>
      )}

      {[...identities.entries()].slice(1).map(([date, items]) => (
        <Box key={date}>
          <Title order={6} fw={500}>
            {date}
          </Title>
          {items.map((identity) => (
            <IdentityRow key={identity.id} {...identity} />
          ))}
        </Box>
      ))}
    </Flex>
  );
};

export default Identities;
