import { Text, type TextProps } from '@mantine/core';
import { type ForwardRefRenderFunction, forwardRef } from 'react';

interface TruncatedTextProps extends Omit<TextProps, 'children'> {
  children: string;
  end?: number;
  start?: number;
}

const TruncatedText: ForwardRefRenderFunction<HTMLDivElement, TruncatedTextProps> = (
  props,
  ref
) => {
  const { children, end = 4, start = 4, ...rest } = props;

  return (
    <Text ref={ref} {...rest}>
      {children.substring(0, start)}...{children.substring(children.length - end)}
    </Text>
  );
};

export default forwardRef(TruncatedText);
