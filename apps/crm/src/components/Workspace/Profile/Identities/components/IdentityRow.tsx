import { createStyles } from '@mantine/emotion';
import { Dec<PERSON><PERSON>utton, DecaDataValue } from '@resola-ai/ui';
import { IconFiles } from '@tabler/icons-react';
import dayjs from 'dayjs';
import relativeTime from 'dayjs/plugin/relativeTime';
import type { FC } from 'react';

import { FormatDate } from '@/constants/workspace';
import type { Identity } from '@/models/identity';
import { tolgee } from '@/tolgee';
import { Flex, Grid, Tooltip, rem } from '@mantine/core';
import { useTranslate } from '@tolgee/react';
import { getIdentityBadge } from '../helpers';
import TruncatedText from './TruncatedText';

dayjs.extend(relativeTime);

const { Col } = Grid;

const useStyles = createStyles((theme) => ({
  root: {
    marginTop: rem(8),
    padding: `${rem(8)} 0`,

    ':not(:last-of-type)': {
      borderBottom: `1px solid ${theme.colors.decaLight[2]}`,
    },
  },
}));

const IdentityRow: FC<Identity> = (props) => {
  const { id, provider, type, updatedAt } = props;
  const { t: tCommon } = useTranslate('common');
  const { t: tWorkspace } = useTranslate('workspace');
  const { classes } = useStyles();

  return (
    <Grid className={classes.root}>
      <Col span={2}>{getIdentityBadge(type, tWorkspace)}</Col>
      <Col span={3}>
        <DecaDataValue title={tWorkspace('belongsTo')} value={provider} />
      </Col>
      <Col span={3}>
        <Tooltip label={dayjs(updatedAt).format(FormatDate[`${tolgee.getLanguage()} || 'ja'`])}>
          <DecaDataValue title={tCommon('lastUpdated')} value={dayjs(updatedAt).fromNow()} />
        </Tooltip>
      </Col>
      <Col span={4}>
        <Flex align='center' justify='end' rowGap={rem(8)}>
          <Tooltip label={id}>
            <TruncatedText>{id}</TruncatedText>
          </Tooltip>
          <Tooltip label={tCommon('copy')}>
            <DecaButton
              radius='xl'
              size='sm'
              variant='neutral_text'
              onClick={() => navigator.clipboard.writeText(id)}
            >
              <IconFiles size={16} />
            </DecaButton>
          </Tooltip>
        </Flex>
      </Col>
    </Grid>
  );
};

export default IdentityRow;
