import type { Identity } from '@/models/identity';
import { renderWithMantine } from '@/tests/utils/testUtils';
import { screen } from '@testing-library/react';
import { beforeEach, describe, expect, it, vi } from 'vitest';
import Identities from './index';

// Mock the useParams hook
const mockUseParams = vi.fn();
vi.mock('react-router-dom', () => ({
  useParams: () => mockUseParams(),
}));

// Mock the useIdentities hook
const mockUseIdentities = vi.fn();
vi.mock('@/hooks', () => ({
  useIdentities: (props: any) => mockUseIdentities(props),
}));

// Mock the IdentityRow component
vi.mock('./components/IdentityRow', () => ({
  default: ({ id, type, provider }: Identity) => (
    <div data-testid={`identity-row-${id}`}>
      <span>ID: {id}</span>
      <span>Type: {type}</span>
      <span>Provider: {provider}</span>
    </div>
  ),
}));

// Mock the groupDataByDate helper
const mockGroupDataByDate = vi.fn();
vi.mock('../helpers', () => ({
  groupDataByDate: (data: any, field: any) => mockGroupDataByDate(data, field),
}));

describe('Identities Component', () => {
  const mockParams = {
    wsId: 'workspace-123',
    id: 'object-456',
    recordId: 'record-789',
  };

  const mockIdentityData: Identity[] = [
    {
      id: 'identity-1',
      createdAt: '2023-01-15T10:00:00Z',
      updatedAt: '2023-01-15T10:00:00Z',
      type: 'email',
      value: '<EMAIL>',
      provider: 'email-provider',
      displayName: 'Test Email',
      relatedObjectId: 'object-456',
      relatedRecordId: 'record-789',
      properties: {
        verified: true,
        primary: true,
      },
      settingId: 'setting-1',
    },
    {
      id: 'identity-2',
      createdAt: '2023-01-10T15:30:00Z',
      updatedAt: '2023-01-10T15:30:00Z',
      type: 'phone',
      value: '+**********',
      provider: 'phone-provider',
      displayName: 'Test Phone',
      relatedObjectId: 'object-456',
      relatedRecordId: 'record-789',
      properties: {
        verified: false,
        primary: false,
      },
      settingId: 'setting-2',
    },
  ];

  beforeEach(() => {
    vi.clearAllMocks();
    mockUseParams.mockReturnValue(mockParams);
  });

  describe('Loading State', () => {
    it('should display loader when data is loading', () => {
      mockUseIdentities.mockReturnValue({
        data: undefined,
        error: undefined,
        isLoading: true,
      });

      renderWithMantine(<Identities />);

      // Look for the Mantine loader using its class
      expect(document.querySelector('.mantine-Loader-root')).toBeInTheDocument();
    });

    it('should call useIdentities with correct parameters', () => {
      mockUseIdentities.mockReturnValue({
        data: [],
        error: undefined,
        isLoading: true,
      });

      renderWithMantine(<Identities />);

      expect(mockUseIdentities).toHaveBeenCalledWith({
        objectId: 'object-456',
        recordId: 'record-789',
        workspaceId: 'workspace-123',
        sort: { createdAt: 'desc' },
      });
    });
  });

  describe('Error State', () => {
    it('should display error message when there is an error', () => {
      const errorMessage = 'Failed to fetch identities';
      const mockError = new Error(errorMessage);
      mockUseIdentities.mockReturnValue({
        data: undefined,
        error: mockError,
        isLoading: false,
      });

      renderWithMantine(<Identities />);

      // The component shows "Error: " prefix, so we look for the full text
      expect(screen.getByText(`Error: ${errorMessage}`)).toBeInTheDocument();
    });

    it('should handle error objects without specific message', () => {
      const mockError = { toString: () => 'Network error occurred' };
      mockUseIdentities.mockReturnValue({
        data: undefined,
        error: mockError,
        isLoading: false,
      });

      renderWithMantine(<Identities />);

      expect(screen.getByText('Network error occurred')).toBeInTheDocument();
    });
  });

  describe('Success State with Data', () => {
    beforeEach(() => {
      mockUseIdentities.mockReturnValue({
        data: mockIdentityData,
        error: undefined,
        isLoading: false,
      });
    });

    it('should display the main identities title', () => {
      const mockGroupedData = new Map([
        ['thisWeek', [mockIdentityData[0]]],
        ['15/01/2023', [mockIdentityData[1]]],
      ]);
      mockGroupDataByDate.mockReturnValue(mockGroupedData);

      renderWithMantine(<Identities />);

      // Look for the h5 element directly since real Mantine Title doesn't use data-testid
      expect(screen.getByRole('heading', { level: 5 })).toBeInTheDocument();
      expect(screen.getByText('identities')).toBeInTheDocument();
    });

    it('should call groupDataByDate with correct parameters', () => {
      const mockGroupedData = new Map([['thisWeek', []]]);
      mockGroupDataByDate.mockReturnValue(mockGroupedData);

      renderWithMantine(<Identities />);

      expect(mockGroupDataByDate).toHaveBeenCalledWith(mockIdentityData, 'createdAt');
    });

    it('should display "This Week" section when there are identities from this week', () => {
      const mockGroupedData = new Map([
        ['thisWeek', [mockIdentityData[0]]],
        ['10/01/2023', [mockIdentityData[1]]],
      ]);
      mockGroupDataByDate.mockReturnValue(mockGroupedData);

      renderWithMantine(<Identities />);

      expect(screen.getByText('thisWeek')).toBeInTheDocument();
      expect(screen.getByTestId('identity-row-identity-1')).toBeInTheDocument();
    });

    it('should not display "This Week" section when there are no identities from this week', () => {
      const mockGroupedData = new Map([
        ['thisWeek', []],
        ['10/01/2023', mockIdentityData],
      ]);
      mockGroupDataByDate.mockReturnValue(mockGroupedData);

      renderWithMantine(<Identities />);

      expect(screen.queryByText('thisWeek')).not.toBeInTheDocument();
    });

    it('should display other date sections correctly', () => {
      const mockGroupedData = new Map([
        ['thisWeek', []],
        ['15/01/2023', [mockIdentityData[0]]],
        ['10/01/2023', [mockIdentityData[1]]],
      ]);
      mockGroupDataByDate.mockReturnValue(mockGroupedData);

      renderWithMantine(<Identities />);

      expect(screen.getByText('15/01/2023')).toBeInTheDocument();
      expect(screen.getByText('10/01/2023')).toBeInTheDocument();
      expect(screen.getByTestId('identity-row-identity-1')).toBeInTheDocument();
      expect(screen.getByTestId('identity-row-identity-2')).toBeInTheDocument();
    });

    it('should pass correct props to IdentityRow components', () => {
      const mockGroupedData = new Map([
        ['thisWeek', [mockIdentityData[0]]],
        ['10/01/2023', [mockIdentityData[1]]],
      ]);
      mockGroupDataByDate.mockReturnValue(mockGroupedData);

      renderWithMantine(<Identities />);

      // Check first identity row
      const firstRow = screen.getByTestId('identity-row-identity-1');
      expect(firstRow).toHaveTextContent('ID: identity-1');
      expect(firstRow).toHaveTextContent('Type: email');
      expect(firstRow).toHaveTextContent('Provider: email-provider');

      // Check second identity row
      const secondRow = screen.getByTestId('identity-row-identity-2');
      expect(secondRow).toHaveTextContent('ID: identity-2');
      expect(secondRow).toHaveTextContent('Type: phone');
      expect(secondRow).toHaveTextContent('Provider: phone-provider');
    });

    it('should render multiple identities in the same date group', () => {
      const additionalIdentity: Identity = {
        id: 'identity-3',
        createdAt: '2023-01-15T11:00:00Z',
        updatedAt: '2023-01-15T11:00:00Z',
        type: 'lineId',
        value: 'line123',
        provider: 'line-provider',
        displayName: 'Test Line',
        relatedObjectId: 'object-456',
        relatedRecordId: 'record-789',
        properties: {
          verified: true,
          primary: false,
        },
        settingId: 'setting-3',
      };

      const mockGroupedData = new Map([
        ['thisWeek', [mockIdentityData[0], additionalIdentity]],
        ['10/01/2023', [mockIdentityData[1]]],
      ]);
      mockGroupDataByDate.mockReturnValue(mockGroupedData);

      renderWithMantine(<Identities />);

      expect(screen.getByTestId('identity-row-identity-1')).toBeInTheDocument();
      expect(screen.getByTestId('identity-row-identity-3')).toBeInTheDocument();
      expect(screen.getByTestId('identity-row-identity-2')).toBeInTheDocument();
    });

    it('should handle the case when there are only thisWeek identities', () => {
      const mockGroupedData = new Map([['thisWeek', mockIdentityData]]);
      mockGroupDataByDate.mockReturnValue(mockGroupedData);

      renderWithMantine(<Identities />);

      expect(screen.getByText('thisWeek')).toBeInTheDocument();
      expect(screen.getByTestId('identity-row-identity-1')).toBeInTheDocument();
      expect(screen.getByTestId('identity-row-identity-2')).toBeInTheDocument();

      // Should not display any other date sections
      expect(screen.queryByText('15/01/2023')).not.toBeInTheDocument();
      expect(screen.queryByText('10/01/2023')).not.toBeInTheDocument();
    });
  });

  describe('Empty State', () => {
    it('should handle empty data array', () => {
      mockUseIdentities.mockReturnValue({
        data: [],
        error: undefined,
        isLoading: false,
      });

      const mockGroupedData = new Map([['thisWeek', []]]);
      mockGroupDataByDate.mockReturnValue(mockGroupedData);

      renderWithMantine(<Identities />);

      expect(screen.getByText('identities')).toBeInTheDocument();
      expect(screen.queryByText('thisWeek')).not.toBeInTheDocument();
      expect(screen.queryByTestId('identity-row-identity-1')).not.toBeInTheDocument();
    });

    it('should handle undefined data with default empty array', () => {
      mockUseIdentities.mockReturnValue({
        data: undefined,
        error: undefined,
        isLoading: false,
      });

      const mockGroupedData = new Map([['thisWeek', []]]);
      mockGroupDataByDate.mockReturnValue(mockGroupedData);

      renderWithMantine(<Identities />);

      expect(mockGroupDataByDate).toHaveBeenCalledWith([], 'createdAt');
      expect(screen.getByText('identities')).toBeInTheDocument();
    });
  });

  describe('Edge Cases', () => {
    it('should handle missing URL parameters gracefully', () => {
      mockUseParams.mockReturnValue({
        wsId: undefined,
        id: undefined,
        recordId: undefined,
      });

      mockUseIdentities.mockReturnValue({
        data: [],
        error: undefined,
        isLoading: false,
      });

      renderWithMantine(<Identities />);

      expect(mockUseIdentities).toHaveBeenCalledWith({
        objectId: undefined,
        recordId: undefined,
        workspaceId: undefined,
        sort: { createdAt: 'desc' },
      });
    });

    it('should handle partial URL parameters', () => {
      mockUseParams.mockReturnValue({
        wsId: 'workspace-123',
        id: undefined,
        recordId: 'record-789',
      });

      mockUseIdentities.mockReturnValue({
        data: [],
        error: undefined,
        isLoading: false,
      });

      renderWithMantine(<Identities />);

      expect(mockUseIdentities).toHaveBeenCalledWith({
        objectId: undefined,
        recordId: 'record-789',
        workspaceId: 'workspace-123',
        sort: { createdAt: 'desc' },
      });
    });

    it('should maintain proper key props for mapped elements', () => {
      const mockGroupedData = new Map([
        ['thisWeek', [mockIdentityData[0]]],
        ['15/01/2023', [mockIdentityData[1]]],
      ]);
      mockGroupDataByDate.mockReturnValue(mockGroupedData);

      const { container } = renderWithMantine(<Identities />);

      // Verify that each IdentityRow has a unique key by checking for duplicated elements
      const identityRows = container.querySelectorAll('[data-testid^="identity-row-"]');
      expect(identityRows).toHaveLength(2);
    });

    it('should handle complex grouped data structure', () => {
      const complexGroupedData = new Map([
        ['thisWeek', [mockIdentityData[0]]],
        ['15/01/2023', [mockIdentityData[1]]],
        ['31/12/2022', [mockIdentityData[0]]],
      ]);
      mockGroupDataByDate.mockReturnValue(complexGroupedData);

      renderWithMantine(<Identities />);

      // Should show thisWeek section
      expect(screen.getByText('thisWeek')).toBeInTheDocument();

      // Should show non-empty date sections (excluding thisWeek)
      expect(screen.getByText('15/01/2023')).toBeInTheDocument();
      expect(screen.getByText('31/12/2022')).toBeInTheDocument();

      // Should show correct number of identity rows
      expect(screen.getAllByTestId(/^identity-row-/)).toHaveLength(3);
    });
  });
});
