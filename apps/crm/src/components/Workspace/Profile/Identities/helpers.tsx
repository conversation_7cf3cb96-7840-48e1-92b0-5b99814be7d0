import type { IdentityType } from '@/models/identity';
import type { TFnType } from '@tolgee/react';

import { DecaStatus } from '@resola-ai/ui';

export const getIdentityBadge = (type: IdentityType, t: TFnType) => {
  switch (type) {
    case 'email':
      return <DecaStatus variant='yellow'>{t(`identity.${type}`)}</DecaStatus>;
    case 'lineId':
      return (
        <DecaStatus tt='uppercase' variant='green'>
          {t(`identity.${type}`)}
        </DecaStatus>
      );
    case 'phone':
      return <DecaStatus variant='blue'>{t(`identity.${type}`)}</DecaStatus>;
    case 'webuserId':
      return <DecaStatus variant='violet'>{t(`identity.${type}`)}</DecaStatus>;
    default:
      return null;
  }
};
