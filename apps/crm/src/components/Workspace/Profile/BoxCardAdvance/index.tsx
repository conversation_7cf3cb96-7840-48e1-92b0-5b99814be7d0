import type { BoxCard } from '@/models';
import { useSortable } from '@dnd-kit/sortable';
import { CSS } from '@dnd-kit/utilities';
import { Box, rem } from '@mantine/core';
import { createStyles } from '@mantine/emotion';
import { IconGripVertical } from '@tabler/icons-react';
import type React from 'react';
import { type CSSProperties, memo, useCallback, useEffect, useRef, useState } from 'react';

type Props = {
  children: React.ReactNode;
  className?: string;
  recordId: string;
  boxCard: BoxCard;
  onChangeSize: (id: string, height: string) => void;
  allowDrag?: boolean;
};

const MIN_HEIGHT = 200;
const HEIGHT_PADDING_OFFSET = 32;

const useStyle = createStyles((theme) => ({
  root: {
    padding: `${rem(16)} ${rem(20)}`,
    backgroundColor: 'white',
    borderRadius: theme.spacing.md,
  },
  container: {
    backgroundColor: 'white',
    height: '100%',
    position: 'relative',
  },
  scrollableContent: {
    height: '100%',
    overflowY: 'auto',
    overflowX: 'hidden',
    scrollbarGutter: 'stable',
    scrollbarWidth: 'thin',
    scrollbarColor: 'transparent transparent',
    '&::-webkit-scrollbar': {
      width: rem(8),
    },
    '&::-webkit-scrollbar-track': {
      background: 'transparent',
      borderRadius: rem(4),
    },
    '&::-webkit-scrollbar-thumb': {
      background: 'transparent',
      borderRadius: rem(4),
      transition: 'background-color 0.2s ease',
    },
    '&:hover': {
      scrollbarColor: `${theme.colors.decaGrey[3]} transparent`,
    },
  },
  resizePanel: {
    position: 'absolute',
    bottom: 0,
    left: 0,
    right: 0,
    height: rem(10),
    cursor: 'ns-resize',
    zIndex: 10,
    transition: 'background-color 0.2s ease',
  },
  drag: {
    position: 'absolute',
    top: rem(16),
    right: rem(16),
    cursor: 'move',
    color: theme.colors.decaGrey[4],
    zIndex: 5,
  },
  gradientOverlay: {
    position: 'absolute',
    bottom: 0,
    left: 0,
    right: 0,
    height: rem(70),
    background: `linear-gradient(to bottom, transparent, white)`,
    pointerEvents: 'none',
    zIndex: 8,
  },
}));

const BoxCardAdvance = ({
  children,
  recordId,
  boxCard,
  allowDrag = true,
  className,
  onChangeSize,
}: Props) => {
  const { classes, cx } = useStyle();
  const contentRef = useRef<HTMLDivElement>(null);
  const scrollContainerRef = useRef<HTMLDivElement>(null);
  const isResizingRef = useRef(false);
  const animationFrameRef = useRef<number>();

  const { id, height } = boxCard;

  const [boxHeight, setBoxHeight] = useState<number>(MIN_HEIGHT);
  const [showOverflowGradient, setShowOverflowGradient] = useState(false);

  // Check for overflow and show/hide gradient
  const checkOverflow = useCallback(() => {
    if (scrollContainerRef.current && !isResizingRef.current) {
      const { scrollHeight, clientHeight, scrollTop } = scrollContainerRef.current;
      const hasOverflow = scrollHeight > clientHeight;
      const isScrolledToBottom = scrollTop + clientHeight >= scrollHeight - 5;
      setShowOverflowGradient(hasOverflow && !isScrolledToBottom);
    }
  }, []);

  // Initialize height from props or content
  useEffect(() => {
    if (height && height !== 'auto') {
      // Respect API height when provided
      const apiHeight = +height;
      setBoxHeight(apiHeight > 0 ? apiHeight : MIN_HEIGHT);
    } else {
      // Use minimum height as fallback
      setBoxHeight(MIN_HEIGHT);
    }
  }, [recordId, height]);

  // Check overflow when height changes
  useEffect(() => {
    const timeoutId = setTimeout(checkOverflow, 100);
    return () => clearTimeout(timeoutId);
  }, [boxHeight, checkOverflow]);

  // Improved resize handler with requestAnimationFrame
  const handleMouseDown = useCallback(
    (e: React.MouseEvent) => {
      e.preventDefault();

      const startY = e.clientY;
      const startHeight = boxHeight;
      const contentHeight = contentRef.current?.scrollHeight || MIN_HEIGHT;
      const maxAllowedHeight = Math.max(contentHeight + HEIGHT_PADDING_OFFSET, MIN_HEIGHT * 3);

      isResizingRef.current = true;
      let hasChanged = false;
      let currentHeight = startHeight; // Track the actual current height during drag

      const doDrag = (e: MouseEvent) => {
        // Cancel any pending animation frame
        if (animationFrameRef.current) {
          cancelAnimationFrame(animationFrameRef.current);
        }

        // Use requestAnimationFrame for smoother performance
        animationFrameRef.current = requestAnimationFrame(() => {
          const newHeight = startHeight + (e.clientY - startY);

          // Enforce minimum height and reasonable maximum (users can resize freely)
          if (newHeight >= MIN_HEIGHT && newHeight <= maxAllowedHeight) {
            currentHeight = newHeight; // Update the tracked height
            setBoxHeight(newHeight);
            hasChanged = true;
          }
        });
      };

      const stopDrag = () => {
        isResizingRef.current = false;

        // Cancel any pending animation frame
        if (animationFrameRef.current) {
          cancelAnimationFrame(animationFrameRef.current);
        }

        // Call onChangeSize with the actual current height
        if (hasChanged) {
          onChangeSize(id, Math.round(currentHeight).toString());
        }

        // Check overflow after resize is complete
        setTimeout(checkOverflow, 100);

        document.removeEventListener('mousemove', doDrag);
        document.removeEventListener('mouseup', stopDrag);
      };

      document.addEventListener('mousemove', doDrag);
      document.addEventListener('mouseup', stopDrag);
    },
    [boxHeight, id, onChangeSize, checkOverflow]
  );

  // Cleanup animation frame on unmount
  useEffect(() => {
    return () => {
      if (animationFrameRef.current) {
        cancelAnimationFrame(animationFrameRef.current);
      }
    };
  }, []);

  const { attributes, isDragging, listeners, setNodeRef, transform, transition } = useSortable({
    id: id || '',
  });

  const style: CSSProperties = {
    opacity: isDragging ? 0.4 : undefined,
    transform: CSS.Translate.toString(transform),
    transition,
  };

  return (
    <Box
      data-testid={`box-card-${id}`}
      pos='relative'
      className={cx(classes.root, className)}
      sx={{ height: `${boxHeight}px` }}
    >
      <Box className={classes.resizePanel} onMouseDown={handleMouseDown} />
      <Box
        className={classes.container}
        {...attributes}
        {...(allowDrag ? listeners : {})}
        ref={setNodeRef}
        style={{
          ...style,
          minHeight: `${boxHeight - 16}px`,
          marginBottom: rem(16),
        }}
      >
        {allowDrag && <IconGripVertical size={14} className={classes.drag} />}

        <Box
          ref={scrollContainerRef}
          className={classes.scrollableContent}
          onScroll={checkOverflow}
        >
          <div ref={contentRef}>{children}</div>
        </Box>

        {/* Gradient overlay for overflow indication */}
        {showOverflowGradient && id === 'objectFields' && (
          <Box className={classes.gradientOverlay} />
        )}
      </Box>
    </Box>
  );
};

export default memo(BoxCardAdvance);
