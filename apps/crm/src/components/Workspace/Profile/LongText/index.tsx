import { useProfileContext } from '@/contexts/ProfileContext';
import { useWorkspaceContext } from '@/contexts/WorkspaceContext';
import {
  Accordion,
  Box,
  Button,
  CopyButton,
  Flex,
  Group,
  Text,
  Textarea,
  rem,
} from '@mantine/core';
import { createStyles } from '@mantine/emotion';
import { Modal } from '@resola-ai/ui';
import { IconCheck, IconCopy, IconEdit, IconMinus } from '@tabler/icons-react';
import { useTranslate } from '@tolgee/react';
import { memo, useState } from 'react';

const useStyles = createStyles((theme) => ({
  control: {
    backgroundColor: theme.colors.decaLight[0],
    '&:hover': {
      backgroundColor: 'unset',
    },
  },
  panel: {
    backgroundColor: theme.colors.decaLight[0],
    '& textarea': {
      backgroundColor: theme.colors.decaLight[0],
      border: 'none',
      padding: 0,
      fontSize: rem(14),
      color: theme.colors.decaGrey[9],
    },
  },
}));

const LongText = () => {
  const { profile } = useProfileContext();
  const { activeView, object, onSaveData, currRecordIndex, handleViewChange } =
    useWorkspaceContext();
  const { t } = useTranslate('workspace');
  const { classes } = useStyles();
  const [editingId, setEditingId] = useState<string | null>(null);
  const [editValue, setEditValue] = useState('');
  const [activeAccordion, setActiveAccordion] = useState<string | null>(null);
  const [isUnpinModalOpen, setIsUnpinModalOpen] = useState(false);

  const handleUnpinTabbar = () => {
    if (activeView && activeAccordion) {
      const longTextList = activeView.displayLongText || [];
      handleViewChange(activeView.id, {
        ...activeView,
        displayLongText: longTextList.filter((id) => id !== activeAccordion),
      });
    }
    setIsUnpinModalOpen(false);
  };

  return (
    <Box>
      <Flex justify='space-between' align='center' mb={rem(12)}>
        <Text fw={500} fz={rem(18)} c='decaNavy.9'>
          {t('longText')}
        </Text>
        <Button
          onClick={() => setIsUnpinModalOpen(true)}
          leftSection={<IconMinus size={16} />}
          fz={rem(14)}
          variant='subtle'
          color='red'
          disabled={!activeAccordion}
        >
          {t('unpinTabbar')}
        </Button>
      </Flex>
      <Accordion
        value={activeAccordion}
        onChange={setActiveAccordion}
        bg='decaLight.1'
        variant='contained'
      >
        {activeView?.displayLongText?.map((id) => (
          <Accordion.Item key={id} value={id}>
            <Accordion.Control className={classes.control}>
              <Flex align='center' justify='space-between' wrap='wrap'>
                <Text w={rem(200)} truncate fw={500} fz={rem(16)} c='decaNavy.6'>
                  {object?.fields?.find((field) => field.id === id)?.name}
                </Text>
                <Group gap='xs'>
                  {editingId === id ? (
                    <>
                      <Button
                        variant='white'
                        color='decaGrey.6'
                        fz={rem(14)}
                        onClick={(e) => {
                          e.stopPropagation();
                          setEditingId(null);
                        }}
                      >
                        {t('cancel')}
                      </Button>
                      <Button
                        variant='subtle'
                        color='blue'
                        fz={rem(14)}
                        onClick={async (e) => {
                          e.stopPropagation();
                          setEditingId(null);
                          await onSaveData(editValue, currRecordIndex, id, false);
                        }}
                      >
                        {t('save')}
                      </Button>
                    </>
                  ) : (
                    <>
                      <Button
                        variant='subtle'
                        color='blue'
                        fz={rem(14)}
                        onClick={(e) => {
                          e.stopPropagation();
                          setEditingId(id);
                          setEditValue(profile?.[id] || '');
                        }}
                        leftSection={<IconEdit size={16} />}
                      >
                        {t('edit')}
                      </Button>
                      <CopyButton value={profile?.[id] || ''} timeout={1000}>
                        {({ copied, copy }) => (
                          <Button
                            variant='light'
                            bg='white'
                            color='decaGrey.6'
                            fz={rem(14)}
                            onClick={(e) => {
                              e.stopPropagation();
                              copy();
                            }}
                            leftSection={copied ? <IconCheck size={16} /> : <IconCopy size={16} />}
                          >
                            {t('copyToClipboard')}
                          </Button>
                        )}
                      </CopyButton>
                    </>
                  )}
                </Group>
              </Flex>
            </Accordion.Control>
            <Accordion.Panel className={classes.panel}>
              {editingId === id ? (
                <Textarea
                  value={editValue}
                  onChange={(e) => setEditValue(e.currentTarget.value)}
                  minRows={3}
                  autosize
                  autoFocus
                />
              ) : (
                <Text sx={{ whiteSpace: 'pre-wrap' }} fz={rem(14)} c='decaGrey.9'>
                  {profile?.[id] ?? ''}
                </Text>
              )}
            </Accordion.Panel>
          </Accordion.Item>
        ))}
      </Accordion>
      <Modal
        centered
        title={t('unpinTabbar')}
        onOk={handleUnpinTabbar}
        okText={t('confirm')}
        cancelText={t('cancel')}
        opened={isUnpinModalOpen}
        onClose={() => setIsUnpinModalOpen(false)}
        onCancel={() => setIsUnpinModalOpen(false)}
      >
        <Text>{t('unpinTabbarDescription')}</Text>
      </Modal>
    </Box>
  );
};

export default memo(LongText);
