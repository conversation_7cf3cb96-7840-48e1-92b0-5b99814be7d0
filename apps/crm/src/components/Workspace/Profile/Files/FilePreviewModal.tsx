import { useAttachmentId } from '@/hooks';
import { Flex, Loader, Pagination, Text, rem } from '@mantine/core';
import { useTranslate } from '@tolgee/react';
import { memo, useEffect, useState } from 'react';
import { Document, Page, pdfjs } from 'react-pdf';
import 'react-pdf/dist/Page/AnnotationLayer.css';
import 'react-pdf/dist/Page/TextLayer.css';
import { createStyles } from '@mantine/emotion';
import { Modal } from '@resola-ai/ui';
import { useParams } from 'react-router-dom';
// Types
type FilePreviewModalProps = {
  opened: boolean;
  onClose: () => void;
  fileId: string;
  fileTitle: string;
};

// Styles
const useStyles = createStyles(() => ({
  previewImage: {
    maxWidth: '100%',
    maxHeight: 'calc(90vh - 100px)',
    objectFit: 'contain',
  },
}));

// Image Preview Modal Component
export const ImagePreviewModal = memo(
  ({ opened, onClose, fileId, fileTitle }: FilePreviewModalProps) => {
    const { classes } = useStyles();
    const { wsId: workspaceId, id: objectId, recordId } = useParams();
    const { url, isLoading } = useAttachmentId({
      attachmentId: fileId,
      workspaceId: workspaceId ?? '',
      objectId: objectId ?? '',
      recordId: recordId ?? '',
      enabled: opened,
    });

    return (
      <Modal opened={opened} onClose={onClose} title={fileTitle} size='75%' centered>
        <Flex justify='center' align='center' direction='column' p={rem(10)}>
          {isLoading ? (
            <Loader size='lg' data-testid='image-preview-modal-loader' />
          ) : (
            url && <img src={url} alt={fileTitle} className={classes.previewImage} />
          )}
        </Flex>
      </Modal>
    );
  }
);

ImagePreviewModal.displayName = 'ImagePreviewModal';

// PDF Preview Modal Component
export const PDFPreviewModal = memo(
  ({ opened, onClose, fileId, fileTitle }: FilePreviewModalProps) => {
    // Initialize PDF.js worker
    pdfjs.GlobalWorkerOptions.workerSrc = `//unpkg.com/pdfjs-dist@${pdfjs.version}/build/pdf.worker.min.mjs`;
    const [numPages, setNumPages] = useState<number | null>(null);
    const [pageNumber, setPageNumber] = useState(1);
    const { t: tCommon } = useTranslate('common');
    const { wsId: workspaceId, id: objectId, recordId } = useParams();
    const { url, isLoading } = useAttachmentId({
      attachmentId: fileId,
      workspaceId: workspaceId ?? '',
      objectId: objectId ?? '',
      recordId: recordId ?? '',
      enabled: opened,
    });

    // Reset page number when modal opens
    useEffect(() => {
      if (opened) {
        setPageNumber(1);
      }
    }, [opened]);

    const onDocumentLoadSuccess = ({ numPages }: { numPages: number }) => {
      setNumPages(numPages);
    };

    const handlePageChange = (newPage: number) => {
      setPageNumber(newPage);
    };

    return (
      <Modal opened={opened} onClose={onClose} title={fileTitle} size='75%' centered>
        <Flex direction='column' align='center' w='100%'>
          {isLoading && <Loader />}
          {url && (
            <>
              <Document file={url} onLoadSuccess={onDocumentLoadSuccess} loading={<Loader />}>
                <Page key={`page_${pageNumber}`} pageNumber={pageNumber} />
              </Document>

              {numPages && numPages > 1 && (
                <Flex direction='column' align='center' mt={rem(20)}>
                  <Text size='sm' mb={rem(10)}>
                    {tCommon('page')}: {pageNumber}/{numPages}
                  </Text>
                  <Pagination
                    total={numPages}
                    value={pageNumber}
                    onChange={handlePageChange}
                    withEdges
                    withControls={true}
                    withPages={false}
                  />
                </Flex>
              )}
            </>
          )}
        </Flex>
      </Modal>
    );
  }
);

PDFPreviewModal.displayName = 'PDFPreviewModal';
