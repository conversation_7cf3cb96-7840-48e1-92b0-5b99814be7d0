import { renderWithMantine } from '@/tests/utils/testUtils';
import { DndContext, closestCenter } from '@dnd-kit/core';
import { SortableContext, verticalListSortingStrategy } from '@dnd-kit/sortable';
import { screen } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { vi } from 'vitest';
import { SortableCategory } from './SortableCategory';

// Mock useSortable from @dnd-kit/sortable
vi.mock('@dnd-kit/sortable', async () => {
  const actual = await vi.importActual('@dnd-kit/sortable');
  return {
    ...actual,
    useSortable: () => ({
      attributes: { 'aria-roledescription': 'sortable' },
      listeners: { 'data-test-id': 'sortable-listeners' },
      setNodeRef: vi.fn(),
      transform: null,
      transition: 'transform 250ms ease',
    }),
  };
});

// Mock tolgee translation
vi.mock('@tolgee/react', () => ({
  useTranslate: () => ({
    t: (key) => (key === 'enterCategoryName' ? 'Enter category name' : key),
  }),
}));

describe('SortableCategory Component', () => {
  const mockCategory = {
    id: 'category-1',
    name: 'Test Category',
  };

  const SortableCategoryWrapper = ({
    category = mockCategory,
    id = 'category-1',
    onRemove = vi.fn(),
    onNameChange = vi.fn(),
  }) => {
    return (
      <DndContext collisionDetection={closestCenter}>
        <SortableContext
          items={[id]}
          strategy={verticalListSortingStrategy}
          data-testid='sortable-context'
        >
          <SortableCategory
            category={category}
            id={id}
            onRemove={onRemove}
            onNameChange={onNameChange}
          />
        </SortableContext>
      </DndContext>
    );
  };

  it('renders with correct category name', () => {
    renderWithMantine(<SortableCategoryWrapper />);

    // Check if the input value matches the category name
    const input = screen.getByDisplayValue('Test Category');
    expect(input).toBeInTheDocument();
  });

  it('calls onRemove when delete button is clicked', async () => {
    const onRemoveMock = vi.fn();
    const user = userEvent.setup();

    renderWithMantine(<SortableCategoryWrapper onRemove={onRemoveMock} />);

    // Find and click the delete button
    const deleteButton = screen.getByTestId('remove-category-button');
    await user.click(deleteButton);

    // Check if onRemove was called with the correct ID
    expect(onRemoveMock).toHaveBeenCalledWith('category-1');
  });

  it('displays the placeholder when the category name is empty', () => {
    const emptyCategory = { id: 'empty-category', name: '' };

    renderWithMantine(<SortableCategoryWrapper category={emptyCategory} id='empty-category' />);

    // Check if the placeholder text is shown
    const input = screen.getByPlaceholderText('Enter category name');
    expect(input).toBeInTheDocument();
  });

  it('has data-testid attribute', () => {
    renderWithMantine(<SortableCategoryWrapper />);

    // Check if the component has the correct data-testid
    const component = screen.getByTestId('sortable-category');
    expect(component).toBeInTheDocument();
  });
});
