import type { FileEnhanced } from '@/models/file';
import { Box, Flex, Title, rem } from '@mantine/core';
import { DecaProgressBar } from '@resola-ai/ui';
import { DecaFileUpload, type DecaFileUploadProps } from '@resola-ai/ui/components';
import { useTranslate } from '@tolgee/react';
import { isValidElement } from 'react';
import { getFileKey } from './utils';

type FileUploaderProps = {
  onDrop: DecaFileUploadProps['onDrop'];
  uploadingFiles: FileEnhanced[];
  uploadProgress: Record<string, number>;
  onRetryUploadingFile: (file: FileEnhanced) => void;
};

export const FileUploader = ({
  onDrop,
  uploadingFiles,
  uploadProgress,
  onRetryUploadingFile,
}: FileUploaderProps) => {
  const { t: tWorkspace } = useTranslate('workspace');

  return (
    <Box data-testid='file-uploader'>
      <DecaFileUpload onDrop={onDrop} />

      {uploadingFiles.length > 0 && (
        <Flex direction='column' gap={rem(32)}>
          <Title order={6} fw={500}>
            {tWorkspace('uploading')}
          </Title>
          <Flex direction='column' gap={rem(32)}>
            {uploadingFiles.map((file) => (
              <DecaFileUpload.Item
                key={getFileKey(file)}
                mimeType={file.type}
                size={file.size}
                status={
                  file.status ?? (
                    <DecaProgressBar
                      value={uploadProgress[getFileKey(file)]}
                      styles={{ root: { width: '100%' } }}
                    />
                  )
                }
                title={file.name}
                onRetry={
                  file.status === undefined ||
                  (typeof file.status !== 'string' && isValidElement(file.status))
                    ? undefined
                    : () => onRetryUploadingFile(file)
                }
              />
            ))}
          </Flex>
        </Flex>
      )}
    </Box>
  );
};
