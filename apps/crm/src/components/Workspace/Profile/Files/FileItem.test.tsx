import type { Attachment } from '@/models/attachment';
import { renderWithMantine } from '@/tests/utils/testUtils';
import { fireEvent, screen } from '@testing-library/react';
import { beforeEach, describe, expect, it, vi } from 'vitest';
import { FileItem } from './FileItem';

// Mock the required components
vi.mock('@resola-ai/ui/components', () => ({
  DecaFileUpload: {
    Item: ({ mimeType, size, title, onDownload }) => (
      <div data-testid='file-upload-item'>
        <div data-testid='file-mimetype'>{mimeType}</div>
        <div data-testid='file-size'>{size}</div>
        <div data-testid='file-title'>{title}</div>
        <button data-testid='download-button' onClick={onDownload}>
          Download
        </button>
      </div>
    ),
  },
}));

// Mock the Mantine Menu component
vi.mock('@mantine/core', () => ({
  ...vi.importActual('@mantine/core'),
  Menu: {
    Target: ({ children }) => <div data-testid='menu-target'>{children}</div>,
    Dropdown: ({ children }) => <div data-testid='menu-dropdown'>{children}</div>,
    Item: ({ children, onClick, color, disabled }) => (
      <button data-testid={`menu-item-${color || 'default'}`} disabled={disabled} onClick={onClick}>
        {children}
      </button>
    ),
  },
  ActionIcon: ({ children, 'data-testid': dataTestId }) => (
    <button data-testid={dataTestId}>{children}</button>
  ),
  Box: ({ children, onClick, style }) => (
    <div data-testid='file-box' onClick={onClick} style={style}>
      {children}
    </div>
  ),
  Flex: ({ children }) => <div data-testid='flex-container'>{children}</div>,
  Text: ({ children }) => <span>{children}</span>,
  rem: (value) => `${value}px`,
}));

// Mock the icon components
vi.mock('@tabler/icons-react', () => ({
  IconDots: () => <div data-testid='dots-icon'>Dots</div>,
  IconChevronRight: () => <div data-testid='chevron-icon'>Chevron</div>,
}));

// Mock the useDisclosure hook
vi.mock('@mantine/hooks', () => ({
  useDisclosure: () => {
    return [
      false,
      {
        open: vi.fn(),
        close: vi.fn(),
      },
    ];
  },
}));

// Mock the tolgee translation
vi.mock('@tolgee/react', () => ({
  useTranslate: () => ({
    t: (key) => key,
  }),
}));

// Mock the FilePreviewModal components
vi.mock('./FilePreviewModal', () => ({
  ImagePreviewModal: ({ opened, fileId, fileTitle }) => (
    <div data-testid='image-preview-modal'>
      <div data-testid='modal-opened'>{String(opened)}</div>
      <div data-testid='modal-file-id'>{fileId}</div>
      <div data-testid='modal-file-title'>{fileTitle}</div>
    </div>
  ),
  PDFPreviewModal: ({ opened, fileId, fileTitle }) => (
    <div data-testid='pdf-preview-modal'>
      <div data-testid='modal-opened'>{String(opened)}</div>
      <div data-testid='modal-file-id'>{fileId}</div>
      <div data-testid='modal-file-title'>{fileTitle}</div>
    </div>
  ),
}));

describe('FileItem Component', () => {
  const mockOnDelete = vi.fn();
  const mockOnDownload = vi.fn();
  const mockOnChangeCategory = vi.fn().mockResolvedValue(undefined);

  // Sample attachment
  const baseAttachment: Attachment = {
    id: 'file-123',
    path: '/path/to/file',
    mimeType: 'text/plain',
    size: 12345,
    title: 'sample-file.txt',
    url: 'https://example.com/sample-file.txt',
    createdAt: '2023-01-01T00:00:00Z',
    updatedAt: '2023-01-01T00:00:00Z',
    categoryId: '',
  };

  beforeEach(() => {
    vi.clearAllMocks();
  });

  it('renders file item with correct file data', () => {
    renderWithMantine(
      <FileItem
        file={baseAttachment}
        onDelete={mockOnDelete}
        onDownload={mockOnDownload}
        onChangeCategory={mockOnChangeCategory}
      />
    );

    // Check basic rendering
    expect(screen.getByTestId('file-item')).toBeInTheDocument();
    expect(screen.getByTestId('file-mimetype')).toHaveTextContent('text/plain');
    expect(screen.getByTestId('file-size')).toHaveTextContent('12345');
    expect(screen.getByTestId('file-title')).toHaveTextContent('sample-file.txt');
  });

  it('handles download button click', () => {
    renderWithMantine(
      <FileItem
        file={baseAttachment}
        onDelete={mockOnDelete}
        onDownload={mockOnDownload}
        onChangeCategory={mockOnChangeCategory}
      />
    );

    // Click the download button
    fireEvent.click(screen.getByTestId('download-button'));

    // Check if onDownload was called with the file ID
    expect(mockOnDownload).toHaveBeenCalledWith('file-123');
  });
});
