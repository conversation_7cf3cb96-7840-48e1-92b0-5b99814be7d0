import { useWorkspaceContext } from '@/contexts/WorkspaceContext';
import { useAttachmentDelete, useAttachments } from '@/hooks';
import type { Attachment } from '@/models/attachment';
import { FileEnhanced } from '@/models/file';
import { AssetAPI, AttachmentAPI, ObjectAPI, UploadAPI } from '@/services/api';
import { ActionIcon, Box, Center, Flex, Loader, Title, rem } from '@mantine/core';
import { createStyles } from '@mantine/emotion';
import { useDisclosure } from '@mantine/hooks';
import { type DecaFileUploadProps, DecaTooltip } from '@resola-ai/ui/components';
import { showCustomNotification } from '@resola-ai/ui/components/DecaTable/utils';
import { IconList } from '@tabler/icons-react';
import { useTranslate } from '@tolgee/react';
import omitBy from 'lodash/omitBy';
import { memo, useState } from 'react';
import { useParams } from 'react-router-dom';
import { CategoryManager } from './CategoryManager';
import { FileItem } from './FileItem';
import { FileRemovalModal } from './FileRemovalModal';
import { FileUploader } from './FileUploader';
import { FilesCategoryGroup } from './FilesCategoryGroup';
import { downloadFile, fileEquals, getFileKey } from './utils';

export type Category = {
  id: string;
  name: string;
};

const useStyles = createStyles(() => ({
  emptyState: {
    ['& img']: {
      height: rem(100),
      width: rem(100),
    },
  },
}));

const Files = () => {
  const { wsId: workspaceId = '', id: objectId = '', recordId = '' } = useParams();
  const { classes } = useStyles();
  const {
    data = [],
    error,
    isLoading: isLoadingAttachments,
    mutate: refetchAttachments,
  } = useAttachments({
    objectId,
    recordId,
    workspaceId,
    sort: { createdAt: 'desc' },
  });

  const { object, mutateObject } = useWorkspaceContext();
  const { t: tWorkspace } = useTranslate('workspace');

  // File deletion state
  const [modalOpen, { close: closeModal, open: openModal }] = useDisclosure(false);
  const [removingAttachment, setRemovingAttachment] = useState({ id: '', name: '' });
  const { isMutating: isDeletingAttachment, trigger: triggerDeleteAttachment } =
    useAttachmentDelete({
      attachmentId: removingAttachment.id,
      objectId,
      recordId,
      workspaceId,
    });

  // File upload state
  const [uploadingFiles, setUploadingFiles] = useState<FileEnhanced[]>([]);
  const [uploadProgress, setUploadProgress] = useState<Record<string, number>>({});

  // Category management state
  const [categoryModalOpen, { close: closeCategoryModal, open: openCategoryModal }] =
    useDisclosure(false);
  const [isSavingCategories, setIsSavingCategories] = useState(false);

  // File deletion handlers
  const handleDeleteClick = (id: string, name: string) => {
    setRemovingAttachment({ id, name });
    openModal();
  };

  const deleteAttachment = async () => {
    await triggerDeleteAttachment();
    setRemovingAttachment({ id: '', name: '' });
    closeModal();
    showCustomNotification(tWorkspace('fileDeleted'));
    await refetchAttachments();
  };

  const handleDownload = async (id: string) => {
    const attachment = await AttachmentAPI.get({
      workspaceId,
      objectId,
      recordId,
      attachmentId: id,
    });
    if (attachment) {
      downloadFile(attachment.url);
    }
  };

  // Category change handler
  const handleChangeCategoryClick = async (attachmentId: string, categoryId: string) => {
    try {
      await AttachmentAPI.update({
        workspaceId,
        objectId,
        recordId,
        attachmentId,
        categoryId: categoryId ?? ' ', // Using a space instead of empty string
      });
      await refetchAttachments();
    } catch (err) {
      console.error(err);
    }
  };

  // Save category changes
  const handleSaveCategories = async (categories: Category[]) => {
    if (!object) return;

    setIsSavingCategories(true);
    try {
      // Create updated object with new categories
      const updatedObject = {
        ...object,
        attachmentCategories: categories,
      };

      // Use ObjectAPI.update instead of mutateObject
      await ObjectAPI.update(workspaceId, updatedObject);

      // Refresh the object data after update
      if (mutateObject) {
        await mutateObject({ ...updatedObject }, false);
      }

      closeCategoryModal();
    } catch (error) {
      console.error('Failed to save categories:', error);
    } finally {
      setIsSavingCategories(false);
    }
  };

  // File upload handlers
  const handleDrop: DecaFileUploadProps['onDrop'] = async ([file]) => {
    const fileExists = uploadingFiles.find((f) => fileEquals(f, file));

    if (fileExists && fileExists.status === (file as FileEnhanced).status) return;

    const controller = new AbortController();
    const uploadingFile = new FileEnhanced([file], file.name, {
      controller,
      lastModified: file.lastModified,
      type: file.type,
    });

    setUploadingFiles((prevState) => [uploadingFile, ...prevState]);

    try {
      const { file: asset, uploadUrl } = await AssetAPI.save(file, 'attachment');

      await UploadAPI.update({
        file,
        url: uploadUrl,
        config: {
          signal: controller.signal,
          onUploadProgress: (event) => {
            setUploadProgress((prevState) => ({
              ...prevState,
              [getFileKey(file)]: event.lengthComputable ? event.progress! * 100 : 0,
            }));
          },
        },
      });

      await AttachmentAPI.save(
        { workspaceId, objectId, recordId },
        {
          assetId: asset.externalId,
          mimeType: asset.mimeType,
          path: asset.path,
          size: asset.size,
          title: file.name,
          url: asset.url,
          assetType: 'attachment',
        }
      );

      setUploadingFiles((prevState) => prevState.filter((f) => !fileEquals(f, file)));
      await refetchAttachments();
    } catch (err) {
      console.error('Error while marking file as upload failed:', err);

      setUploadingFiles((prevState) =>
        prevState.map((file) =>
          file === uploadingFile
            ? new FileEnhanced([uploadingFile], uploadingFile.name, {
                controller: uploadingFile.controller,
                lastModified: uploadingFile.lastModified,
                status: 'fileStatus.uploadFailed',
                type: uploadingFile.type,
              })
            : file
        )
      );
    } finally {
      setUploadProgress((prevState) => omitBy(prevState, (_, key) => key === getFileKey(file)));
    }
  };

  const handleRetryUploadingFile = (file: FileEnhanced) => {
    setUploadingFiles((prevState) => prevState.filter((f) => !fileEquals(f, file)));
    setUploadProgress((prevState) => omitBy(prevState, (_, key) => key === getFileKey(file)));
    handleDrop([
      new FileEnhanced([file], file.name, {
        controller: file.controller,
        lastModified: file.lastModified,
        type: file.type,
      }),
    ]);
  };

  // Show loading or error state
  if (error) return <Box>{error.toString()}</Box>;
  if (isLoadingAttachments)
    return (
      <Center maw='100%' h='100%' mx='auto' data-testid='loading-indicator'>
        <Loader />
      </Center>
    );

  // Organize files by category
  const categorizedFiles: Record<string, Attachment[]> = {};
  const uncategorizedFiles: Attachment[] = [];
  const categories = object?.attachmentCategories ?? [];

  if (categories.length > 0) {
    categories.forEach((category) => {
      categorizedFiles[category.id] = [];
    });
  }

  data.forEach((file) => {
    if (file.categoryId && categorizedFiles[file.categoryId]) {
      categorizedFiles[file.categoryId].push(file);
    } else {
      uncategorizedFiles.push(file);
    }
  });

  return (
    <>
      <Flex direction='column' gap={rem(18)} data-testid='files-container'>
        {/* Header */}
        <Flex justify='space-between' align='center'>
          <Title order={5} fw={500}>
            {tWorkspace('files')}
          </Title>
          <DecaTooltip label={tWorkspace('customCategories')} ml={rem(-3)}>
            <ActionIcon
              variant='subtle'
              radius='xl'
              onClick={openCategoryModal}
              color='decaGrey.4'
              ml={rem(8)}
            >
              <IconList size={20} />
            </ActionIcon>
          </DecaTooltip>
        </Flex>

        {/* File Uploader */}
        <FileUploader
          onDrop={handleDrop}
          uploadingFiles={uploadingFiles}
          uploadProgress={uploadProgress}
          onRetryUploadingFile={handleRetryUploadingFile}
        />

        {/* Uncategorized Files */}
        {uncategorizedFiles.length > 0 && (
          <Flex direction='column' gap={rem(16)} data-testid='uncategorized-files'>
            {uncategorizedFiles.map((file) => (
              <FileItem
                key={file.id}
                file={file}
                onDelete={handleDeleteClick}
                onDownload={handleDownload}
                categories={categories}
                onChangeCategory={handleChangeCategoryClick}
              />
            ))}
          </Flex>
        )}

        {/* Categorized Files */}
        {Object.entries(categorizedFiles).map(([categoryId, files]) => {
          const category = categories.find((cat) => cat.id === categoryId);
          if (!category) return null;

          return (
            <FilesCategoryGroup
              key={categoryId}
              category={category}
              files={files}
              onDeleteFile={handleDeleteClick}
              onDownloadFile={handleDownload}
              categories={categories}
              onChangeCategory={handleChangeCategoryClick}
              className={classes.emptyState}
            />
          );
        })}
      </Flex>

      {/* Modals */}
      <FileRemovalModal
        opened={modalOpen}
        onClose={closeModal}
        fileName={removingAttachment.name}
        isDeleting={isDeletingAttachment}
        onDelete={deleteAttachment}
      />

      <CategoryManager
        opened={categoryModalOpen}
        onClose={closeCategoryModal}
        categories={categories}
        onSave={handleSaveCategories}
        isSaving={isSavingCategories}
      />
    </>
  );
};

export default memo(Files);
