import type { Attachment } from '@/models/attachment';
import { ActionIcon, Box, Flex, Menu, Text, rem } from '@mantine/core';
import { useDisclosure } from '@mantine/hooks';
import { DecaFileUpload } from '@resola-ai/ui/components';
import { IconChevronRight, IconDots } from '@tabler/icons-react';
import { useTranslate } from '@tolgee/react';
import { ImagePreviewModal, PDFPreviewModal } from './FilePreviewModal';
import type { Category } from './index';

type FileItemProps = {
  file: Attachment;
  onDelete: (id: string, name: string) => void;
  onDownload: (id: string) => void;
  categories?: Category[];
  onChangeCategory: (attachmentId: string, categoryId: string) => Promise<void>;
};

// File Item Component
export const FileItem = ({
  file,
  onDelete,
  categories,
  onChangeCategory,
  onDownload,
}: FileItemProps) => {
  const { t: tWorkspace } = useTranslate('workspace');
  const { id, mimeType, size, title, categoryId } = file;
  const shouldShowCategoryMenu = categories && categories.length > 0;
  const [imagePreviewOpened, { open: openImagePreview, close: closeImagePreview }] =
    useDisclosure(false);
  const [pdfPreviewOpened, { open: openPdfPreview, close: closePdfPreview }] = useDisclosure(false);

  // Check if the file is an image
  const isImage = mimeType?.startsWith('image/');
  // Check if the file is a PDF
  const isPdf = mimeType === 'application/pdf';

  // Handle onclick for files
  const handleOnClick = () => {
    if (isImage) {
      openImagePreview();
    }
    if (isPdf) {
      openPdfPreview();
    }
  };

  return (
    <Flex key={id} align='center' gap={rem(8)} data-testid='file-item'>
      <Box
        sx={{ flex: 1, width: '90%' }}
        onClick={handleOnClick}
        style={{ cursor: isImage || isPdf ? 'pointer' : 'default' }}
        bg='white'
      >
        <DecaFileUpload.Item
          key={id}
          mimeType={mimeType}
          size={size}
          title={title}
          status='fileStatus.uploaded'
          onDownload={(e) => {
            e.stopPropagation();
            onDownload(id);
          }}
        />
      </Box>

      <Menu position='bottom-end' withinPortal data-testid='category-menu'>
        <Menu.Target>
          <ActionIcon
            radius='xl'
            variant='subtle'
            color='gray'
            aria-label={tWorkspace('changeCategory')}
            title={tWorkspace('changeCategory')}
            data-testid='menu-button'
          >
            <IconDots size={16} />
          </ActionIcon>
        </Menu.Target>
        <Menu.Dropdown miw={rem(150)}>
          {/* Move to category submenu */}
          {shouldShowCategoryMenu && (
            <Menu.Item closeMenuOnClick={false} fz={rem(14)}>
              <Menu
                position='right-start'
                trigger='hover'
                withinPortal
                offset={{ alignmentAxis: -10, mainAxis: 20 }}
              >
                <Menu.Target>
                  <Flex align='center' justify='space-between'>
                    <Text fz={rem(14)}>{tWorkspace('moveTo')}</Text>
                    <IconChevronRight size={14} />
                  </Flex>
                </Menu.Target>
                <Menu.Dropdown miw={rem(150)}>
                  {categories.map((category) => (
                    <Menu.Item
                      fz={rem(14)}
                      key={category.id}
                      onClick={() => onChangeCategory(id, category.id)}
                      disabled={categoryId === category.id}
                    >
                      {category.name}
                    </Menu.Item>
                  ))}
                </Menu.Dropdown>
              </Menu>
            </Menu.Item>
          )}

          {/* Remove from category option - only show if file is in a category */}
          {categoryId && categoryId !== ' ' && (
            <Menu.Item fz={rem(14)} onClick={() => onChangeCategory(id, '')} color='red'>
              {tWorkspace('ungroupedFields')}
            </Menu.Item>
          )}
          <Menu.Item fz={rem(14)} color='red' onClick={() => onDelete(id, title)}>
            {tWorkspace('delete')}
          </Menu.Item>
        </Menu.Dropdown>
      </Menu>

      {/* Modals */}
      {isImage && (
        <ImagePreviewModal
          opened={imagePreviewOpened}
          onClose={closeImagePreview}
          fileId={id}
          fileTitle={title}
        />
      )}
      {isPdf && (
        <PDFPreviewModal
          opened={pdfPreviewOpened}
          onClose={closePdfPreview}
          fileId={id}
          fileTitle={title}
        />
      )}
    </Flex>
  );
};
