import { FileEnhanced } from '@/models/file';
import { renderWithMantine } from '@/tests/utils/testUtils';
import { fireEvent, screen } from '@testing-library/react';
import { vi } from 'vitest';
import { FileUploader } from './FileUploader';
import { getFileKey } from './utils';

// Mock external components
vi.mock('@resola-ai/ui/components', () => {
  const DecaFileUploadMock = ({ onDrop }) => (
    <div data-testid='deca-file-upload' onClick={() => onDrop([new File([], 'test.pdf')])}>
      Drop files
    </div>
  );

  // Add Item component
  const ItemComponent = ({ title, mimeType, size, status, onRetry }) => (
    <div data-testid='file-item'>
      <span>{title}</span>
      <span>{mimeType}</span>
      <span>{size}</span>
      <div data-testid='status'>{status}</div>
      {onRetry && (
        <button data-testid='retry-button' onClick={onRetry}>
          Retry
        </button>
      )}
    </div>
  );

  // Add the Item component to the mock
  DecaFileUploadMock.Item = ItemComponent;

  return {
    DecaFileUpload: DecaFileUploadMock,
  };
});

// Mock DecaProgressBar component
vi.mock('@resola-ai/ui', () => ({
  DecaProgressBar: ({ value, styles }) => (
    <div data-testid='progress-bar' style={styles?.root}>
      {value}%
    </div>
  ),
}));

// Mock Tolgee translate function
vi.mock('@tolgee/react', () => ({
  useTranslate: () => ({
    t: vi.fn((key) => (key === 'uploading' ? 'Uploading' : key)),
  }),
}));

describe('FileUploader Component', () => {
  const mockOnDrop = vi.fn();
  const mockOnRetryUploadingFile = vi.fn();

  beforeEach(() => {
    vi.clearAllMocks();
  });

  it('renders empty state correctly', () => {
    renderWithMantine(
      <FileUploader
        onDrop={mockOnDrop}
        uploadingFiles={[]}
        uploadProgress={{}}
        onRetryUploadingFile={mockOnRetryUploadingFile}
      />
    );

    // Check that the component renders with the file upload component
    expect(screen.getByTestId('file-uploader')).toBeInTheDocument();
    expect(screen.getByTestId('deca-file-upload')).toBeInTheDocument();

    // Should not show uploading section when no files are uploading
    expect(screen.queryByText('Uploading')).not.toBeInTheDocument();
  });

  it('handles file drop correctly', () => {
    renderWithMantine(
      <FileUploader
        onDrop={mockOnDrop}
        uploadingFiles={[]}
        uploadProgress={{}}
        onRetryUploadingFile={mockOnRetryUploadingFile}
      />
    );

    // Simulate file drop
    fireEvent.click(screen.getByTestId('deca-file-upload'));

    // Check that onDrop was called with the file
    expect(mockOnDrop).toHaveBeenCalledTimes(1);
  });

  it('displays uploading files with progress bars', () => {
    // Create mock files for uploading
    const mockFiles = [
      new FileEnhanced(['content'], 'document.pdf', { type: 'application/pdf' }),
      new FileEnhanced(['content'], 'image.jpg', { type: 'image/jpeg' }),
    ];

    const mockProgress = {
      [getFileKey(mockFiles[0])]: 25,
      [getFileKey(mockFiles[1])]: 75,
    };

    renderWithMantine(
      <FileUploader
        onDrop={mockOnDrop}
        uploadingFiles={mockFiles}
        uploadProgress={mockProgress}
        onRetryUploadingFile={mockOnRetryUploadingFile}
      />
    );

    // Check that the uploading section is shown
    expect(screen.getByText('Uploading')).toBeInTheDocument();

    // Check files are displayed
    expect(screen.getAllByTestId('file-item')).toHaveLength(2);
    expect(screen.getByText('document.pdf')).toBeInTheDocument();
    expect(screen.getByText('image.jpg')).toBeInTheDocument();

    // Check progress bars are displayed
    const progressBars = screen.getAllByTestId('progress-bar');
    expect(progressBars).toHaveLength(2);
    expect(progressBars[0].textContent).toBe('25%');
    expect(progressBars[1].textContent).toBe('75%');
  });

  it('displays files with error status and retry button', () => {
    // Create mock file with error status
    const mockFile = new FileEnhanced(['content'], 'failed.txt', {
      type: 'text/plain',
      status: 'fileStatus.uploadFailed',
    });

    renderWithMantine(
      <FileUploader
        onDrop={mockOnDrop}
        uploadingFiles={[mockFile]}
        uploadProgress={{}}
        onRetryUploadingFile={mockOnRetryUploadingFile}
      />
    );

    // Check file with error status is displayed
    expect(screen.getByText('failed.txt')).toBeInTheDocument();

    // Check retry button is displayed and works
    const retryButton = screen.getByTestId('retry-button');
    expect(retryButton).toBeInTheDocument();

    // Click retry button
    fireEvent.click(retryButton);
    expect(mockOnRetryUploadingFile).toHaveBeenCalledWith(mockFile);
  });

  it('does not show retry button for files without error status', () => {
    // Create a React element to simulate the progress bar
    const progressElement = <div>Progress</div>;

    // Create mock file with a React element as status
    const mockFile = new FileEnhanced(['content'], 'pending.txt', {
      type: 'text/plain',
      status: progressElement,
    });

    renderWithMantine(
      <FileUploader
        onDrop={mockOnDrop}
        uploadingFiles={[mockFile]}
        uploadProgress={{}}
        onRetryUploadingFile={mockOnRetryUploadingFile}
      />
    );

    // Check file is displayed
    expect(screen.getByText('pending.txt')).toBeInTheDocument();

    // Retry button should not be displayed
    expect(screen.queryByTestId('retry-button')).not.toBeInTheDocument();
  });
});
