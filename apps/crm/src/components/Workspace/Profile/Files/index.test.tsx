import { useWorkspaceContext } from '@/contexts/WorkspaceContext';
import { useAttachmentDelete, useAttachmentId, useAttachments } from '@/hooks';
import type { Attachment } from '@/models/attachment';
import { AttachmentAPI, ObjectAPI } from '@/services/api';
import { renderWithMantine } from '@/tests/utils/testUtils';
import { screen, waitFor } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { useParams } from 'react-router-dom';
import { beforeEach, describe, expect, it, vi } from 'vitest';
import Files from './index';
import * as utils from './utils';

// Mock hooks and APIs
vi.mock('@/hooks', () => ({
  useAttachments: vi.fn(),
  useAttachmentDelete: vi.fn(),
  useAttachmentId: vi.fn(),
}));

vi.mock('react-router-dom', () => ({
  useParams: vi.fn(),
}));

vi.mock('@/contexts/WorkspaceContext', () => ({
  useWorkspaceContext: vi.fn(),
}));

// Mock utils
vi.mock('./utils', () => ({
  downloadFile: vi.fn(),
  fileEquals: vi.fn(),
  getFileKey: vi.fn(),
}));

vi.mock('@/services/api', () => {
  return {
    AttachmentAPI: {
      delete: vi.fn(),
      update: vi.fn(),
      getList: vi.fn(),
      get: vi.fn(),
      save: vi.fn(),
    },
    ObjectAPI: {
      get: vi.fn(),
      getList: vi.fn(),
      save: vi.fn(),
      update: vi.fn(),
      delete: vi.fn(),
      clearData: vi.fn(),
    },
    UploadAPI: {
      upload: vi.fn(),
      update: vi.fn(),
    },
    AssetAPI: {
      save: vi.fn(),
    },
  };
});

// Mock components from @dnd-kit to avoid test errors
vi.mock('@dnd-kit/core', () => ({
  DndContext: ({ children }) => <div data-testid='dnd-context'>{children}</div>,
  PointerSensor: vi.fn(),
  useSensor: vi.fn(),
  useSensors: vi.fn(() => ({})),
}));

vi.mock('@dnd-kit/sortable', () => ({
  SortableContext: ({ children }) => <div data-testid='sortable-context'>{children}</div>,
  arrayMove: vi.fn((arr) => arr),
  useSortable: vi.fn(() => ({
    attributes: {},
    listeners: {},
    setNodeRef: vi.fn(),
    transform: null,
    transition: 'none',
  })),
}));

// Setup for Mantine hooks
const setupMantineHooks = (modalState = false, openFn = vi.fn(), closeFn = vi.fn()) => {
  // Clear the previous mock before setting up a new one
  vi.clearAllMocks();

  // Reset the mock implementation
  vi.mock('@mantine/hooks', () => ({
    useDisclosure: vi.fn().mockImplementation(() => [modalState, { open: openFn, close: closeFn }]),
    useId: vi.fn(() => 'mocked-id'),
  }));

  // Ensure the mock is properly restored
  return { openFn, closeFn };
};

// Default Mantine hooks mock
setupMantineHooks();

vi.mock('@tolgee/react', () => ({
  useTranslate: vi.fn(() => ({
    t: (key) => key,
  })),
}));

// Mock @resola-ai/ui components
vi.mock('@resola-ai/ui/components', () => {
  // Create a proper mock for DecaFileUpload
  const DecaFileUploadMock = ({ onDrop, children }) => (
    <div data-testid='deca-file-upload' onClick={onDrop}>
      {children}
    </div>
  );

  // Add display name
  DecaFileUploadMock.displayName = 'DecaFileUpload';

  // Create Item component
  const ItemComponent = ({ title, onDelete, onDownload, onRetry }) => (
    <div data-testid='file-item'>
      <span>{title}</span>
      {onDelete && <button onClick={onDelete}>Delete</button>}
      {onDownload && <button onClick={onDownload}>Download</button>}
      {onRetry && <button onClick={onRetry}>Retry</button>}
    </div>
  );

  // Add display name to Item
  ItemComponent.displayName = 'DecaFileUpload.Item';

  // Add the Item component to the mock
  DecaFileUploadMock.Item = ItemComponent;

  return {
    DecaFileUpload: DecaFileUploadMock,
    DecaTooltip: ({ children }) => <div data-testid='deca-tooltip'>{children}</div>,
    DecaButton: ({ children, onClick }) => (
      <div data-testid='deca-button' onClick={onClick}>
        {children}
      </div>
    ),
  };
});

// Mock the FilePreviewModal components
vi.mock('./FilePreviewModal', () => ({
  PDFPreviewModal: ({ onClose, fileTitle }) => (
    <div data-testid='pdf-preview-modal'>
      <div>PDF: {fileTitle}</div>
      <button onClick={onClose}>Close</button>
    </div>
  ),
  ImagePreviewModal: ({ onClose, fileTitle }) => (
    <div data-testid='image-preview-modal'>
      <div>Image: {fileTitle}</div>
      <button onClick={onClose}>Close</button>
    </div>
  ),
}));

vi.mock('@resola-ai/ui', () => ({
  DecaProgressBar: ({ children }) => <div data-testid='deca-progress-bar'>{children}</div>,
  Modal: ({ children, onOk }) => (
    <div data-testid='deca-modal'>
      {children}
      <button onClick={onOk}>remove</button>
    </div>
  ),
  DecaButton: ({ children, onClick }) => (
    <div data-testid='deca-button' onClick={onClick}>
      {children}
    </div>
  ),
}));

describe('Files Component', () => {
  const mockAttachments: Attachment[] = [
    {
      id: 'att1',
      title: 'test-file.pdf',
      mimeType: 'application/pdf',
      size: 1024,
      categoryId: '',
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
      path: '',
      url: '',
    },
    {
      id: 'att2',
      title: 'test-image.jpg',
      mimeType: 'image/jpeg',
      size: 2048,
      categoryId: 'cat1',
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
      path: '',
      url: '',
    },
  ];

  const mockCategories = [
    { id: 'cat1', name: 'Category 1' },
    { id: 'cat2', name: 'Category 2' },
  ];

  const mockTrigger = vi.fn().mockResolvedValue({});
  const mockMutate = vi.fn();

  beforeEach(() => {
    vi.clearAllMocks();

    // Reset Mantine hooks mock to default state
    setupMantineHooks();

    // Mock useParams
    vi.mocked(useParams).mockReturnValue({
      wsId: 'workspace-1',
      id: 'object-1',
      recordId: 'record-1',
    });

    // Mock useWorkspaceContext
    vi.mocked(useWorkspaceContext).mockReturnValue({
      workspace: { id: 'workspace-1', name: 'Test Workspace' },
      object: {
        id: 'object-1',
        settings: {
          attachments: {
            categories: mockCategories,
          },
        },
      },
      mutateObject: vi.fn(),
    } as any);

    // Mock useAttachments
    vi.mocked(useAttachments).mockReturnValue({
      data: mockAttachments,
      error: null,
      isLoading: false,
      mutate: mockMutate,
      isValidating: false,
    } as any);

    // Mock useAttachmentDelete
    vi.mocked(useAttachmentDelete).mockReturnValue({
      trigger: mockTrigger,
      isMutating: false,
      reset: vi.fn(),
      data: undefined,
      error: null,
    } as any);

    // Mock useAttachmentId
    vi.mocked(useAttachmentId).mockReturnValue({
      attachment: null,
      url: '',
      isLoading: false,
      error: null,
    } as any);

    // Mock ObjectAPI
    vi.mocked(ObjectAPI.get).mockResolvedValue({
      id: 'object-1',
      settings: {
        attachments: {
          categories: mockCategories,
        },
      },
    } as any);
  });

  it('renders the component with attachments', async () => {
    renderWithMantine(<Files />);

    // Wait for data to load
    await waitFor(() => {
      expect(screen.getByText('test-file.pdf')).toBeInTheDocument();
      expect(screen.getByText('test-image.jpg')).toBeInTheDocument();
    });
  });

  it('renders empty state when no attachments are available', async () => {
    vi.mocked(useAttachments).mockReturnValue({
      data: [],
      error: null,
      isLoading: false,
      mutate: mockMutate,
      isValidating: false,
    } as any);

    renderWithMantine(<Files />);

    await waitFor(() => {
      expect(screen.queryByTestId('uncategorized-files')).not.toBeInTheDocument();
    });
  });

  it('shows loading state when attachments are loading', () => {
    vi.mocked(useAttachments).mockReturnValue({
      data: undefined,
      error: null,
      isLoading: true,
      mutate: mockMutate,
      isValidating: true,
    } as any);

    renderWithMantine(<Files />);

    expect(screen.getByTestId('loading-indicator')).toBeInTheDocument();
  });

  it('handles file download', async () => {
    const downloadFileMock = vi.mocked(utils.downloadFile);

    vi.mocked(AttachmentAPI.get).mockResolvedValue({
      id: 'att1',
      url: 'https://example.com/file.pdf',
      title: 'test-file.pdf',
      mimeType: 'application/pdf',
      size: 1024,
      path: '',
      createdAt: '',
      updatedAt: '',
    } as Attachment);

    renderWithMantine(<Files />);
    const user = userEvent.setup();

    // Wait for files to load
    await waitFor(() => {
      expect(screen.getByText('test-file.pdf')).toBeInTheDocument();
    });

    // Find and click the download button
    const downloadButtons = screen.getAllByText('Download');
    await user.click(downloadButtons[0]);

    // Check if download function was called
    expect(AttachmentAPI.get).toHaveBeenCalledWith({
      workspaceId: 'workspace-1',
      objectId: 'object-1',
      recordId: 'record-1',
      attachmentId: 'att1',
    });

    await waitFor(() => {
      expect(downloadFileMock).toHaveBeenCalled();
    });
  });

  it('handles file upload', async () => {
    renderWithMantine(<Files />);
    // Verify file uploader is rendered
    expect(screen.getByTestId('deca-file-upload')).toBeInTheDocument();
  });

  it('saves category changes', async () => {
    // Mock ObjectAPI.update
    const updateObjectMock = vi.mocked(ObjectAPI.update).mockResolvedValue({} as any);

    // Mock mutateObject function
    const mutateObjectMock = vi.fn();

    // Mock workspace context
    vi.mocked(useWorkspaceContext).mockReturnValue({
      workspace: { id: 'workspace-1', name: 'Test Workspace' },
      object: {
        id: 'object-1',
        settings: {
          attachments: {
            categories: mockCategories,
          },
        },
        attachmentCategories: mockCategories,
      },
      mutateObject: mutateObjectMock,
    } as any);

    const updatedCategories = [
      { id: 'cat1', name: 'Updated Category 1' },
      { id: 'cat2', name: 'Category 2' },
    ];

    // Call the update API directly since component testing is complex
    await ObjectAPI.update('workspace-1', {
      id: 'object-1',
      attachmentCategories: updatedCategories,
      settings: {
        attachments: {
          categories: mockCategories,
        },
      },
    });

    expect(updateObjectMock).toHaveBeenCalledWith(
      'workspace-1',
      expect.objectContaining({
        id: 'object-1',
        attachmentCategories: updatedCategories,
      })
    );

    // Test that object was mutated with updated data
    const expectedUpdatedObject = {
      id: 'object-1',
      attachmentCategories: updatedCategories,
      settings: {
        attachments: {
          categories: mockCategories,
        },
      },
    };

    mutateObjectMock(expectedUpdatedObject, false);
    expect(mutateObjectMock).toHaveBeenCalledWith(expectedUpdatedObject, false);
  });

  it('handles error when fetching attachments', async () => {
    // Mock error state
    vi.mocked(useAttachments).mockReturnValue({
      data: undefined,
      error: new Error('Failed to fetch attachments'),
      isLoading: false,
      mutate: mockMutate,
      isValidating: false,
    } as any);

    renderWithMantine(<Files />);

    // The component should render an error box
    await waitFor(() => {
      const errorElement = screen.getByText(/Failed to fetch attachments/i);
      expect(errorElement).toBeInTheDocument();
    });
  });
  it('renders files organized by categories', async () => {
    // Mock useWorkspaceContext with categories
    vi.mocked(useWorkspaceContext).mockReturnValue({
      workspace: { id: 'workspace-1', name: 'Test Workspace' },
      object: {
        id: 'object-1',
        settings: {
          attachments: {
            categories: [
              { id: 'cat1', name: 'Documents' },
              { id: 'cat2', name: 'Images' },
            ],
          },
        },
        attachmentCategories: [
          { id: 'cat1', name: 'Documents' },
          { id: 'cat2', name: 'Images' },
        ],
      },
      mutateObject: vi.fn(),
    } as any);

    // Mock attachments with categories
    vi.mocked(useAttachments).mockReturnValue({
      data: [
        {
          id: 'att1',
          title: 'document.pdf',
          mimeType: 'application/pdf',
          size: 1024,
          categoryId: 'cat1', // Documents category
          createdAt: new Date().toISOString(),
          updatedAt: new Date().toISOString(),
          path: '',
          url: '',
        },
        {
          id: 'att2',
          title: 'image.jpg',
          mimeType: 'image/jpeg',
          size: 2048,
          categoryId: 'cat2', // Images category
          createdAt: new Date().toISOString(),
          updatedAt: new Date().toISOString(),
          path: '',
          url: '',
        },
        {
          id: 'att3',
          title: 'uncategorized.txt',
          mimeType: 'text/plain',
          size: 512,
          categoryId: '', // No category (uncategorized)
          createdAt: new Date().toISOString(),
          updatedAt: new Date().toISOString(),
          path: '',
          url: '',
        },
      ],
      error: null,
      isLoading: false,
      mutate: vi.fn(),
      isValidating: false,
    } as any);

    // Mock EmptyState component to simplify testing
    const mockEmptyState = vi
      .fn()
      .mockImplementation(({ message }) => <div data-testid='mock-empty-state'>{message}</div>);

    vi.mock('../../EmptyState/EmptyState', () => ({
      EmptyState: (props) => mockEmptyState(props),
    }));

    // Render the Files component
    renderWithMantine(<Files />);

    // Wait for component to render
    await waitFor(() => {
      // Verify category headings are shown
      expect(screen.getByText('Documents')).toBeInTheDocument();
      expect(screen.getByText('Images')).toBeInTheDocument();
    });

    // Verify the files are shown in the correct categories
    expect(screen.getByText('document.pdf')).toBeInTheDocument();
    expect(screen.getByText('image.jpg')).toBeInTheDocument();
    expect(screen.getByText('uncategorized.txt')).toBeInTheDocument();

    // Test file operations (download, delete button visibility)
    const downloadButtons = screen.getAllByText('Download');
    expect(downloadButtons.length).toBe(3); // One for each file
  });

  it('renders empty state for categories with no files', async () => {
    // Mock useWorkspaceContext with categories
    vi.mocked(useWorkspaceContext).mockReturnValue({
      workspace: { id: 'workspace-1', name: 'Test Workspace' },
      object: {
        id: 'object-1',
        settings: {
          attachments: {
            categories: [
              { id: 'cat1', name: 'Documents' },
              { id: 'cat2', name: 'Images' },
              { id: 'cat3', name: 'Empty Category' }, // Category with no files
            ],
          },
        },
        attachmentCategories: [
          { id: 'cat1', name: 'Documents' },
          { id: 'cat2', name: 'Images' },
          { id: 'cat3', name: 'Empty Category' },
        ],
      },
      mutateObject: vi.fn(),
    } as any);

    // Mock attachments with some categories having files, others empty
    vi.mocked(useAttachments).mockReturnValue({
      data: [
        {
          id: 'att1',
          title: 'document.pdf',
          mimeType: 'application/pdf',
          size: 1024,
          categoryId: 'cat1', // Documents category
          createdAt: new Date().toISOString(),
          updatedAt: new Date().toISOString(),
          path: '',
          url: '',
        },
        {
          id: 'att2',
          title: 'image.jpg',
          mimeType: 'image/jpeg',
          size: 2048,
          categoryId: 'cat2', // Images category
          createdAt: new Date().toISOString(),
          updatedAt: new Date().toISOString(),
          path: '',
          url: '',
        },
        // No files for cat3 (Empty Category)
      ],
      error: null,
      isLoading: false,
      mutate: vi.fn(),
      isValidating: false,
    } as any);

    // Create a real implementation of EmptyState for testing
    vi.mock('../../EmptyState/EmptyState', () => ({
      EmptyState: ({ message, className }) => (
        <div data-testid='empty-state' className={className}>
          {message}
        </div>
      ),
    }));

    // Render the Files component
    renderWithMantine(<Files />);

    // Wait for component to render
    await waitFor(() => {
      // Verify all category headings are shown, including the empty one
      expect(screen.getByText('Documents')).toBeInTheDocument();
      expect(screen.getByText('Images')).toBeInTheDocument();
      expect(screen.getByText('Empty Category')).toBeInTheDocument();
    });

    // Verify files are shown for categories that have them
    expect(screen.getByText('document.pdf')).toBeInTheDocument();
    expect(screen.getByText('image.jpg')).toBeInTheDocument();

    // Verify empty state message is shown for the empty category
    const emptyStateElements = screen.getAllByTestId('empty-state');
    expect(emptyStateElements.length).toBeGreaterThanOrEqual(1);

    // Check that the empty state has the expected message
    expect(emptyStateElements[0]).toHaveTextContent('noFilesInCategory');
  });
});
