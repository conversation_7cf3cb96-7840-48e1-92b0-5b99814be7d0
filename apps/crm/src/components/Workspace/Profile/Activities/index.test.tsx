import { useActivities } from '@/hooks/useActivity';
import { renderWithMantine } from '@/tests/utils/testUtils';
import { screen } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { MemoryRouter } from 'react-router-dom';
import { beforeEach, describe, expect, it, vi } from 'vitest';
import Activities from './index';

// Mock the useActivity hook
vi.mock('@/hooks/useActivity', () => ({
  useActivities: vi.fn(),
}));

// Mock the ActivityModal component
vi.mock('./ActivityModal', () => ({
  ActivityModal: vi.fn(() => <div data-testid='activity-modal' />),
}));

// Mock DOMPurify
vi.mock('dompurify', () => ({
  default: {
    sanitize: vi.fn((content) => content),
  },
}));

// Mock syncContextData
vi.mock('@/utils', () => ({
  syncContextData: vi.fn((_, data) => `Mocked activity for ${data.id}`),
}));

// Mock ActivityEmailSms
vi.mock('@/templates/activitiesEmail', () => ({
  default: vi.fn(() => <div data-testid='activity-email-sms'>Mocked email/sms activity</div>),
}));

describe('Activities Component', () => {
  const mockActivities = [
    {
      id: 'activity1',
      actor: { type: 'user' },
      sentAt: '2023-05-01T12:00:00.000Z',
      template: '<p>Activity 1 template</p>',
      source: { type: 'deca.crm' },
      action: 'create',
      object: { id: 'obj1', type: 'deca.crm' },
    },
    {
      id: 'activity2',
      actor: { type: 'mail' },
      sentAt: '2023-05-02T12:00:00.000Z',
      template: '<p>Activity 2 template</p>',
      source: { type: 'deca.crm' },
      action: 'send',
      object: { id: 'obj2', type: 'deca.crm' },
    },
  ];

  beforeEach(() => {
    vi.clearAllMocks();
  });

  it('renders loading state when activities are not available', () => {
    (useActivities as ReturnType<typeof vi.fn>).mockReturnValue({ activities: null });

    renderWithMantine(
      <MemoryRouter>
        <Activities />
      </MemoryRouter>
    );

    expect(screen.getByTestId('activities-loading')).toBeInTheDocument();
  });

  it('renders activities when they are available', () => {
    (useActivities as ReturnType<typeof vi.fn>).mockReturnValue({ activities: mockActivities });

    renderWithMantine(
      <MemoryRouter>
        <Activities />
      </MemoryRouter>
    );

    expect(screen.getByText('activities')).toBeInTheDocument();
    expect(screen.getByText('create')).toBeInTheDocument();
    expect(screen.getByTestId('activity-email-sms')).toBeInTheDocument();
  });

  it('opens the ActivityModal when create button is clicked', async () => {
    const user = userEvent.setup();
    (useActivities as ReturnType<typeof vi.fn>).mockReturnValue({ activities: mockActivities });

    renderWithMantine(
      <MemoryRouter>
        <Activities />
      </MemoryRouter>
    );

    const createButton = screen.getByText('create');
    await user.click(createButton);

    expect(screen.getByTestId('activity-modal')).toBeInTheDocument();
  });
});
