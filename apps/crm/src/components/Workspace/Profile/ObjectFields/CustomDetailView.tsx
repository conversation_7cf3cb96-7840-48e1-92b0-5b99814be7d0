import { UNGROUPED_ID } from '@/constants/workspace';
import { useProfileContext } from '@/contexts/ProfileContext';
import { useWorkspaceContext } from '@/contexts/WorkspaceContext';
import { DndContext, type DragEndEvent, PointerSensor, useSensor, useSensors } from '@dnd-kit/core';
import { restrictToVerticalAxis } from '@dnd-kit/modifiers';
import { SortableContext, arrayMove, verticalListSortingStrategy } from '@dnd-kit/sortable';
import { Box, Button, Flex, ScrollArea, Text, rem } from '@mantine/core';
import { createStyles } from '@mantine/emotion';
import { createFormContext } from '@mantine/form';
import { Modal } from '@resola-ai/ui';
import { IconPlus, IconSpeakerphone } from '@tabler/icons-react';
import { useTranslate } from '@tolgee/react';
import camelCase from 'lodash/camelCase';
import React, { useState, useEffect, useMemo, useCallback, useRef } from 'react';
import { SortableGroupComponent, UngroupedFieldsComponent } from './CustomDetailViewComponents';
import type { InternalFieldGroup } from './GroupActionMenu';
import type { ProfileData } from './RenderProfileTypes';

interface FormGroupField {
  title: string;
  id: string;
}

const [GroupFormProvider, _useGroupFormContext, useGroupForm] = createFormContext<{
  groups: Record<string, FormGroupField>;
}>();

const safeId = (title: string) => camelCase(title);
const useStyle = createStyles((theme) => ({
  description: {
    borderRadius: theme.radius.md,
  },
}));

interface CustomDetailViewProps {
  opened: boolean;
  onClose: () => void;
  customObjectFields: ProfileData[];
}

interface GroupsContainerProps {
  fieldGroups: InternalFieldGroup[];
  getFieldsForGroup: (groupId: string) => ProfileData[];
  onEditGroup: (groupId: string, newTitle: string) => void;
  onDeleteGroup: (groupId: string) => void;
  onMoveToGroup: (fieldId: string, groupId: string) => void;
  onFieldsReordered: (groupId: string, updatedFields: ProfileData[]) => void;
  onGroupsReordered: (newGroups: InternalFieldGroup[]) => void;
  onValidationChange: (groupId: string, isValid: boolean) => void;
}

const GroupsContainer = React.memo<GroupsContainerProps>(
  ({
    fieldGroups,
    getFieldsForGroup,
    onEditGroup,
    onDeleteGroup,
    onMoveToGroup,
    onFieldsReordered,
    onGroupsReordered,
    onValidationChange,
  }) => {
    const { t } = useTranslate('workspace');
    const sensors = useSensors(
      useSensor(PointerSensor, {
        activationConstraint: {
          distance: 10,
        },
      })
    );

    const groupIds = useMemo(() => fieldGroups.map((group) => group.id), [fieldGroups]);

    const handleDragEnd = useCallback(
      (event: DragEndEvent) => {
        const { active, over } = event;
        if (!over || active.id === over.id) return;

        if (active.data?.current?.type === 'group') {
          const activeIndex = fieldGroups.findIndex((group) => group.id === active.id);
          const overIndex = fieldGroups.findIndex((group) => group.id === over.id);

          if (activeIndex !== -1 && overIndex !== -1) {
            const newOrder = arrayMove([...fieldGroups], activeIndex, overIndex);
            onGroupsReordered(newOrder);
          }
        }
      },
      [fieldGroups, onGroupsReordered]
    );

    const renderGroup = useCallback(
      (group: InternalFieldGroup) => {
        const groupFields = getFieldsForGroup(group.id);
        return (
          <SortableGroupComponent
            key={group.id}
            group={group}
            fields={groupFields}
            onEditGroup={onEditGroup}
            onDeleteGroup={onDeleteGroup}
            availableGroups={[
              ...fieldGroups,
              { id: UNGROUPED_ID, title: t('ungroupedFields'), fields: [] },
            ]}
            onMoveToGroup={onMoveToGroup}
            onFieldsReordered={onFieldsReordered}
            onValidationChange={onValidationChange}
            allGroups={fieldGroups}
          />
        );
      },
      [
        fieldGroups,
        getFieldsForGroup,
        onEditGroup,
        onDeleteGroup,
        onMoveToGroup,
        onFieldsReordered,
        onValidationChange,
        t,
      ]
    );

    return (
      <DndContext sensors={sensors} onDragEnd={handleDragEnd} modifiers={[restrictToVerticalAxis]}>
        <SortableContext items={groupIds} strategy={verticalListSortingStrategy}>
          <Box sx={{ display: 'flex', flexDirection: 'column', gap: 0 }}>
            {fieldGroups.map(renderGroup)}
          </Box>
        </SortableContext>
      </DndContext>
    );
  }
);
GroupsContainer.displayName = 'GroupsContainer';

const CustomDetailView: React.FC<CustomDetailViewProps> = ({
  opened,
  onClose,
  customObjectFields,
}) => {
  const { t } = useTranslate('workspace');
  const { classes } = useStyle();
  const { activeView, handleViewChange } = useWorkspaceContext();
  const { resetEditFields, setCustomObjectFields } = useProfileContext();

  const [fieldGroups, setFieldGroups] = useState<InternalFieldGroup[]>([]);
  const [ungroupedFieldIds, setUngroupedFieldIds] = useState<string[]>([]);
  const [workingFields, setWorkingFields] = useState<ProfileData[]>([]);
  const [deletedGroupIds] = useState<Set<string>>(new Set());
  const invalidGroupsRef = useRef<Set<string>>(new Set());
  const [forceUpdate, setForceUpdate] = useState(0);
  const hasReorderedRef = useRef(false);

  const formContext = useGroupForm({
    initialValues: {
      groups: {},
    },
  });

  const updateGroupValidation = useCallback((groupId: string, isValid: boolean) => {
    const invalidGroups = invalidGroupsRef.current;
    let changed = false;

    if (isValid) {
      if (invalidGroups.has(groupId)) {
        invalidGroups.delete(groupId);
        changed = true;
      }
    } else {
      if (!invalidGroups.has(groupId)) {
        invalidGroups.add(groupId);
        changed = true;
      }
    }

    if (changed) {
      setForceUpdate((count) => count + 1);
    }
  }, []);

  const hasInvalidGroups = useMemo(() => {
    return invalidGroupsRef.current.size > 0;
  }, [forceUpdate]);

  const handleGroupValidationChange = useCallback(
    (groupId: string, isValid: boolean) => {
      updateGroupValidation(groupId, isValid);
    },
    [updateGroupValidation]
  );

  useEffect(() => {
    if (hasReorderedRef.current) {
      return;
    }

    if (!opened || customObjectFields.length === 0) return;

    const updatedFields = customObjectFields.map((field) => {
      return {
        ...field,
        isDetailVisible: field.isDetailVisible ?? true,
      };
    });

    // Always update the working fields when the modal opens
    setWorkingFields(updatedFields);

    if (activeView?.fieldGroups && activeView.fieldGroups.length > 0) {
      const regularGroups: InternalFieldGroup[] = [];
      let ungroupedIds: string[] = [];

      activeView.fieldGroups.forEach((group) => {
        const isUngrouped = group.title === UNGROUPED_ID;
        const fieldIds = group.fields.map((field) => field.fieldMetaId);

        if (isUngrouped) {
          ungroupedIds = fieldIds;
        } else {
          const groupId = safeId(group.title);
          if (!deletedGroupIds.has(groupId)) {
            regularGroups.push({
              id: groupId,
              title: group.title,
              fields: fieldIds,
            });
          }
        }
      });

      const allGroupedFieldIds = new Set(regularGroups.flatMap((group) => group.fields));
      const missingUngroupedIds = updatedFields
        .map((field) => field.id!)
        .filter((id) => !allGroupedFieldIds.has(id) && !ungroupedIds.includes(id));

      setFieldGroups(regularGroups);
      setUngroupedFieldIds([...ungroupedIds, ...missingUngroupedIds]);
    } else {
      setFieldGroups([]);
      setUngroupedFieldIds(updatedFields.map((field) => field.id!));
    }
  }, [opened, customObjectFields, activeView, deletedGroupIds]);

  const ungroupedFields = useMemo(() => {
    return ungroupedFieldIds
      .map((fieldId) => workingFields.find((field) => field.id === fieldId))
      .filter(Boolean) as ProfileData[];
  }, [ungroupedFieldIds, workingFields]);

  const getFieldsForGroup = useCallback(
    (groupId: string): ProfileData[] => {
      const group = fieldGroups.find((g) => g.id === groupId);
      if (!group) return [];

      return group.fields
        .map((fieldId) => workingFields.find((field) => field.id === fieldId))
        .filter(Boolean) as ProfileData[];
    },
    [fieldGroups, workingFields]
  );

  const handleAddGroup = useCallback(() => {
    // Set the reordered flag to prevent re-initialization
    hasReorderedRef.current = true;

    const tempId = `group-${Date.now()}`;

    const newGroup: InternalFieldGroup = {
      id: tempId,
      title: '',
      fields: [],
    };

    setFieldGroups((prevGroups) => [newGroup, ...prevGroups]);
  }, []);

  const handleEditGroup = useCallback((groupId: string, newTitle: string) => {
    if (!newTitle.trim()) return;

    // Set the reordered flag to prevent re-initialization
    hasReorderedRef.current = true;

    const titleToUse = newTitle.trim();
    const newId = safeId(titleToUse);

    setFieldGroups((prevGroups) => {
      // Find the group to edit
      const groupToEdit = prevGroups.find((g) => g.id === groupId);
      if (!groupToEdit) {
        return prevGroups;
      }

      return prevGroups.map((group) => {
        if (group.id === groupId) {
          return {
            ...group,
            id: newId,
            title: titleToUse,
          };
        }
        return group;
      });
    });
  }, []);

  const handleDeleteGroup = useCallback(
    (groupId: string) => {
      const groupToDelete = fieldGroups.find((g) => g.id === groupId);
      if (!groupToDelete) return;

      const fieldsToMove = [...groupToDelete.fields];
      setUngroupedFieldIds((prevIds) => [...prevIds, ...fieldsToMove]);
      setFieldGroups((prevGroups) => prevGroups.filter((g) => g.id !== groupId));
    },
    [fieldGroups]
  );

  const handleMoveFieldToGroup = useCallback(
    (fieldId: string, targetGroupId: string) => {
      // Set the reordered flag to prevent re-initialization
      hasReorderedRef.current = true;

      let currentGroupId: string | null = null;
      const containingGroup = fieldGroups.find((group) => group.fields.includes(fieldId));

      if (containingGroup) {
        currentGroupId = containingGroup.id;
      } else if (ungroupedFieldIds.includes(fieldId)) {
        currentGroupId = UNGROUPED_ID;
      }

      if (targetGroupId === currentGroupId) return;

      if (currentGroupId === UNGROUPED_ID) {
        // Moving from ungrouped to a group
        setUngroupedFieldIds((prevIds) => prevIds.filter((id) => id !== fieldId));

        setFieldGroups((prevGroups) => {
          // Make sure the target group exists
          const targetGroupExists = prevGroups.some((group) => group.id === targetGroupId);
          if (!targetGroupExists) {
            return prevGroups;
          }

          return prevGroups.map((group) => {
            if (group.id === targetGroupId) {
              return {
                ...group,
                fields: [...group.fields, fieldId],
              };
            }
            return group;
          });
        });
      } else if (targetGroupId === UNGROUPED_ID) {
        setFieldGroups((prevGroups) =>
          prevGroups.map((group) => {
            if (group.id === currentGroupId) {
              return {
                ...group,
                fields: group.fields.filter((id) => id !== fieldId),
              };
            }
            return group;
          })
        );

        setUngroupedFieldIds((prevIds) => [...prevIds, fieldId]);
      } else {
        setFieldGroups((prevGroups) =>
          prevGroups.map((group) => {
            if (group.id === currentGroupId) {
              return {
                ...group,
                fields: group.fields.filter((id) => id !== fieldId),
              };
            }
            if (group.id === targetGroupId) {
              return {
                ...group,
                fields: [...group.fields, fieldId],
              };
            }
            return group;
          })
        );
      }

      const field = workingFields.find((f) => f.id === fieldId);
      if (!field) return;

      setCustomObjectFields((prevFields) => {
        return prevFields.map((prevField) => {
          if (prevField.id === fieldId) {
            return {
              ...prevField,
            };
          }
          return prevField;
        });
      });
    },
    [fieldGroups, ungroupedFieldIds, workingFields, setCustomObjectFields]
  );

  const handleGroupFieldsReordered = useCallback(
    (groupId: string, reorderedFields: ProfileData[]) => {
      // Set the reordered flag to prevent reinitialization
      hasReorderedRef.current = true;

      // First update the working fields
      setWorkingFields((prevFields) => {
        const fieldMap = new Map(prevFields.map((field) => [field.id, field]));

        reorderedFields.forEach((field) => {
          if (field.id) {
            // Make sure to preserve the isDetailVisible value
            fieldMap.set(field.id, {
              ...fieldMap.get(field.id)!,
              isDetailVisible: field.isDetailVisible,
            });
          }
        });

        return Array.from(fieldMap.values());
      });

      setFieldGroups((prevGroups) =>
        prevGroups.map((group) => {
          if (group.id === groupId) {
            return {
              ...group,
              fields: reorderedFields.map((field) => field.id!),
            };
          }
          return group;
        })
      );

      // Finally update the custom object fields with the new visibility states
      setCustomObjectFields((prevFields) => {
        const updatedFields = [...prevFields];

        reorderedFields.forEach((reorderedField) => {
          const index = updatedFields.findIndex((field) => field.id === reorderedField.id);
          if (index !== -1) {
            updatedFields[index] = {
              ...updatedFields[index],
              isDetailVisible: reorderedField.isDetailVisible,
            };
          }
        });

        return updatedFields;
      });
    },
    [setCustomObjectFields]
  );

  const handleUngroupedFieldsReordered = useCallback(
    (reorderedFields: ProfileData[]) => {
      if (!reorderedFields.length) {
        return;
      }

      hasReorderedRef.current = true;

      setWorkingFields((prevFields) => {
        const fieldMap = new Map(prevFields.map((field) => [field.id, field]));

        reorderedFields.forEach((field) => {
          if (field.id && fieldMap.has(field.id)) {
            fieldMap.set(field.id, {
              ...fieldMap.get(field.id)!,
              isDetailVisible: field.isDetailVisible,
            });
          }
        });

        return Array.from(fieldMap.values());
      });

      setUngroupedFieldIds(reorderedFields.map((field) => field.id!));

      setCustomObjectFields((prevFields) => {
        const updatedFields = [...prevFields];

        reorderedFields.forEach((reorderedField) => {
          const index = updatedFields.findIndex((field) => field.id === reorderedField.id);
          if (index !== -1) {
            updatedFields[index] = {
              ...updatedFields[index],
              isDetailVisible: reorderedField.isDetailVisible,
            };
          }
        });

        return updatedFields;
      });
    },
    [fieldGroups.length, ungroupedFieldIds.length, setCustomObjectFields]
  );

  const handleGroupsReordered = useCallback((newGroups: InternalFieldGroup[]) => {
    setFieldGroups(newGroups);
  }, []);

  const handleSave = useCallback(async () => {
    if (!activeView || hasInvalidGroups) return;

    const formattedGroupings = [
      ...fieldGroups.map((group) => ({
        title: group.title,
        fields: group.fields.map((fieldId) => ({
          fieldMetaId: fieldId,
          isVisible: true,
          isDetailVisible: workingFields.find((f) => f.id === fieldId)?.isDetailVisible ?? true,
        })),
      })),
      {
        title: UNGROUPED_ID,
        fields: ungroupedFieldIds.map((fieldId) => ({
          fieldMetaId: fieldId,
          isVisible: true,
          isDetailVisible: workingFields.find((f) => f.id === fieldId)?.isDetailVisible ?? true,
        })),
      },
    ];

    try {
      await handleViewChange(activeView.id, {
        ...activeView,
        fieldGroups: formattedGroupings,
      });
      onClose();
      resetEditFields();
    } catch (error) {
      console.error('Failed to update view:', error);
    }
  }, [
    activeView,
    workingFields,
    fieldGroups,
    ungroupedFieldIds,
    handleViewChange,
    onClose,
    resetEditFields,
    hasInvalidGroups,
  ]);

  useEffect(() => {
    if (!opened) {
      hasReorderedRef.current = false;
    }
  }, [opened]);

  return (
    <Modal
      size={'90vw'}
      opened={opened}
      title={t('customObjectFields')}
      centered
      onClose={onClose}
      onOk={hasInvalidGroups ? undefined : handleSave}
      onCancel={onClose}
      okText={t('apply')}
      cancelText={t('cancel')}
    >
      <GroupFormProvider form={formContext}>
        <ScrollArea.Autosize mah={`calc(80vh - ${rem(80)})`}>
          <Box>
            <Flex
              align='center'
              gap={rem(10)}
              p={rem(10)}
              m={rem(16)}
              w='fit-content'
              bg='decaBlue.0'
              c='decaBlue.6'
              className={classes.description}
            >
              <IconSpeakerphone size={18} />
              <Text>{t('customObjectFieldsDescription')}</Text>
            </Flex>

            <Flex justify='space-between' align='center' mb={rem(16)}>
              <Button
                leftSection={<IconPlus size={16} />}
                onClick={handleAddGroup}
                variant='subtle'
                c='decaBlue.5'
                fz='14'
              >
                {t('addGroup')}
              </Button>
            </Flex>
          </Box>

          <Box sx={{ flexGrow: 1, overflowX: 'hidden' }}>
            {fieldGroups.length > 0 && (
              <GroupsContainer
                fieldGroups={fieldGroups}
                getFieldsForGroup={getFieldsForGroup}
                onEditGroup={handleEditGroup}
                onDeleteGroup={handleDeleteGroup}
                onMoveToGroup={handleMoveFieldToGroup}
                onFieldsReordered={handleGroupFieldsReordered}
                onGroupsReordered={handleGroupsReordered}
                onValidationChange={handleGroupValidationChange}
              />
            )}

            {ungroupedFields.length > 0 && (
              <UngroupedFieldsComponent
                fields={ungroupedFields}
                availableGroups={fieldGroups}
                onMoveToGroup={handleMoveFieldToGroup}
                onFieldsReordered={handleUngroupedFieldsReordered}
              />
            )}
          </Box>
        </ScrollArea.Autosize>
      </GroupFormProvider>
    </Modal>
  );
};

export default React.memo(CustomDetailView);
