import { useWorkspaceContext } from '@/contexts/WorkspaceContext';
import { Box, Button, CopyButton, Flex, Textarea, rem } from '@mantine/core';
import { createStyles } from '@mantine/emotion';
import { IconCheck, IconCopy, IconEdit } from '@tabler/icons-react';
import { useTranslate } from '@tolgee/react';
import { useState } from 'react';
import type { ProfileData } from './RenderProfileTypes';

const useStyle = createStyles(() => ({
  contentWrapper: {
    minHeight: 200,
    '.mantine-Textarea-input': {
      border: 'none',
      padding: 0,
      fontSize: rem(14),
    },
  },
}));

const ViewAllModal = ({ data }: { data?: ProfileData }) => {
  const [isEditing, setIsEditing] = useState(false);
  const [editedValue, setEditedValue] = useState<string>(data?.mapValue || '');
  const { classes } = useStyle();
  const { t } = useTranslate('workspace');
  const { onSaveData, currRecordIndex } = useWorkspaceContext();

  const handleSave = async () => {
    await onSaveData(editedValue, currRecordIndex, data?.id, false);
    setEditedValue(editedValue.trim());
    setIsEditing(false);
  };

  return (
    <>
      <Box>
        <Flex justify='flex-start' align='center'>
          {!isEditing ? (
            <>
              <Button
                onClick={() => setIsEditing(true)}
                variant='white'
                color='blue'
                leftSection={<IconEdit size={16} />}
              >
                {t('edit')}
              </Button>
              <CopyButton value={editedValue || ''}>
                {({ copied, copy }) => (
                  <Button
                    leftSection={copied ? <IconCheck size={16} /> : <IconCopy size={16} />}
                    onClick={copy}
                    variant='white'
                    color='gray'
                  >
                    {t('copyToClipboard')}
                  </Button>
                )}
              </CopyButton>
            </>
          ) : (
            <>
              <Button
                onClick={() => {
                  setIsEditing(false);
                  setEditedValue(data?.mapValue || '');
                }}
                variant='white'
                color='decaGrey.6'
              >
                {t('cancel')}
              </Button>
              <Button onClick={handleSave} variant='subtle' color='blue'>
                {t('save')}
              </Button>
            </>
          )}
        </Flex>

        {isEditing ? (
          <Textarea
            value={editedValue}
            onChange={(e) => setEditedValue(e.target.value)}
            minRows={8}
            autosize
            autoFocus
            className={classes.contentWrapper}
          />
        ) : (
          <Box className={classes.contentWrapper}>{editedValue}</Box>
        )}
      </Box>
    </>
  );
};

export default ViewAllModal;
