import { Currency, CurrencySymbol } from '@/constants/workspace';
import { useBreadcrumbNavigation } from '@/hooks/useBreadcrumbNavigation';
import type { ObjectColumn, RelationshipConfig } from '@/models';
import { getCurrencyFormat } from '@/utils';
import { Avatar, Flex, Text, rem } from '@mantine/core';
import { CustomImageBackground, DecaStatus } from '@resola-ai/ui';
import { FieldTypes } from '@resola-ai/ui/components';
import { useTranslate } from '@tolgee/react';
import get from 'lodash/get';
import isEmpty from 'lodash/isEmpty';
import { Link } from 'react-router-dom';
import { DateTimeCell, LinkToRecordCell } from '../../TableCellRendering/Cell';

export interface ProfileData extends ObjectColumn {
  mapValue: any | any[] | RelationshipConfig;
  icon: any;
  isDetailVisible?: boolean;
}
export const RenderProfileTypes = ({
  field,
  imageRounded = false,
}: {
  field: ProfileData;
  imageRounded?: boolean;
}) => {
  const { navigateToLinkedRecord } = useBreadcrumbNavigation();
  const { t } = useTranslate('workspace');
  const handleClick = () => {
    if (!isEmpty(field.mapValue) && field.options?.objectId) {
      // Use breadcrumb navigation instead of opening in new tab
      navigateToLinkedRecord(field.options.objectId, field.mapValue.recordId, field.mapValue);
    }
  };

  switch (field.type) {
    case FieldTypes.CHECKBOX:
      return field.mapValue ? t('yes') : t('no');
    case FieldTypes.DATETIME:
    case FieldTypes.CREATED_TIME:
    case FieldTypes.MODIFIED_TIME: {
      return <DateTimeCell config={field.options} value={field.mapValue} />;
    }
    case FieldTypes.SINGLE_SELECT: {
      const select = field.options?.choices?.find((opt) => opt.id === field.mapValue);
      return select && <DecaStatus size='small' variant={select?.color} text={select?.label} />;
    }
    case FieldTypes.MULTI_SELECT: {
      const selects =
        field?.mapValue
          ?.map((value: string) => field.options?.choices?.find((opt) => opt.id === value))
          .filter(Boolean) || [];
      return (
        <Flex gap={rem(10)} wrap='wrap'>
          {selects?.map((o) => (
            <DecaStatus key={o.id} size='small' variant={o?.color} text={o?.label} />
          ))}
        </Flex>
      );
    }
    case FieldTypes.PERCENT: {
      const precision = field.options?.decimalPlaces || 1;
      const separatorFormat = field.options?.separator?.enabled
        ? field.options?.separator?.format
        : 'commaPeriod';
      return getCurrencyFormat(field.mapValue, '%', separatorFormat, precision, true);
    }
    case FieldTypes.RELATIONSHIP: {
      return (
        <Text
          component='span'
          onClick={handleClick}
          className='hyperlink'
          style={{ cursor: !isEmpty(field.mapValue) ? 'pointer' : 'auto' }}
        >
          <LinkToRecordCell config={field.options} objectValue={field.mapValue} />
        </Text>
      );
    }
    case FieldTypes.IMAGE: {
      return field?.mapValue ? (
        <CustomImageBackground
          height={rem(28)}
          width={rem(28)}
          url={field?.mapValue}
          rounded={imageRounded}
        />
      ) : (
        <Avatar size={28} />
      );
    }
    case FieldTypes.MODIFIED_BY:
    case FieldTypes.CREATED_BY: {
      return field.mapValue?.name || '';
    }
    case FieldTypes.CURRENCY: {
      const symbol = CurrencySymbol[field.options?.currency || Currency.yen];
      const separatorFormat = field.options?.separator?.enabled
        ? field.options?.separator?.format
        : 'local';
      return field.mapValue
        ? getCurrencyFormat(+field.mapValue, symbol, separatorFormat, field.options?.decimalPlaces)
        : '';
    }
    case FieldTypes.AUTONUMBER: {
      return field.mapValue ? field.mapValue : '';
    }
    case FieldTypes.URL: {
      return field.mapValue ? (
        <Link to={field.mapValue} target='_blank' className='hyperlink'>
          {field.mapValue}
        </Link>
      ) : (
        ''
      );
    }
    default: {
      const value = get(field, 'mapValue', '');
      return typeof value !== 'object' ? value : '';
    }
  }
};
