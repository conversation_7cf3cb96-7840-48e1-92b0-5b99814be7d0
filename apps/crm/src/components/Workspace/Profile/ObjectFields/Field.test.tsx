import { AppContextProvider } from '@/contexts/AppContext';
import { useProfileContext } from '@/contexts/ProfileContext';
import { useWorkspaceContext } from '@/contexts/WorkspaceContext';
import { renderWithMantine } from '@/tests/utils/testUtils';
import { FieldTypes } from '@resola-ai/ui/components';
import { fireEvent, screen, waitFor } from '@testing-library/react';
import { BrowserRouter } from 'react-router-dom';
import { vi } from 'vitest';
import { Field } from './Field';
import type { ProfileData } from './RenderProfileTypes';

// Mock react-router-dom
vi.mock('react-router-dom', () => ({
  useParams: () => ({ wsId: 'workspace-1' }),
  useNavigate: () => vi.fn(),
  BrowserRouter: ({ children }: any) => <div>{children}</div>,
  Link: ({ children, to, className, target }: any) => (
    <a href={to} className={className} target={target}>
      {children}
    </a>
  ),
}));

// Mock BreadcrumbContext
vi.mock('@/contexts/BreadcrumbContext', () => ({
  useBreadcrumbContext: () => ({
    breadcrumbs: [],
    addBreadcrumb: vi.fn(),
    navigateToBreadcrumb: vi.fn(),
    clearBreadcrumbs: vi.fn(),
  }),
}));

// Mock useBreadcrumbNavigation
vi.mock('@/hooks/useBreadcrumbNavigation', () => ({
  useBreadcrumbNavigation: () => ({
    navigateToLinkedRecord: vi.fn(),
  }),
}));

// Mock @resola-ai/ui/hooks
vi.mock('@resola-ai/ui/hooks', () => ({
  usePathParams: () => ({
    createPathWithLngParam: (path: string) => path,
  }),
}));

// Mock the context hooks
vi.mock('@/contexts/WorkspaceContext', () => ({
  useWorkspaceContext: vi.fn(),
  WorkspaceContextProvider: ({ children }) => children,
}));

vi.mock('@/contexts/ProfileContext', () => ({
  useProfileContext: vi.fn(),
  ProfileContextProvider: ({ children }) => children,
}));

// Mock AppContext
vi.mock('@/contexts/AppContext', () => ({
  useAppContext: vi.fn(() => ({
    isManager: true,
  })),
  AppContextProvider: ({ children }) => children,
}));

// Mock tolgee
vi.mock('@tolgee/react', () => ({
  useTranslate: vi.fn(() => ({
    t: (key, options) => key + (options ? JSON.stringify(options) : ''),
  })),
  Tolgee: vi.fn(() => ({
    use: vi.fn().mockReturnThis(),
    init: vi.fn().mockReturnThis(),
    run: vi.fn(),
  })),
  FormatSimple: vi.fn(() => ({
    use: vi.fn().mockReturnThis(),
  })),
  InContextTools: vi.fn(() => ({
    use: vi.fn().mockReturnThis(),
  })),
  T: vi.fn(({ children }) => children),
}));

// Mock web tools
vi.mock('@tolgee/web/tools');

// Mock AppConfig
vi.mock('@/configs', () => ({
  default: {
    TOLGEE_TOOLS_ENABLED: false,
    TOLGEE_URL: 'https://example.com',
    TOLGEE_KEY: 'test-key',
  },
}));

// Mock shared constants
vi.mock('@resola-ai/shared-constants', () => ({
  DEFAULT_LANGUAGE: 'en',
  PREFERENCE_LANGUAGE: 'en',
}));

// Mock local tolgee module
vi.mock('@/tolgee/index', () => ({
  tolgee: {
    use: vi.fn().mockReturnThis(),
    init: vi.fn().mockReturnThis(),
    run: vi.fn(),
  },
}));

// Wrapper component with context providers
const renderWithContext = (ui: React.ReactElement) => {
  return renderWithMantine(
    <BrowserRouter>
      <AppContextProvider>{ui}</AppContextProvider>
    </BrowserRouter>
  );
};

describe('Field Component', () => {
  const mockData: ProfileData = {
    id: 'test-field',
    type: FieldTypes.SINGLE_LINE_TEXT,
    mapValue: 'Test Value',
    options: {},
    isDetailVisible: true,
    icon: 'test-icon',
    header: 'Test Header',
  };

  const mockContextValues = {
    onSaveData: vi.fn(),
    currRecordIndex: 0,
    handleViewChange: vi.fn(),
    activeView: {
      id: 'view1',
      displayLongText: [],
      locked: false,
    },
  };

  const mockProfileContextValues = {
    editFields: {},
    onEditField: vi.fn(),
    resetEditFields: vi.fn(),
    mutateProfile: vi.fn(),
    handleSaveRecord: vi.fn(),
    profile: { id: 'test-record' },
  };

  beforeEach(() => {
    vi.clearAllMocks();
    (useWorkspaceContext as unknown as ReturnType<typeof vi.fn>).mockReturnValue(mockContextValues);
    (useProfileContext as unknown as ReturnType<typeof vi.fn>).mockReturnValue(
      mockProfileContextValues
    );
  });

  it('renders field value correctly', () => {
    renderWithContext(<Field data={mockData} allowEdit={true} />);
    expect(screen.getByTestId(`field-${mockData.id}-test-id`)).toBeInTheDocument();
    expect(screen.getByText('Test Value')).toBeInTheDocument();
  });

  it('enters edit mode when edit button is clicked', () => {
    renderWithContext(<Field data={mockData} allowEdit={true} />);

    const editButton = screen.getByRole('button');
    fireEvent.click(editButton);

    expect(mockProfileContextValues.onEditField).toHaveBeenCalledWith('test-field');
  });

  it('handles text input changes and saves correctly', () => {
    const editFields = { 'test-field': true };
    (useProfileContext as unknown as ReturnType<typeof vi.fn>).mockReturnValue({
      ...mockProfileContextValues,
      editFields,
    });

    renderWithContext(<Field data={mockData} allowEdit={true} />);

    const input = screen.getByRole('textbox');
    fireEvent.change(input, { target: { value: 'New Value' } });
    fireEvent.blur(input);

    expect(mockContextValues.onSaveData).toHaveBeenCalledWith('New Value', 0, 'test-field', false);
    expect(mockProfileContextValues.resetEditFields).toHaveBeenCalled();
  });

  it('handles checkbox field correctly', () => {
    const checkboxData: ProfileData = {
      ...mockData,
      type: FieldTypes.CHECKBOX,
      mapValue: false,
    };

    const editFields = { 'test-field': true };
    (useProfileContext as unknown as ReturnType<typeof vi.fn>).mockReturnValue({
      ...mockProfileContextValues,
      editFields,
    });

    renderWithContext(<Field data={checkboxData} allowEdit={true} />);

    const checkbox = screen.getByRole('checkbox');
    fireEvent.click(checkbox);

    expect(mockContextValues.onSaveData).toHaveBeenCalledWith(true, 0, 'test-field', false);
  });

  it('disables field when isDetailVisible is false', () => {
    const disabledData: ProfileData = {
      ...mockData,
      isDetailVisible: false,
    };

    renderWithContext(<Field data={disabledData} allowEdit={true} />);

    const fieldText = screen.getByText('Test Value');
    expect(fieldText).toHaveStyle({ opacity: 0.5 });
    expect(fieldText).toHaveStyle({ pointerEvents: 'none' });
  });

  it('shows menu for long text fields', async () => {
    const longTextData: ProfileData = {
      ...mockData,
      type: FieldTypes.LONG_TEXT,
    };

    const mockSetModalData = vi.fn();
    const mockOpenViewAllModal = vi.fn();

    renderWithContext(
      <Field
        data={longTextData}
        allowEdit={true}
        setModalData={mockSetModalData}
        openViewAllModal={mockOpenViewAllModal}
      />
    );

    const menuButton = screen.getByTestId(`field-${longTextData.id}-menu-test-id`);
    fireEvent.click(menuButton);
    await waitFor(() => {
      expect(screen.getByText('viewAll')).toBeInTheDocument();
      expect(screen.getByText('pinToTabbar')).toBeInTheDocument();
    });
  });

  it('handles pin to tabbar action', async () => {
    const longTextData: ProfileData = {
      ...mockData,
      type: FieldTypes.LONG_TEXT,
    };

    renderWithContext(<Field data={longTextData} allowEdit={true} />);

    const menuButton = screen.getByTestId(`field-${longTextData.id}-menu-test-id`);
    fireEvent.click(menuButton);
    await waitFor(() => {
      const pinButton = screen.getByText('pinToTabbar');
      fireEvent.click(pinButton);

      expect(mockContextValues.handleViewChange).toHaveBeenCalledWith('view1', {
        ...mockContextValues.activeView,
        displayLongText: ['test-field'],
      });
    });
  });

  it('respects view lock state', () => {
    (useWorkspaceContext as unknown as ReturnType<typeof vi.fn>).mockReturnValue({
      ...mockContextValues,
      activeView: {
        ...mockContextValues.activeView,
        locked: true,
      },
    });

    renderWithContext(<Field data={mockData} allowEdit={false} />);

    const editButton = screen.queryByTestId(`field-${mockData.id}-edit-test-id`);
    expect(editButton).not.toBeInTheDocument();
  });

  it('handles new record creation correctly', async () => {
    (useWorkspaceContext as unknown as ReturnType<typeof vi.fn>).mockReturnValue({
      ...mockContextValues,
      currRecordIndex: -1,
    });

    const editFields = { 'test-field': true };
    (useProfileContext as unknown as ReturnType<typeof vi.fn>).mockReturnValue({
      ...mockProfileContextValues,
      editFields,
    });

    renderWithContext(<Field data={mockData} allowEdit={true} />);

    const input = screen.getByRole('textbox');
    fireEvent.change(input, { target: { value: 'New Value' } });
    fireEvent.blur(input);

    await waitFor(() => {
      expect(mockProfileContextValues.handleSaveRecord).toHaveBeenCalledWith(
        'New Value',
        'test-field'
      );
      expect(mockProfileContextValues.mutateProfile).toHaveBeenCalled();
    });
  });

  describe('renderTypeEdit - Field Type Tests', () => {
    beforeEach(() => {
      vi.clearAllMocks();
      (useWorkspaceContext as unknown as ReturnType<typeof vi.fn>).mockReturnValue(
        mockContextValues
      );
      (useProfileContext as unknown as ReturnType<typeof vi.fn>).mockReturnValue({
        ...mockProfileContextValues,
        editFields: { 'test-field': true },
      });
    });

    it('renders DateTimeCell for DATETIME type', () => {
      const dateTimeData: ProfileData = {
        ...mockData,
        type: FieldTypes.DATETIME,
        mapValue: '2024-03-20T10:00:00Z',
      };

      renderWithContext(<Field data={dateTimeData} allowEdit={true} />);
      expect(screen.getByTestId(`field-${dateTimeData.id}-test-id`)).toBeInTheDocument();
    });

    it('renders MultiSelectCell for MULTI_SELECT type', () => {
      const multiSelectData: ProfileData = {
        ...mockData,
        type: FieldTypes.MULTI_SELECT,
        mapValue: ['option1'],
        options: {
          options: ['option1', 'option2', 'option3'],
        },
      };

      renderWithContext(<Field data={multiSelectData} allowEdit={true} />);
      expect(screen.getByTestId(`field-${multiSelectData.id}-test-id`)).toBeInTheDocument();
    });

    it('renders ImageCell for IMAGE type', () => {
      const imageData: ProfileData = {
        ...mockData,
        type: FieldTypes.IMAGE,
        mapValue: 'https://example.com/image.jpg',
      };

      renderWithContext(<Field data={imageData} allowEdit={true} />);
      expect(screen.getByTestId(`field-${imageData.id}-test-id`)).toBeInTheDocument();
    });

    it('renders LinkToRecordCell for RELATIONSHIP type', () => {
      const relationshipData: ProfileData = {
        ...mockData,
        type: FieldTypes.RELATIONSHIP,
        mapValue: 'related-record-id',
        options: {
          targetObjectId: 'target-object',
        },
      };

      renderWithContext(<Field data={relationshipData} allowEdit={true} />);
      expect(screen.getByTestId(`field-${relationshipData.id}-test-id`)).toBeInTheDocument();
    });

    it('handles Enter key press in default text input', () => {
      const textData: ProfileData = {
        ...mockData,
        type: FieldTypes.SINGLE_LINE_TEXT,
        mapValue: 'Initial text',
      };

      renderWithContext(<Field data={textData} allowEdit={true} />);
      const input = screen.getByRole('textbox');

      fireEvent.change(input, { target: { value: 'New text' } });
      fireEvent.keyDown(input, { key: 'Enter' });

      expect(mockContextValues.onSaveData).toHaveBeenCalledWith('New text', 0, 'test-field', false);
    });
  });
});
