import { Box, rem } from '@mantine/core';
import { useTranslate } from '@tolgee/react';
import React from 'react';
import { useFormContext } from 'react-hook-form';
import { TextInput } from 'react-hook-form-mantine';
import ColorConfig from './ColorConfig';

const OpenLinkConfig = () => {
  const { t } = useTranslate('workspace');

  const { control } = useFormContext();
  return (
    <>
      <Box ml={rem(30)} my={rem(10)} w={'100%'}>
        <TextInput
          control={control}
          label={t('displayLabel')}
          withAsterisk
          labelProps={{ mb: rem(10) }}
          name='openLink.label'
          placeholder={t('inputLabel')}
        />
        <TextInput
          control={control}
          my={rem(20)}
          label={'URL'}
          withAsterisk
          labelProps={{ mb: rem(10) }}
          name='openLink.url'
          placeholder={t('inputUrl')}
        />
        <ColorConfig name='openLink.color' />
      </Box>
    </>
  );
};

export default React.memo(OpenLinkConfig);
