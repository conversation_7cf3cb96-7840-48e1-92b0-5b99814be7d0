import { CUSTOM_ACTION_COLORS } from '@/constants/workspace';
import { rem } from '@mantine/core';
import { createStyles } from '@mantine/emotion';

export const useCustomActionStyles = createStyles((theme) => {
  return {
    desc: {
      width: '100%',
      fontSize: rem(14),
      alignItems: 'center',
      gap: rem(16),
      marginLeft: rem(30),
      borderRadius: rem(10),
      padding: rem(10),
      backgroundColor: theme.colors.decaLight[1],

      svg: {
        color: theme.colors.decaBlue[5],
      },
    },
    color: {
      width: rem(24),
      height: rem(24),
      borderRadius: '50%',
      cursor: 'pointer',
    },
    ...Object.fromEntries(
      CUSTOM_ACTION_COLORS.map((color) => [
        color,
        {
          backgroundColor: theme.colors[`deca${color.charAt(0).toUpperCase() + color.slice(1)}`][0],
        },
      ])
    ),
    ...Object.fromEntries(
      CUSTOM_ACTION_COLORS.map((color) => [
        `selected_${color}`,
        {
          border: `2px solid ${theme.colors[`deca${color.charAt(0).toUpperCase() + color.slice(1)}`][5]}`,
        },
      ])
    ),
    headerParameters: {
      paddingBottom: rem(20),
      marginBottom: rem(10),
      ':not(:last-child)': {
        borderBottom: `1px dashed ${theme.colors.decaGrey[2]}`,
      },
    },
    icon: {
      borderRadius: '50%',
      cursor: 'pointer',
      width: rem(36),
      height: rem(36),
      display: 'flex',
      alignItems: 'center',
      justifyContent: 'center',
      '&:hover': {
        backgroundColor: theme.colors.decaBlue[1],
      },
      svg: {
        color: theme.colors.decaGrey[6],
      },
    },
    selectedIcon: {
      backgroundColor: theme.colors.decaBlue[1],
      svg: {
        color: theme.colors.decaBlue[5],
      },
    },
  };
});
