import { TextInput } from '@mantine/core';

import { ActionIcon } from '@mantine/core';

import { Box, Flex, Text, rem } from '@mantine/core';
import { DecaButton } from '@resola-ai/ui';
import { IconChevronDown, IconTrash } from '@tabler/icons-react';
import { useTranslate } from '@tolgee/react';
import { useFieldArray, useFormContext } from 'react-hook-form';
import { Select, Textarea } from 'react-hook-form-mantine';
import { useCustomActionStyles } from './useCustomActionStyles';

const SpecificConfig = (fields: any) => {
  const { t } = useTranslate('workspace');
  const { watch, control } = useFormContext();
  const { append, remove } = useFieldArray({
    control,
    name: 'headerParameters',
  });

  const { classes } = useCustomActionStyles();

  return (
    <>
      <Select
        withAsterisk
        name='headers'
        mt={rem(10)}
        ml={rem(20)}
        mb={rem(20)}
        labelProps={{ mb: rem(10), fontSize: rem(16) }}
        label={t('specifyHeaders')}
        placeholder={t('selectHeader')}
        data={[
          { label: t('usingJson'), value: 'json' },
          { label: t('usingFieldsBelow'), value: 'manually' },
        ]}
        rightSection={<IconChevronDown size={16} />}
      />
      {watch('headers') === 'json' && (
        <Textarea
          rows={4}
          ml={rem(20)}
          mt={rem(20)}
          label={t('JSON')}
          name='json'
          labelProps={{ mb: rem(10) }}
        />
      )}
      {watch('headers') === 'manually' && (
        <Box pl={rem(20)}>
          <Flex justify='space-between' mb={rem(10)}>
            <Text fz={16} fw={500}>
              {t('headerParameters')}
            </Text>
            <DecaButton
              variant='secondary_text'
              size='sm'
              onClick={() => append({ name: '', value: '' })}
            >
              {t('addParameter')}
            </DecaButton>
          </Flex>
          <Box>
            {fields.map((field, index) => (
              <Flex
                key={field.id}
                gap={rem(10)}
                align='center'
                className={classes.headerParameters}
              >
                <TextInput
                  labelProps={{ mb: rem(10) }}
                  w={rem(180)}
                  label={t('headerName')}
                  name={`headerParameters.${index}.name`}
                  placeholder={t('inputHeaderName')}
                />
                <TextInput
                  labelProps={{ mb: rem(10) }}
                  w={`calc(100% - ${rem(220)})`}
                  label={t('headerValue')}
                  name={`headerParameters.${index}.value`}
                  placeholder={t('inputHeaderValue')}
                />
                <ActionIcon
                  onClick={() => remove(index)}
                  mt={rem(30)}
                  variant='transparent'
                  color='decaRed.5'
                >
                  <IconTrash size={18} />
                </ActionIcon>
              </Flex>
            ))}
          </Box>
        </Box>
      )}
    </>
  );
};

export default SpecificConfig;
