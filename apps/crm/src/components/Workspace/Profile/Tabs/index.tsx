import { PREFERENCES } from '@/constants/workspace';
import { useProfileContext } from '@/contexts/ProfileContext';
import { useWorkspaceContext } from '@/contexts/WorkspaceContext';
import {
  ActionIcon,
  Box,
  Flex,
  LoadingOverlay,
  ScrollArea,
  rem,
  useMantineTheme,
} from '@mantine/core';
import {
  IconChevronLeft,
  IconChevronRight,
  IconDirections,
  IconFileAnalytics,
  IconFloatLeft,
  IconFolder,
  IconHistory,
  IconKeyframes,
} from '@tabler/icons-react';
import React, { Suspense, useRef, useState, useEffect, useCallback, useMemo, memo } from 'react';
import { type ITab, TabItem } from './TabItem';

// Lazy load tab components
const Activities = React.lazy(() => import('../Activities'));
const Identities = React.lazy(() => import('../Identities'));
const Files = React.lazy(() => import('../Files'));
const History = React.lazy(() => import('../History'));
const LongText = React.lazy(() => import('../LongText'));
const Forms = React.lazy(() => import('../Forms'));

// Component references defined outside the component to avoid recreating on each render
const tabIcons = {
  [PREFERENCES.activities]: <IconDirections />,
  [PREFERENCES.identities]: <IconKeyframes />,
  [PREFERENCES.files]: <IconFileAnalytics />,
  [PREFERENCES.history]: <IconHistory />,
  [PREFERENCES.longText]: <IconFloatLeft />,
  [PREFERENCES.customObject]: <IconFolder />,
};

// Loading component for Suspense fallback
const TabLoading = () => (
  <Box pos='relative' h='100%'>
    <LoadingOverlay visible={true} loaderProps={{ size: 'sm' }} />
  </Box>
);

const ProfileTabs: React.FC<{ isFullview: boolean }> = ({ isFullview }) => {
  const { colors } = useMantineTheme();
  const { getForm } = useProfileContext();
  const { object, activeView } = useWorkspaceContext();
  const scrollRef = useRef<HTMLDivElement>(null);
  const [canScrollLeft, setCanScrollLeft] = useState(false);
  const [canScrollRight, setCanScrollRight] = useState(false);
  const [activeTab, setActiveTab] = useState<string | null>(null);

  // Use useMemo to create tab configurations
  const tabs = useMemo(() => {
    const newTabs: ITab[] = [];

    if (!object?.profileSettings) return newTabs;
    // Iterate through profileSettings array to maintain order
    object.profileSettings.forEach((setting) => {
      if (setting.enabled) {
        if (activeView?.displayLongText?.length && setting.type === 'pinLongText') {
          newTabs.push({
            value: PREFERENCES.longText,
            component: PREFERENCES.longText,
            icon: tabIcons[PREFERENCES.longText],
          });
        }
        if (setting.type === 'pinCustomObject') {
          // Add tabs for each pinned custom object
          object.childObjects?.forEach((childObj) => {
            if (childObj.pinned) {
              const form = getForm(childObj.id);
              if (form?.linkedObj) {
                newTabs.push({
                  value: `customObject-${childObj.id}`,
                  component: form?.linkedObj?.name?.singular,
                  icon: tabIcons[PREFERENCES.customObject],
                });
              }
            }
          });
        }
        if (setting.type === 'tasks') {
          newTabs.push({
            value: PREFERENCES.history,
            component: PREFERENCES.history,
            icon: tabIcons[PREFERENCES.history],
          });
        }
        if (setting.type === 'attachments') {
          newTabs.push({
            value: PREFERENCES.files,
            component: PREFERENCES.files,
            icon: tabIcons[PREFERENCES.files],
          });
        }
        if (setting.type === 'activities') {
          newTabs.push({
            value: PREFERENCES.activities,
            component: PREFERENCES.activities,
            icon: tabIcons[PREFERENCES.activities],
          });
        }
        if (setting.type === 'identities') {
          newTabs.push({
            value: PREFERENCES.identities,
            component: PREFERENCES.identities,
            icon: tabIcons[PREFERENCES.identities],
          });
        }
      }
    });
    return newTabs;
  }, [object?.profileSettings, object?.childObjects, activeView?.displayLongText, getForm]);

  // Set active tab only when needed: when tabs change
  useEffect(() => {
    if (tabs.length > 0 && (!activeTab || !tabs.some((tab) => tab.value === activeTab))) {
      setActiveTab(tabs[0]?.value);
    }
  }, [tabs, activeTab]);

  const isMinimized = useMemo(() => tabs.length > 8, [tabs.length]);

  // Render the correct component based on tab value
  const renderTabComponent = useCallback(
    (tabValue: string) => {
      switch (tabValue) {
        case PREFERENCES.activities:
          return <Activities />;
        case PREFERENCES.identities:
          return <Identities />;
        case PREFERENCES.files:
          return <Files />;
        case PREFERENCES.history:
          return <History />;
        case PREFERENCES.longText:
          return <LongText />;
        default:
          // Handle custom object tabs
          if (tabValue.startsWith('customObject-')) {
            const objectId = tabValue.replace('customObject-', '');
            const form = getForm(objectId);
            return <Forms form={form} />;
          }
          return null;
      }
    },
    [getForm]
  );

  const checkScrollBoundaries = useCallback(() => {
    if (scrollRef.current) {
      const { scrollLeft, scrollWidth, clientWidth } = scrollRef.current;
      setCanScrollLeft(scrollLeft > 0);
      setCanScrollRight(scrollLeft < scrollWidth - clientWidth - 1);
    }
  }, [scrollRef.current]);

  useEffect(() => {
    const scrollElement = scrollRef.current;
    if (scrollElement) {
      checkScrollBoundaries();
      scrollElement.addEventListener('scroll', checkScrollBoundaries);

      // Check on resize as well
      const resizeObserver = new ResizeObserver(checkScrollBoundaries);
      resizeObserver.observe(scrollElement);

      return () => {
        scrollElement.removeEventListener('scroll', checkScrollBoundaries);
        resizeObserver.disconnect();
      };
    }
  }, [checkScrollBoundaries]);

  const scrollLeft = () => {
    if (scrollRef.current) {
      scrollRef.current.scrollBy({ left: -150, behavior: 'smooth' });
    }
  };

  const scrollRight = () => {
    if (scrollRef.current) {
      scrollRef.current.scrollBy({ left: 150, behavior: 'smooth' });
    }
  };

  const showNavigation = tabs.length > (isMinimized ? 8 : 4);

  // Don't render if there are no tabs
  if (tabs.length === 0) {
    return null;
  }

  return (
    <Flex direction='column' w='50%' h='100%' bg={colors.decaLight[0]}>
      <Box
        pos='relative'
        px={rem(16)}
        py={rem(8)}
        h={rem(48)}
        sx={{
          borderBottom: `${rem(2)} solid ${colors.decaLight[1]}`,
        }}
      >
        <Box
          ref={scrollRef}
          style={{
            flex: 1,
            overflowX: 'auto',
            overflowY: 'hidden',
            scrollbarWidth: 'none',
            msOverflowStyle: 'none',
            '&::-webkit-scrollbar': {
              display: 'none',
            },
          }}
        >
          <Flex gap={rem(4)} align='center' maw='fit-content' h={rem(40)} role='tablist'>
            {tabs.map((tab) => (
              <TabItem
                key={tab.value}
                tab={tab}
                isMinimized={isMinimized}
                isActive={activeTab === tab.value}
                onClick={() => setActiveTab(tab.value)}
              />
            ))}
          </Flex>
        </Box>

        {/* Left Navigation Button */}
        {showNavigation && canScrollLeft && (
          <Flex
            pos='absolute'
            left={0}
            top='50%'
            style={{
              transform: 'translateY(-50%)',
              zIndex: 1,
            }}
          >
            <ActionIcon
              variant='gradient'
              gradient={{ from: colors.decaLight[4], to: colors.decaLight[0], deg: 90 }}
              size='48px'
              onClick={scrollLeft}
              c='white'
              radius={0}
            >
              <IconChevronLeft color={colors.decaGrey[4]} size={20} />
            </ActionIcon>
          </Flex>
        )}

        {/* Right Navigation Button */}
        {showNavigation && canScrollRight && (
          <Flex
            pos='absolute'
            right={0}
            top='50%'
            style={{
              transform: 'translateY(-50%)',
              zIndex: 1,
            }}
          >
            <ActionIcon
              variant='gradient'
              gradient={{ from: colors.decaLight[0], to: colors.decaLight[4], deg: 90 }}
              size='48px'
              onClick={scrollRight}
              c='white'
              radius={0}
            >
              <IconChevronRight color={colors.decaGrey[4]} size={20} />
            </ActionIcon>
          </Flex>
        )}
      </Box>
      <ScrollArea
        type='hover'
        flex={1}
        h={`calc(100vh - ${isFullview ? rem(60) : rem(120)})`}
        p={rem(16)}
      >
        <Suspense fallback={<TabLoading />}>{activeTab && renderTabComponent(activeTab)}</Suspense>
      </ScrollArea>
    </Flex>
  );
};

export default memo(ProfileTabs);
