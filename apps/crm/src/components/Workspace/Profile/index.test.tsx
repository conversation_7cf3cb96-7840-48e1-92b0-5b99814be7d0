import { ProfileContextProvider } from '@/contexts/ProfileContext';
import { renderWithMantine } from '@/tests/utils/testUtils';
import { fireEvent, screen, waitFor } from '@testing-library/react';
import { BrowserRouter } from 'react-router-dom';
import { beforeEach, describe, expect, it, vi } from 'vitest';
import { Profile } from './index';

// Mock react-router-dom first
vi.mock('react-router-dom', () => ({
  useParams: () => ({
    wsId: 'workspace-123',
    id: 'object-456',
    recordId: 'record-789',
  }),
  useNavigate: () => vi.fn(),
  BrowserRouter: ({ children }: any) => <div>{children}</div>,
  Link: ({ children, to, className, target }: any) => (
    <a href={to} className={className} target={target}>
      {children}
    </a>
  ),
}));

// Mock the workspace context
vi.mock('@/contexts/WorkspaceContext', () => ({
  useWorkspaceContext: vi.fn(() => ({
    opened: true,
    data: [],
    currRecordIndex: 0,
    object: {
      name: {
        singular: 'Test Object',
        plural: 'Test Objects',
      },
      profileSettings: [
        { type: 'activities', enabled: true },
        { type: 'identities', enabled: true },
        { type: 'attachments', enabled: true },
        { type: 'tasks', enabled: true },
      ],
      hasTags: true,
    },
    activeView: {
      displayLongText: [],
      permission: { canRead: true, canList: true, canView: true },
    },
    openProfile: vi.fn(),
    viewLoading: false,
  })),
}));

// Mock BreadcrumbContext
vi.mock('@/contexts/BreadcrumbContext', () => ({
  BreadcrumbProvider: ({ children }: any) => children,
  useBreadcrumbContext: () => ({
    breadcrumbs: [],
    addBreadcrumb: vi.fn(),
    navigateToBreadcrumb: vi.fn(),
    clearBreadcrumbs: vi.fn(),
  }),
}));

// Mock useBreadcrumbNavigation
vi.mock('@/hooks/useBreadcrumbNavigation', () => ({
  useBreadcrumbNavigation: () => ({
    navigateToLinkedRecord: vi.fn(),
  }),
}));

// Mock @resola-ai/ui/hooks
vi.mock('@resola-ai/ui/hooks', () => ({
  usePathParams: () => ({
    createPathWithLngParam: (path: string) => path,
  }),
}));

// Mock Tolgee
vi.mock('@tolgee/react', () => ({
  useTranslate: () => ({
    t: (key: string) => key,
  }),
}));

vi.mock('@/tolgee', () => ({
  tolgee: {
    changeLanguage: vi.fn(),
  },
}));

// Mock ProfileContext
vi.mock('@/contexts/ProfileContext', async () => {
  const actual = await vi.importActual('@/contexts/ProfileContext');
  return {
    ...actual,
    ProfileContextProvider: ({ children }) => children,
    useProfileContext: vi.fn(() => ({
      profile: {},
      mutateProfile: vi.fn(),
      tags: [],
      handleAddTag: vi.fn(),
      handleRemoveTag: vi.fn(),
      dragging: false,
      onSetDragging: vi.fn(),
      sortItems: [
        { id: 'objectFields', height: 'auto' },
        { id: 'tags', height: 'auto' },
      ],
      handleDragEnd: vi.fn(),
      handleChangeSize: vi.fn(),
      forms: [],
      getForm: vi.fn(),
      editFields: {},
      onEditField: vi.fn(),
      resetEditFields: vi.fn(),
      customObjectFields: [],
      setCustomObjectFields: vi.fn(),
      closeProfile: vi.fn(),
      handleSaveRecord: vi.fn(),
    })),
  };
});

// Mock @resola-ai/ui
vi.mock('@resola-ai/ui', () => ({
  If: ({ children, condition }: { children: React.ReactNode; condition: boolean }) =>
    condition ? <div>{children}</div> : null,
  Modal: ({ children, opened, onClose, ...props }: any) =>
    opened ? (
      <div data-testid='modal' {...props}>
        {children}
      </div>
    ) : null,
  DecaSwitch: ({ children, ...props }: any) => (
    <div data-testid='deca-switch' {...props}>
      {children}
    </div>
  ),
  DecaTag: ({ children, ...props }: any) => (
    <div data-testid='deca-tag' {...props}>
      {children}
    </div>
  ),
}));

// Mock permission utils
vi.mock('@resola-ai/ui/components/DecaTable/utils', () => ({
  isPermissionAllowed: () => true,
  PERMISSION_KEYS: {
    VIEW_READ: 'view:read',
    OBJECT_READ: 'object:read',
    VIEW_LIST: 'view:list',
  },
}));

// Mock components that are lazy loaded
vi.mock('./Activities', () => ({
  default: () => <div data-testid='activities'>Activities Component</div>,
}));

vi.mock('./Identities', () => ({
  default: () => <div data-testid='identities'>Identities Component</div>,
}));

vi.mock('./Files', () => ({
  default: () => <div data-testid='files'>Files Component</div>,
}));

vi.mock('./History', () => ({
  default: () => <div data-testid='history'>History Component</div>,
}));

// Mock Breadcrumbs component
vi.mock('./Breadcrumbs', () => ({
  default: () => <div data-testid='breadcrumbs'>Breadcrumbs</div>,
}));

// Mock NoViewAccess
vi.mock('../NoViewAccess', () => ({
  default: () => <div data-testid='no-view-access'>No View Access</div>,
}));

const renderWithProviders = (component: React.ReactNode) => {
  return renderWithMantine(
    <BrowserRouter>
      <ProfileContextProvider>{component}</ProfileContextProvider>
    </BrowserRouter>
  );
};

describe('Profile Component', () => {
  beforeEach(() => {
    vi.clearAllMocks();
  });

  it('renders profile component with drawer', () => {
    renderWithProviders(<Profile />);
    expect(screen.getByTestId('profile-drawer')).toBeInTheDocument();
    expect(screen.getByTestId('profile-header')).toBeInTheDocument();
    expect(screen.getByTestId('fullview-toggle')).toBeInTheDocument();
    expect(screen.getByTestId('tab-activities')).toBeInTheDocument();
    expect(screen.getByTestId('tab-identities')).toBeInTheDocument();
    expect(screen.getByTestId('tab-files')).toBeInTheDocument();
    expect(screen.getByTestId('tab-history')).toBeInTheDocument();
    expect(screen.getByTestId('tags-test-id')).toBeInTheDocument();
  });

  it('renders profile contact section', () => {
    renderWithProviders(<Profile />);
    expect(screen.getByTestId('profile-contact-test-id')).toBeInTheDocument();
  });

  it('renders object fields section', () => {
    renderWithProviders(<Profile />);
    expect(screen.getByTestId('object-fields-test-id')).toBeInTheDocument();
  });

  it('renders tabs with correct icons', () => {
    renderWithProviders(<Profile />);
    const tabList = screen.getByRole('tablist');
    expect(tabList).toBeInTheDocument();
    expect(screen.getByTestId('tab-activities')).toBeInTheDocument();
    expect(screen.getByTestId('tab-identities')).toBeInTheDocument();
    expect(screen.getByTestId('tab-files')).toBeInTheDocument();
    expect(screen.getByTestId('tab-history')).toBeInTheDocument();
  });

  it('switches between tabs correctly', async () => {
    renderWithProviders(<Profile />);

    // Click activities tab
    fireEvent.click(screen.getByTestId('tab-activities'));
    await waitFor(() => {
      expect(screen.getByTestId('activities')).toBeInTheDocument();
    });

    // Click identities tab
    fireEvent.click(screen.getByTestId('tab-identities'));
    await waitFor(() => {
      expect(screen.getByTestId('identities')).toBeInTheDocument();
    });
  });

  it('toggles fullview mode', () => {
    renderWithProviders(<Profile />);
    const fullviewButton = screen.getByTestId('fullview-toggle');

    fireEvent.click(fullviewButton);
    expect(screen.getByTestId('profile-drawer')).toHaveStyle({ '--drawer-size': '100vw' });

    fireEvent.click(fullviewButton);
    expect(screen.getByTestId('profile-drawer')).toHaveStyle({ '--drawer-size': '70vw' });
  });
});
