import { describe, expect, it } from 'vitest';
import * as FieldSettings from './index';

describe('FieldSettings index exports', () => {
  it('should export all the settings components', () => {
    expect(FieldSettings).toHaveProperty('SingleSelectSettings');
    expect(FieldSettings).toHaveProperty('TimeSettings');
    expect(FieldSettings).toHaveProperty('NumberSettings');
    expect(FieldSettings).toHaveProperty('CurrencySettings');
    expect(FieldSettings).toHaveProperty('PercentSettings');
    expect(FieldSettings).toHaveProperty('TextSettings');
    expect(FieldSettings).toHaveProperty('ImageSettings');
    expect(FieldSettings).toHaveProperty('PhoneSettings');
    expect(FieldSettings).toHaveProperty('EmailSettings');
    expect(FieldSettings).toHaveProperty('LineSettings');
  });
});
