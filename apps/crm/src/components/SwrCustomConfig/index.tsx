import type React from 'react';
import { SWRConfig } from 'swr';

interface SwrCustomConfigProps {
  children: React.ReactNode;
}

const SwrCustomConfig: React.FC<SwrCustomConfigProps> = ({ children }) => {
  return (
    <SWRConfig
      value={{
        revalidateOnFocus: false,
        // revalidateIfStale: false,
      }}
    >
      {children}
    </SWRConfig>
  );
};

export default SwrCustomConfig;
