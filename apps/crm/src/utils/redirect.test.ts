import { afterEach, beforeEach, describe, expect, it, vi } from 'vitest';
import * as domainsUtils from './domains';
import { getRedirectUri } from './redirect';

// Mock the domains utility functions
vi.mock('./domains', () => ({
  isExcludedBasePathDomains: vi.fn(),
  isIncludedBasePathDomains: vi.fn(),
}));

// Mock the AppConfig
vi.mock('@/configs', () => ({
  default: {
    BASE_PATH: '/crm',
  },
}));

describe('redirect utils', () => {
  // Store the original location object
  const originalLocation = window.location;

  // Get the mocked functions using vi.mocked
  const mockedDomainsUtils = vi.mocked(domainsUtils);

  beforeEach(() => {
    // Reset mock function calls
    vi.resetAllMocks();

    // Mock window.location using defineProperty
    Object.defineProperty(window, 'location', {
      configurable: true,
      value: {
        origin: 'https://test-domain.com',
      },
      writable: true,
    });
  });

  afterEach(() => {
    // Restore window.location
    Object.defineProperty(window, 'location', {
      configurable: true,
      value: originalLocation,
      writable: true,
    });
  });

  it('should return empty string when window is undefined', () => {
    const originalWindow = global.window;
    // @ts-expect-error - Testing when window is undefined
    global.window = undefined;
    expect(getRedirectUri()).toBe('');
    global.window = originalWindow;
  });

  it('should return origin when isExcludedBasePathDomains returns true', () => {
    mockedDomainsUtils.isExcludedBasePathDomains.mockReturnValue(true);
    mockedDomainsUtils.isIncludedBasePathDomains.mockReturnValue(false);
    expect(getRedirectUri()).toBe('https://test-domain.com');
    expect(mockedDomainsUtils.isExcludedBasePathDomains).toHaveBeenCalled();
  });

  it('should return origin + BASE_PATH when isIncludedBasePathDomains returns true', () => {
    mockedDomainsUtils.isExcludedBasePathDomains.mockReturnValue(false);
    mockedDomainsUtils.isIncludedBasePathDomains.mockReturnValue(true);
    expect(getRedirectUri()).toBe('https://test-domain.com/crm');
    expect(mockedDomainsUtils.isExcludedBasePathDomains).toHaveBeenCalled();
    expect(mockedDomainsUtils.isIncludedBasePathDomains).toHaveBeenCalled();
  });

  it('should return origin when both domain checks return false', () => {
    mockedDomainsUtils.isExcludedBasePathDomains.mockReturnValue(false);
    mockedDomainsUtils.isIncludedBasePathDomains.mockReturnValue(false);
    expect(getRedirectUri()).toBe('https://test-domain.com');
    expect(mockedDomainsUtils.isExcludedBasePathDomains).toHaveBeenCalled();
    expect(mockedDomainsUtils.isIncludedBasePathDomains).toHaveBeenCalled();
  });
});
