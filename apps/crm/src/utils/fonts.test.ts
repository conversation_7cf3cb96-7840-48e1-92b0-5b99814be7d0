import { describe, expect, it } from 'vitest';
import { defaultFontName, openSansFontName } from './fonts';

describe('fonts utils', () => {
  describe('openSansFontName', () => {
    it('should be "Open Sans"', () => {
      expect(openSansFontName).toBe('Open Sans');
    });
  });

  describe('defaultFontName', () => {
    it('should include Noto Sans JP', () => {
      expect(defaultFontName).toContain('Noto Sans JP');
    });

    it('should include fallback fonts', () => {
      expect(defaultFontName).toContain('Arial');
    });

    it('should have the correct format', () => {
      expect(defaultFontName).toBe('Noto Sans JP, Arial');
    });
  });
});
