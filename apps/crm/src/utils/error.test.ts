import { describe, expect, it } from 'vitest';
import { isAbortError } from './error';

describe('error utils', () => {
  describe('isAbortError', () => {
    it('should return true for AbortError', () => {
      const error = new Error('Operation was aborted');
      error.name = 'AbortError';
      expect(isAbortError(error)).toBe(true);
    });

    it('should return true for CanceledError', () => {
      const error = new Error('Operation was canceled');
      error.name = 'CanceledError';
      expect(isAbortError(error)).toBe(true);
    });

    it('should return false for other error types', () => {
      const error = new Error('Some other error');
      error.name = 'Error';
      expect(isAbortError(error)).toBe(false);
    });

    it('should return false for TypeError', () => {
      const error = new TypeError('Type error occurred');
      expect(isAbortError(error)).toBe(false);
    });

    it('should return false for SyntaxError', () => {
      const error = new SyntaxError('Syntax error occurred');
      expect(isAbortError(error)).toBe(false);
    });
  });
});
