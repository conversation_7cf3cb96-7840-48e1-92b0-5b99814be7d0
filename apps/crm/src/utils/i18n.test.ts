import { describe, expect, it } from 'vitest';
import { getLanguageFromQueryString } from './i18n';

describe('i18n utils', () => {
  describe('getLanguageFromQueryString', () => {
    it('should return the language from query string when present', () => {
      const queryString = '?lng=en';
      expect(getLanguageFromQueryString(queryString)).toBe('en');
    });

    it('should return the language from query string with multiple parameters', () => {
      const queryString = '?page=1&lng=ja&sort=asc';
      expect(getLanguageFromQueryString(queryString)).toBe('ja');
    });

    it('should return null when language is not present in query string', () => {
      const queryString = '?page=1&sort=asc';
      expect(getLanguageFromQueryString(queryString)).toBeNull();
    });

    it('should return null for empty query string', () => {
      const queryString = '';
      expect(getLanguageFromQueryString(queryString)).toBeNull();
    });

    it('should handle query string without question mark', () => {
      const queryString = 'lng=fr';
      expect(getLanguageFromQueryString(queryString)).toBe('fr');
    });
  });
});
