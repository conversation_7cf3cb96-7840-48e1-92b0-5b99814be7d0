import { FieldTypes } from '@resola-ai/ui/components';
import { describe, expect, it, vi } from 'vitest';
import type { z } from 'zod';
import { createObjectFieldSchema, customActionSchemas } from './schemas';

describe('schemas', () => {
  describe('createObjectFieldSchema', () => {
    // Mock translate function
    const t = vi.fn((key) => `translated_${key}`);
    let objectFieldSchema: z.ZodSchema;

    beforeEach(() => {
      objectFieldSchema = createObjectFieldSchema(t);
    });

    it('should validate valid field schema', () => {
      const validData = {
        name: 'Test Field',
        type: 'text',
        options: {},
      };

      const result = objectFieldSchema.safeParse(validData);
      expect(result.success).toBe(true);
    });

    it('should validate fields with relationship type', () => {
      const validRelationship = {
        name: 'Test Relation',
        type: FieldTypes.RELATIONSHIP,
        options: {
          objectId: 'obj123',
          fieldId: 'field456',
        },
      };

      const result = objectFieldSchema.safeParse(validRelationship);
      expect(result.success).toBe(true);
    });

    it('should fail validation for empty name', () => {
      const invalidData = {
        name: '',
        type: 'text',
        options: {},
      };

      const result = objectFieldSchema.safeParse(invalidData);
      expect(result.success).toBe(false);

      if (!result.success) {
        expect(result.error.issues[0].message).toBe('translated_requiredField');
      }
    });

    it('should fail validation for relationship type missing objectId', () => {
      const invalidRelationship = {
        name: 'Test Relation',
        type: FieldTypes.RELATIONSHIP,
        options: {
          fieldId: 'field456',
        },
      };

      const result = objectFieldSchema.safeParse(invalidRelationship);
      expect(result.success).toBe(false);

      if (!result.success) {
        const objectIdError = result.error.issues.find((issue) => issue.path.includes('objectId'));
        expect(objectIdError).toBeDefined();
        expect(objectIdError?.message).toBe('translated_requiredField');
      }
    });

    it('should fail validation for relationship type missing fieldId', () => {
      const invalidRelationship = {
        name: 'Test Relation',
        type: FieldTypes.RELATIONSHIP,
        options: {
          objectId: 'obj123',
        },
      };

      const result = objectFieldSchema.safeParse(invalidRelationship);
      expect(result.success).toBe(false);

      if (!result.success) {
        const fieldIdError = result.error.issues.find((issue) => issue.path.includes('fieldId'));
        expect(fieldIdError).toBeDefined();
        expect(fieldIdError?.message).toBe('translated_requiredField');
      }
    });
  });

  describe('customActionSchemas', () => {
    // Mock translate function
    const t = vi.fn((key) => `translated_${key}`);
    let actionSchema: z.ZodSchema;

    beforeEach(() => {
      actionSchema = customActionSchemas(t);
    });

    it('should validate openLink action with valid data', () => {
      const validOpenLink = {
        type: 'openLink',
        openLink: {
          label: 'Click Here',
          url: 'https://example.com',
          color: '#FF0000',
        },
      };

      const result = actionSchema.safeParse(validOpenLink);
      expect(result.success).toBe(true);
    });

    it('should validate webhook action with valid data', () => {
      const validWebhook = {
        type: 'webhook',
        webhook: {
          method: 'POST',
          url: 'https://api.example.com',
          sendHeaders: {
            enabled: true,
            type: 'manually',
            parameters: [{ name: 'Content-Type', value: 'application/json' }],
          },
          sendBody: {
            enabled: true,
            type: 'json',
            json: '{"key": "value"}',
          },
        },
      };

      const result = actionSchema.safeParse(validWebhook);
      expect(result.success).toBe(true);
    });

    it('should fail validation for openLink action with missing label', () => {
      const invalidOpenLink = {
        type: 'openLink',
        openLink: {
          url: 'https://example.com',
        },
      };

      const result = actionSchema.safeParse(invalidOpenLink);
      expect(result.success).toBe(false);

      if (!result.success) {
        const labelError = result.error.issues.find((issue) => issue.path.includes('label'));
        expect(labelError).toBeDefined();
        expect(labelError?.message).toBe('translated_requiredField');
      }
    });

    it('should fail validation for openLink action with missing url', () => {
      const invalidOpenLink = {
        type: 'openLink',
        openLink: {
          label: 'Click Here',
        },
      };

      const result = actionSchema.safeParse(invalidOpenLink);
      expect(result.success).toBe(false);

      if (!result.success) {
        const urlError = result.error.issues.find((issue) => issue.path.includes('url'));
        expect(urlError).toBeDefined();
        expect(urlError?.message).toBe('translated_requiredField');
      }
    });

    it('should fail validation for webhook action with missing url', () => {
      const invalidWebhook = {
        type: 'webhook',
        webhook: {
          method: 'POST',
        },
      };

      const result = actionSchema.safeParse(invalidWebhook);
      expect(result.success).toBe(false);

      if (!result.success) {
        const urlError = result.error.issues.find((issue) => issue.path.includes('url'));
        expect(urlError).toBeDefined();
        expect(urlError?.message).toBe('translated_requiredField');
      }
    });

    it('should fail validation for webhook action with missing method', () => {
      const invalidWebhook = {
        type: 'webhook',
        webhook: {
          url: 'https://api.example.com',
        },
      };

      const result = actionSchema.safeParse(invalidWebhook);
      expect(result.success).toBe(false);

      if (!result.success) {
        const methodError = result.error.issues.find((issue) => issue.path.includes('method'));
        expect(methodError).toBeDefined();
        expect(methodError?.message).toBe('translated_requiredField');
      }
    });

    it('should fail validation for invalid URL format', () => {
      const invalidUrl = {
        type: 'openLink',
        openLink: {
          label: 'Click Here',
          url: 'invalid-url',
        },
      };

      const result = actionSchema.safeParse(invalidUrl);
      expect(result.success).toBe(false);

      if (!result.success) {
        const urlError = result.error.issues.find((issue) => issue.path.includes('url'));
        expect(urlError).toBeDefined();
        expect(urlError?.message).toBe('translated_urlInValid');
      }
    });
  });
});
