import AppConfig from '@/configs';

export const isAllowedDomains = () => {
  if (typeof window === 'undefined') return false;
  const includedDomains = AppConfig.INCLUDED_BASE_PATH_DOMAINS;
  return (
    includedDomains.filter((domain: string) => window.location.origin === `https://${domain}`)
      .length > 0
  );
};

export const isIncludedBasePathDomains = () => {
  return (
    AppConfig.INCLUDED_BASE_PATH_DOMAINS.filter((domain: string) =>
      window.location.origin.includes(domain)
    ).length > 0
  );
};

export const isExcludedBasePathDomains = () => {
  return (
    AppConfig.EXCLUDED_BASE_PATH_DOMAINS.filter((domain: string) =>
      window.location.origin.includes(domain)
    ).length > 0
  );
};

export const fixDomain = (subDomain: string, currentUrl: string) => {
  if (currentUrl.indexOf('://') !== -1) {
    // Extract the protocol and the domain from the current URL
    const parts = currentUrl.split('://');
    const protocol = parts[0];
    const domain = parts[1];

    // Split the domain into its subdomains and top-level domain
    const domainParts = domain.split('.');
    const subdomains = domainParts.slice(0, domainParts.length - 1);
    const topLevelDomain = domainParts[domainParts.length - 1];
    if (subdomains.length < 2) {
      // deca-dev.com or deca.cloud
      // Insert 'resola' as a subdomain
      subdomains.unshift(subDomain);
    }

    // Join the subdomains and the top-level domain
    const newDomain = `${subdomains.join('.')}.${topLevelDomain}`;

    // Construct the new URL with the updated subdomain
    return `${protocol}://${newDomain}`;
  }
  // invalid URL
  return currentUrl;
};
