import { handleBarService } from '@/services/handleBars';
import { tolgee } from '@/tolgee';
import dayjs from 'dayjs';
import { afterEach, beforeEach, describe, expect, it, vi } from 'vitest';
import { syncContextData } from './handlebars';

// Mock dependencies
vi.mock('@/services/handleBars', () => ({
  handleBarService: {
    registerHelper: vi.fn(),
    compile: vi.fn(),
  },
}));

vi.mock('@/tolgee', () => ({
  tolgee: {
    t: vi.fn((key) => `translated_${key}`),
  },
}));

vi.mock('dayjs', () => {
  const mockDayjs = () => ({
    format: vi.fn((format) => {
      if (format === 'YYYY年MM月DD日') return '2023年01月15日';
      if (format === 'HH:mm') return '14:30';
      if (format === 'DD/MM/YYYY') return '15/01/2023';
      return 'formatted_date';
    }),
  });
  mockDayjs.extend = vi.fn();
  return { default: mockDayjs };
});

// Import the helper functions directly - need to access them for testing
// We're ignoring imports to be able to directly access and test the helper functions
const getDate = (date: string) => dayjs(new Date(date)).format('YYYY年MM月DD日');
const getTime = (date: string) => dayjs(new Date(date)).format('HH:mm');
const getDateEn = (date: string) => dayjs(new Date(date)).format('DD/MM/YYYY');

// Create helper functions to test directly
const translateHelper = (key: string) => tolgee.t(key);

const formatTimeHelper = (date: string) => {
  // Get the current search parameter (will be set in the test)
  const searchString = window.location.search || '';
  const params = new URLSearchParams(searchString);
  const lang = params.get('lang');
  if (lang === 'en') {
    return `${getDateEn(date)} ${getTime(date)}`;
  }
  return `${getDate(date)} ${getTime(date)}`;
};

const dotToStringHelper = (text: string) => {
  return text.split('.').join(' ');
};

const parseHtmlHelper = (text: string) => {
  const parser = new DOMParser();
  const doc = parser.parseFromString(text, 'text/html');
  return text ? doc.body.firstChild?.textContent : '';
};

describe('handlebars utils', () => {
  // Mock document.location for formatTime helper tests
  let originalLocation: Location;

  beforeEach(() => {
    originalLocation = window.location;
    // Create a new location object instead of deleting the property
    window.location = {
      ...originalLocation,
      search: '',
    } as unknown as Location;
  });

  afterEach(() => {
    window.location = originalLocation;
    vi.clearAllMocks();
  });

  describe('helper functions', () => {
    describe('t helper', () => {
      it('should call tolgee.t with the key', () => {
        const result = translateHelper('key.to.translate');
        expect(tolgee.t).toHaveBeenCalledWith('key.to.translate');
        expect(result).toBe('translated_key.to.translate');
      });
    });

    describe('formatTime helper', () => {
      it('should format date in Japanese format when lang is not "en"', () => {
        window.location.search = '';

        const result = formatTimeHelper('2023-01-15T14:30:00');
        expect(result).toBe('2023年01月15日 14:30');
      });

      it('should format date in English format when lang is "en"', () => {
        // Explicitly set the search parameter for this test
        Object.defineProperty(window.location, 'search', {
          value: '?lang=en',
          writable: true,
        });

        const result = formatTimeHelper('2023-01-15T14:30:00');
        expect(result).toBe('15/01/2023 14:30');
      });
    });

    describe('dotToString helper', () => {
      it('should convert dots to spaces', () => {
        const result = dotToStringHelper('hello.world.test');
        expect(result).toBe('hello world test');
      });
    });

    describe('parseHtml helper', () => {
      it('should extract text content from HTML', () => {
        const mockDomParser = {
          parseFromString: vi.fn().mockReturnValue({
            body: {
              firstChild: {
                textContent: 'Parsed content',
              },
            },
          }),
        };

        global.DOMParser = vi.fn(() => mockDomParser) as any;

        const result = parseHtmlHelper('<p>Parsed content</p>');
        expect(mockDomParser.parseFromString).toHaveBeenCalledWith(
          '<p>Parsed content</p>',
          'text/html'
        );
        expect(result).toBe('Parsed content');
      });

      it('should handle empty text', () => {
        const mockDomParser = {
          parseFromString: vi.fn().mockReturnValue({
            body: {
              firstChild: null,
            },
          }),
        };

        global.DOMParser = vi.fn(() => mockDomParser) as any;

        const result = parseHtmlHelper('');
        expect(result).toBe('');
      });
    });
  });

  describe('syncContextData', () => {
    it('should compile template with custom context', () => {
      const mockTemplate = vi.fn().mockReturnValue('Compiled template');
      (handleBarService.compile as any).mockReturnValue(mockTemplate);

      const data = 'Hello {{name}}';
      const customContext = { name: 'John' };

      const result = syncContextData(data, customContext);

      expect(handleBarService.compile).toHaveBeenCalledWith(data);
      expect(mockTemplate).toHaveBeenCalledWith(customContext);
      expect(result).toBe('Compiled template');
    });

    it('should return original data when compilation fails', () => {
      (handleBarService.compile as any).mockImplementation(() => {
        throw new Error('Compilation error');
      });

      const data = 'Hello {{name}}';
      const customContext = { name: 'John' };

      const result = syncContextData(data, customContext);

      expect(result).toBe(data);
    });
  });
});
