import { Currency } from '@/constants/workspace';
import type { WSObject } from '@/models';
import type { Active, Over } from '@dnd-kit/core';
import type { MantineTheme } from '@mantine/core';
import { FieldTypes, ViewType } from '@resola-ai/ui/components';
import { afterEach, beforeEach, describe, expect, it, vi } from 'vitest';
import {
  createNewRow,
  customNotificationStyles,
  downloadImage,
  getCurrencyFormat,
  getDateTime,
  getFieldFormattedValue,
  getSelectedRowDetails,
  getViewFields,
  handleColumnOrderChange,
  handleColumnSizingChange,
  handleDragOption,
  isValidEmail,
  isValidLine,
  isValidPhone,
  isValidUrl,
  mapColumnsToView,
  normalizeViewGroups,
  sortManual,
  sortOptionsAsc,
  sortOptionsDesc,
  validateInput,
} from './workspace';

// Mock uuid
vi.mock('uuid', () => ({
  v4: vi.fn().mockReturnValue('mocked-uuid'),
}));

// Mock fetch for downloadImage tests
global.fetch = vi.fn();
global.URL.createObjectURL = vi.fn(() => 'blob:test');
global.URL.revokeObjectURL = vi.fn();

// Mock window.document functions
const mockAnchor = {
  style: { display: 'none' },
  href: '',
  download: '',
  click: vi.fn(),
  remove: vi.fn(),
};

// Mock document functions
const originalCreateElement = document.createElement;
const originalAppendChild = document.body.appendChild;
const originalRemoveChild = document.body.removeChild;

// Mock ViewAPI for tests
vi.mock('@/services/api/view', () => ({
  ViewAPI: {
    updateFieldOrder: vi
      .fn()
      .mockResolvedValue({ id: 'view1', fieldOrder: ['field2', 'field1', 'field3'] }),
  },
}));

describe('workspace utils', () => {
  describe('sorting functions', () => {
    const options = [
      { label: 'C', time: 3 },
      { label: 'A', time: 1 },
      { label: 'B', time: 2 },
    ];

    it('sortManual - should sort by time property', () => {
      const result = sortManual([...options]);
      expect(result[0].label).toBe('A');
      expect(result[1].label).toBe('B');
      expect(result[2].label).toBe('C');
    });

    it('sortOptionsAsc - should sort by label in ascending order', () => {
      const result = sortOptionsAsc([...options]);
      expect(result[0].label).toBe('A');
      expect(result[1].label).toBe('B');
      expect(result[2].label).toBe('C');
    });

    it('sortOptionsDesc - should sort by label in descending order', () => {
      const result = sortOptionsDesc([...options]);
      expect(result[0].label).toBe('C');
      expect(result[1].label).toBe('B');
      expect(result[2].label).toBe('A');
    });
  });

  describe('handleDragOption', () => {
    it('should reorder array based on drag positions', () => {
      const data = [
        { id: 'item-1', value: 'Item 1' },
        { id: 'item-2', value: 'Item 2' },
        { id: 'item-3', value: 'Item 3' },
      ];

      const active = { id: 'item-1' } as Active;
      const over = { id: 'item-3' } as Over;

      const result = handleDragOption(active, over, [...data]);

      expect((result[0] as { id: string }).id).toBe('item-2');
      expect((result[1] as { id: string }).id).toBe('item-3');
      expect((result[2] as { id: string }).id).toBe('item-1');
    });

    it('should return the original array if active or over items are not found', () => {
      const data = [
        { id: 'item-1', value: 'Item 1' },
        { id: 'item-2', value: 'Item 2' },
      ];

      const active = { id: 'non-existent' } as Active;
      const over = { id: 'item-2' } as Over;

      const result = handleDragOption(active, over, [...data]);

      // Should return the same order since the active item wasn't found
      expect(result).toEqual(data);
    });
  });

  describe('validation functions', () => {
    describe('isValidEmail', () => {
      it('should validate correct email formats', () => {
        expect(isValidEmail('<EMAIL>')).toBe(true);
        expect(isValidEmail('<EMAIL>')).toBe(true);
        expect(isValidEmail('<EMAIL>')).toBe(true);
      });

      it('should invalidate incorrect email formats', () => {
        expect(isValidEmail('user@')).toBe(false);
        expect(isValidEmail('user@example')).toBe(false);
        expect(isValidEmail('user example.com')).toBe(false);
        expect(isValidEmail('')).toBe(false);
      });
    });

    describe('isValidUrl', () => {
      it('should validate correct URL formats', () => {
        expect(isValidUrl('https://example.com')).toBe(true);
        expect(isValidUrl('http://example.com/path')).toBe(true);
        expect(isValidUrl('example.com')).toBe(true);
      });

      it('should invalidate incorrect URL formats', () => {
        expect(isValidUrl('htp://example')).toBe(false);
        expect(isValidUrl('example')).toBe(false);
        expect(isValidUrl('')).toBe(false);
      });
    });

    describe('isValidPhone', () => {
      it('should validate correct phone formats', () => {
        expect(isValidPhone('12345678')).toBe(true);
        expect(isValidPhone('123456789012345')).toBe(true);
      });

      it('should invalidate incorrect phone formats', () => {
        expect(isValidPhone('1234567')).toBe(false); // too short
        expect(isValidPhone('1234567890123456')).toBe(false); // too long
        expect(isValidPhone('abcdefgh')).toBe(false); // not numbers
        expect(isValidPhone('')).toBe(false);
      });
    });

    describe('isValidLine', () => {
      it('should validate correct Line ID formats', () => {
        expect(isValidLine('user1')).toBe(true);
        expect(isValidLine('user_name')).toBe(true);
        expect(isValidLine('user-name123')).toBe(true);
      });

      it('should invalidate incorrect Line ID formats', () => {
        expect(isValidLine('abc')).toBe(false); // too short
        expect(isValidLine('a'.repeat(51))).toBe(false); // too long
        expect(isValidLine('user@name')).toBe(false); // invalid character
        expect(isValidLine('')).toBe(false);
      });
    });
  });

  describe('validateInput', () => {
    it('should validate number input', () => {
      const t = (key: string) => key;

      expect(validateInput('123', FieldTypes.NUMBER, t, 'integer')).toBe('');
      expect(validateInput('123.45', FieldTypes.NUMBER, t, 'decimal')).toBe('');
      expect(validateInput('abc', FieldTypes.NUMBER, t, 'integer')).toBe('numberValidateErr');

      // Test for the right validation rule with a string that's too long
      const longValidNumber = '1'.repeat(51);
      expect(validateInput(longValidNumber, FieldTypes.NUMBER, t)).toBe('maxLengthErr');
    });

    it('should validate email input', () => {
      const t = (key: string) => key;

      expect(validateInput('<EMAIL>', FieldTypes.EMAIL, t)).toBe('');
      expect(validateInput('invalid-email', FieldTypes.EMAIL, t)).toBe('emailInValid');
    });

    it('should validate URL input', () => {
      const t = (key: string) => key;

      expect(validateInput('https://example.com', FieldTypes.URL, t)).toBe('');
      expect(validateInput('invalid-url', FieldTypes.URL, t)).toBe('urlInValid');
    });

    it('should validate phone number input', () => {
      const t = (key: string) => key;

      expect(validateInput('12345678', FieldTypes.PHONE_NUMBER, t)).toBe('');
      expect(validateInput('123', FieldTypes.PHONE_NUMBER, t)).toBe('phoneInvalid');
    });

    it('should validate currency input', () => {
      const t = (key: string) => key;

      expect(validateInput('123.45', FieldTypes.CURRENCY, t)).toBe('');
      expect(validateInput('abc', FieldTypes.CURRENCY, t)).toBe('numberValidateErr');
    });

    it('should validate percent input', () => {
      const t = (key: string) => key;

      expect(validateInput('99.9', FieldTypes.PERCENT, t)).toBe('');
      expect(validateInput('abc', FieldTypes.PERCENT, t)).toBe('numberValidateErr');
    });

    it('should validate single line text input', () => {
      const t = (key: string) => key;

      expect(validateInput('Short text', FieldTypes.SINGLE_LINE_TEXT, t)).toBe('');
      expect(validateInput('a'.repeat(51), FieldTypes.SINGLE_LINE_TEXT, t)).toBe('maxLengthErr');
    });

    it('should validate Line ID input', () => {
      const t = (key: string) => key;

      expect(validateInput('user_id', FieldTypes.LINE, t)).toBe('');
      expect(validateInput('a@b', FieldTypes.LINE, t)).toBe('lineInvalid');
    });

    it('should return empty string for unknown field types', () => {
      const t = (key: string) => key;

      expect(validateInput('any value', 'unknown-type' as any, t)).toBe('');
    });
  });

  describe('createNewRow', () => {
    it('should create a new row with default values based on column types', () => {
      const columns = [
        { id: 'checkbox', type: FieldTypes.CHECKBOX },
        {
          id: 'select',
          type: FieldTypes.SINGLE_SELECT,
          options: { defaultValue: 'option1' },
        },
        {
          id: 'multiSelect',
          type: FieldTypes.MULTI_SELECT,
          options: { defaultValue: ['option1', 'option2'] },
        },
        {
          id: 'date',
          type: FieldTypes.DATETIME,
          options: { useCurrentDate: true },
        },
        { id: 'text', type: FieldTypes.SINGLE_LINE_TEXT },
      ];

      const result = createNewRow(columns);

      expect(result.id).toBe('mocked-uuid');
      expect(result['checkbox']).toBe(false);
      expect(result['select']).toBe('option1');
      expect(result['multiSelect']).toEqual(['option1', 'option2']);
      expect(result['date']).toBeInstanceOf(Date);
      expect(result['text']).toBeUndefined();
    });

    it('should handle single select with "notSelected" default value', () => {
      const columns = [
        {
          id: 'select',
          type: FieldTypes.SINGLE_SELECT,
          options: { defaultValue: 'notSelected' },
        },
      ];

      const result = createNewRow(columns);

      expect(result['select']).toBeUndefined();
    });

    it('should handle DateTime without useCurrentDate option', () => {
      const columns = [
        {
          id: 'date',
          type: FieldTypes.DATETIME,
          options: { useCurrentDate: false },
        },
      ];

      const result = createNewRow(columns);

      expect(result['date']).toBeUndefined();
    });
  });

  describe('getViewFields', () => {
    it('should return fields in order of active view fieldOrder', () => {
      const object = {
        id: 'test-object',
        name: { singular: 'Test Object', plural: 'Test Objects' },
        fields: [
          {
            id: 'field1',
            name: 'Field 1',
            type: FieldTypes.SINGLE_LINE_TEXT,
            header: 'Field 1',
          } as any,
          {
            id: 'field2',
            name: 'Field 2',
            type: FieldTypes.SINGLE_LINE_TEXT,
            header: 'Field 2',
          } as any,
          {
            id: 'field3',
            name: 'Field 3',
            type: FieldTypes.SINGLE_LINE_TEXT,
            header: 'Field 3',
          } as any,
        ],
        views: [
          {
            fieldOrder: ['field3', 'field1', 'field2'],
          },
        ],
        userconfig: {
          viewId: 'view-1',
        },
      } as WSObject;

      const result = getViewFields(object);

      expect(result[0].id).toBe('field3');
      expect(result[1].id).toBe('field1');
      expect(result[2].id).toBe('field2');
    });

    it('should use first view if no active view', () => {
      const object = {
        id: 'test-object',
        name: { singular: 'Test Object', plural: 'Test Objects' },
        fields: [
          {
            id: 'field1',
            name: 'Field 1',
            type: FieldTypes.SINGLE_LINE_TEXT,
            header: 'Field 1',
          } as any,
          {
            id: 'field2',
            name: 'Field 2',
            type: FieldTypes.SINGLE_LINE_TEXT,
            header: 'Field 2',
          } as any,
        ],
        views: [
          {
            id: 'view-1',
            name: 'View 1',
            type: 'grid',
            icon: 'table',
            fieldOrder: ['field2', 'field1'],
          },
        ],
        userconfig: {
          viewId: undefined,
        },
      } as unknown as WSObject;

      const result = getViewFields(object);

      expect(result[0].id).toBe('field2');
      expect(result[1].id).toBe('field1');
    });

    it('should return empty array if no object provided', () => {
      const result = getViewFields(undefined);
      expect(result).toEqual([]);
    });

    it('should filter out fields without id', () => {
      const object = {
        id: 'test-object',
        name: { singular: 'Test Object', plural: 'Test Objects' },
        fields: [
          {
            id: 'field1',
            name: 'Field 1',
            type: FieldTypes.SINGLE_LINE_TEXT,
            header: 'Field 1',
          } as any,
          { name: 'Field without ID' } as any,
          {
            id: 'field2',
            name: 'Field 2',
            type: FieldTypes.SINGLE_LINE_TEXT,
            header: 'Field 2',
          } as any,
        ],
        views: [
          {
            fieldOrder: ['field1', 'field2'],
          },
        ],
        userconfig: {
          viewId: 'view-1',
        },
      } as WSObject;

      const result = getViewFields(object);

      expect(result.length).toBe(2);
      expect(result[0].id).toBe('field1');
      expect(result[1].id).toBe('field2');
    });

    it('should handle objects without views', () => {
      const object = {
        id: 'test-object',
        name: { singular: 'Test Object', plural: 'Test Objects' },
        fields: [
          {
            id: 'field1',
            name: 'Field 1',
            type: FieldTypes.SINGLE_LINE_TEXT,
            header: 'Field 1',
          } as any,
          {
            id: 'field2',
            name: 'Field 2',
            type: FieldTypes.SINGLE_LINE_TEXT,
            header: 'Field 2',
          } as any,
        ],
        views: [],
        userconfig: {
          viewId: undefined,
        },
      } as unknown as WSObject;

      const result = getViewFields(object);

      // Should return all fields in original order
      expect(result.length).toBe(2);
      expect(result[0].id).toBe('field1');
      expect(result[1].id).toBe('field2');
    });
  });

  describe('getDateTime', () => {
    it('should format date and time correctly', () => {
      // Use a fixed date for testing
      const testDate = new Date('2023-01-15T12:30:00Z');

      const result = getDateTime(
        'YYYY-MM-DD', // Use string format directly instead of FormatDate enum
        'HH:mm',
        'Asia/Tokyo',
        false,
        testDate.toISOString(),
        'en'
      );

      // Exact format will depend on timezone handling, but should include date and time
      expect(result).toContain('2023');
      expect(result).toMatch(/\d\d:\d\d/); // Should contain time in format HH:MM
    });

    it('should display timezone abbreviation when requested', () => {
      const testDate = new Date('2023-01-15T12:30:00Z');

      const result = getDateTime(
        'YYYY-MM-DD', // Use string format directly
        'HH:mm',
        'Asia/Tokyo',
        true,
        testDate.toISOString()
      );

      // Should include timezone abbreviation at the end
      expect(result.trim()).toMatch(/(JST|JST \+09|GMT\+9|GMT\+09:00)$/);
    });

    it('should use Japanese date format when specified', () => {
      const testDate = new Date('2023-01-15T12:30:00Z');

      const result = getDateTime(
        'YYYY年MM月DD日', // Japanese format
        'HH:mm',
        'Asia/Tokyo',
        false,
        testDate.toISOString(),
        'ja'
      );

      // Japanese format should include 年月日
      expect(result).toMatch(/\d{4}年\d{2}月\d{2}日/);
    });

    it('should use current date when no date is provided', () => {
      const result = getDateTime(
        'YYYY-MM-DD', // Use string format directly
        'HH:mm',
        'Asia/Tokyo',
        false
      );

      // Should return a non-empty string with the current date/time
      expect(result.length).toBeGreaterThan(0);
    });
  });

  describe('getCurrencyFormat', () => {
    it('should format currency with default parameters', () => {
      const result = getCurrencyFormat(1234.5);
      // The result may vary based on locale, so use a flexible matcher
      expect(result).toMatch(/\$[0-9,.]+5/);
    });

    it('should format currency with custom symbol and precision', () => {
      const result = getCurrencyFormat(1234.5, '¥', 'local', 2);
      // The result may vary based on locale, but should have the symbol and 2 decimal places
      expect(result).toMatch(/¥[0-9,.]+50/);
    });

    it('should format currency with reverse symbol position', () => {
      const result = getCurrencyFormat(1234.5, '€', 'local', 2, true);
      // Symbol should be at the end
      expect(result).toMatch(/[0-9,.]+50€/);
    });

    it('should use different separators based on format', () => {
      const result = getCurrencyFormat(1234.5, '$', 'periodComma', 2);
      // Should use period as thousand separator and comma as decimal
      expect(result).toMatch(/\$1\.234,50/);
    });
  });

  describe('getFieldFormattedValue', () => {
    it('should format currency values', () => {
      const item = {
        type: FieldTypes.CURRENCY,
        value: '1234.5',
        options: {
          currency: Currency.usd,
          decimalPlaces: 2,
        },
      };

      const result = getFieldFormattedValue(item);
      expect(result).toMatch(/\$[0-9,.]+50/);
    });

    it('should format date-time values', () => {
      const item = {
        type: FieldTypes.DATETIME,
        value: '2023-01-15T12:30:00Z',
        options: {
          date: { format: 'YYYY-MM-DD' },
          time: { enabled: true, format: 'HH:mm' },
          timezone: { format: 'Asia/Tokyo' },
        },
      };

      const result = getFieldFormattedValue(item);
      expect(result).toContain('2023');
    });

    it('should format percent values', () => {
      const item = {
        type: FieldTypes.PERCENT,
        value: '50',
        options: {
          decimalPlaces: 1,
        },
      };

      const result = getFieldFormattedValue(item);
      expect(result).toMatch(/50\.0%/);
    });

    it('should return string value for other types', () => {
      const item = {
        type: FieldTypes.SINGLE_LINE_TEXT,
        value: 'Hello World',
        options: {},
      };

      const result = getFieldFormattedValue(item);
      expect(result).toBe('Hello World');
    });

    it('should handle errors in date formatting', () => {
      const item = {
        type: FieldTypes.DATETIME,
        value: 'invalid-date',
        options: {
          date: { format: 'YYYY-MM-DD' },
          time: { enabled: true },
        },
      };

      const result = getFieldFormattedValue(item);
      // Just expect it to return something about the invalid date
      expect(result).toContain('Invalid');
    });
  });

  describe('downloadImage', () => {
    beforeEach(() => {
      // Mock document.createElement
      document.createElement = vi.fn(() => mockAnchor as any);
      document.body.appendChild = vi.fn();
      document.body.removeChild = vi.fn();

      // Reset mock function calls
      mockAnchor.click.mockReset();
      vi.mocked(global.fetch).mockReset();
      vi.mocked(global.URL.createObjectURL).mockReset();
      vi.mocked(global.URL.revokeObjectURL).mockReset();

      // Mock window.alert
      vi.spyOn(window, 'alert').mockImplementation(() => {});
    });

    afterEach(() => {
      // Restore original document functions
      document.createElement = originalCreateElement;
      document.body.appendChild = originalAppendChild;
      document.body.removeChild = originalRemoveChild;

      // Restore window.alert
      vi.mocked(window.alert).mockRestore();
    });

    it('should download an image when given a valid URL', async () => {
      // Mock successful fetch response
      const mockBlob = new Blob(['test'], { type: 'image/jpeg' });
      const mockResponse = {
        ok: true,
        blob: vi.fn().mockResolvedValueOnce(mockBlob),
      };
      vi.mocked(global.fetch).mockResolvedValueOnce(mockResponse as any);

      // Call the function
      downloadImage('https://example.com/image.jpg');

      // Wait for all promises to resolve
      await vi.waitFor(() => {
        expect(global.fetch).toHaveBeenCalledWith('https://example.com/image.jpg');
        expect(mockResponse.blob).toHaveBeenCalled();
        expect(document.createElement).toHaveBeenCalledWith('a');
      });
    });

    it('should show an alert when fetch fails', async () => {
      // Mock failed fetch
      vi.mocked(global.fetch).mockRejectedValueOnce(new Error('Network error'));

      // Call the function
      downloadImage('https://example.com/image.jpg');

      // Wait for the alert to be called
      await vi.waitFor(() => {
        expect(window.alert).toHaveBeenCalledWith('Failed to download image');
      });
    });
  });

  describe('customNotificationStyles', () => {
    it('should return style object with default colors', () => {
      // Create a more complete mock of MantineTheme
      const theme = {
        white: '#FFFFFF',
        black: '#000000',
        colors: {
          decaGreen: [
            '#E6FCF5',
            '#C3FAE8',
            '#96F2D7',
            '#63E6BE',
            '#38D9A9',
            '#20C997',
            '#12B886',
            '#0CA678',
            '#099268',
            '#087F5B',
          ],
          gray: [
            '#f8f9fa',
            '#e9ecef',
            '#dee2e6',
            '#ced4da',
            '#adb5bd',
            '#6c757d',
            '#495057',
            '#343a40',
            '#212529',
          ],
        },
        primaryColor: 'blue',
        primaryShade: 6,
        fn: {
          rgba: () => 'rgba(0,0,0,0.5)',
        },
      } as unknown as MantineTheme;

      const result = customNotificationStyles(theme);

      expect(result).toHaveProperty('root');
      expect(typeof result.root).toBe('object');
      expect(result.root).toHaveProperty('backgroundColor');
    });

    it('should use custom background and text colors when provided', () => {
      // Create a more complete mock of MantineTheme
      const theme = {
        white: '#FFFFFF',
        black: '#000000',
        colors: {
          decaGreen: [
            '#E6FCF5',
            '#C3FAE8',
            '#96F2D7',
            '#63E6BE',
            '#38D9A9',
            '#20C997',
            '#12B886',
            '#0CA678',
            '#099268',
            '#087F5B',
          ],
          gray: [
            '#f8f9fa',
            '#e9ecef',
            '#dee2e6',
            '#ced4da',
            '#adb5bd',
            '#6c757d',
            '#495057',
            '#343a40',
            '#212529',
          ],
        },
        primaryColor: 'blue',
        primaryShade: 6,
        fn: {
          rgba: () => 'rgba(0,0,0,0.5)',
        },
      } as unknown as MantineTheme;

      const bgColor = '#FF0000';
      const textColor = '#00FF00';

      const result = customNotificationStyles(theme, bgColor, textColor);

      expect(result.root).toHaveProperty('backgroundColor');
      expect(result.root.backgroundColor).toContain(bgColor);
    });
  });

  describe('getSelectedRowDetails', () => {
    it('should return selected rows', () => {
      const rowSelection = {
        row1: true,
        row2: false,
        row3: true,
      };

      const data = [
        { id: 'row1', name: 'Row 1' },
        { id: 'row2', name: 'Row 2' },
        { id: 'row3', name: 'Row 3' },
      ];

      const result = getSelectedRowDetails(rowSelection, data);

      expect(result.length).toBe(2);
      expect(result[0].id).toBe('row1');
      expect(result[1].id).toBe('row3');
    });

    it('should return empty array when no rows are selected', () => {
      const rowSelection = {
        row1: false,
        row2: false,
      };

      const data = [
        { id: 'row1', name: 'Row 1' },
        { id: 'row2', name: 'Row 2' },
      ];

      const result = getSelectedRowDetails(rowSelection, data);

      expect(result.length).toBe(0);
    });
  });

  describe('handleColumnOrderChange', () => {
    it('should call handleOrderField with new orders when active view matches selected view', async () => {
      const newOrders = ['field2', 'field1', 'field3'];
      const activeView = {
        id: 'view1',
        fields: [
          { id: 'field1', name: 'Field 1', type: FieldTypes.SINGLE_LINE_TEXT, header: 'Field 1' },
          { id: 'field2', name: 'Field 2', type: FieldTypes.SINGLE_LINE_TEXT, header: 'Field 2' },
          { id: 'field3', name: 'Field 3', type: FieldTypes.SINGLE_LINE_TEXT, header: 'Field 3' },
        ],
        name: 'View 1',
        type: 'grid',
        icon: 'grid',
        workspaceId: 'ws1',
      };
      const selectedView = {
        id: 'view1',
        fieldOrder: ['field1', 'field2', 'field3'],
      };

      const handleViewChange = vi.fn();

      // Call the function that internally uses ViewAPI.updateFieldOrder
      const result = handleColumnOrderChange(
        newOrders,
        activeView as any,
        selectedView as any,
        handleViewChange
      );

      // Wait for promises to resolve
      await vi.waitFor(() => {
        expect(handleViewChange).toHaveBeenCalled();
      });

      // Check that result is undefined (not explicitly returning a value)
      expect(result).toBeUndefined();

      // Check if handleViewChange was called with the correct parameters
      expect(handleViewChange).toHaveBeenCalledWith(
        'view1',
        expect.objectContaining({
          ...activeView,
          fieldOrder: ['field2', 'field1', 'field3'],
        })
      );
    });

    it('should return undefined when active view does not match selected view', () => {
      const newOrders = ['field2', 'field1', 'field3'];
      const activeView = { id: 'view1' };
      const selectedView = {
        id: 'view2',
        fieldOrder: ['field1', 'field2', 'field3'],
      };

      const handleViewChange = vi.fn();

      const result = handleColumnOrderChange(
        newOrders,
        activeView as any,
        selectedView as any,
        handleViewChange
      );

      expect(result).toBeUndefined();
      expect(handleViewChange).not.toHaveBeenCalled();
    });
  });

  describe('handleColumnSizingChange', () => {
    it('should update state and call handleViewChange with new column widths', () => {
      const size = { field1: 150, field2: 200 };
      const activeView = { id: 'view1', columnWidth: { field1: 100 } };
      const setState = vi.fn();
      const handleViewChange = vi.fn();

      handleColumnSizingChange(size, activeView, setState, handleViewChange);

      expect(setState).toHaveBeenCalled();
      expect(handleViewChange).toHaveBeenCalledWith(
        'view1',
        {
          ...activeView,
          columnWidth: { field1: 150, field2: 200 },
        },
        'orderColumn'
      );
    });
  });

  describe('mapColumnsToView', () => {
    it('should map columns based on active view settings', () => {
      const tableColumns = [
        { id: 'field1', name: 'Field 1' },
        { id: 'field2', name: 'Field 2' },
      ];

      const activeView = {
        columnWidth: { field1: 150, field2: 200 },
        fieldOrder: ['field2', 'field1'],
        fields: [
          { fieldMetaId: 'field1', size: 150 },
          { fieldMetaId: 'field2', size: 200 },
        ],
      };

      const objectName = 'TestObject';
      const columnWidth = { field1: 150, field2: 200 };

      const result = mapColumnsToView(tableColumns, activeView, objectName, columnWidth);

      expect(result.length).toBe(2);
      expect(result.fields[0].id).toBe('field1');
      expect(result.fields[1].id).toBe('field2');
      expect(result.fields[0].size).toBe(150);
      expect(result.fields[1].size).toBe(200);
    });
  });

  describe('normalizeViewGroups', () => {
    it('should normalize view groups and views', () => {
      // Create view groups with the correct structure
      const viewGroups = [
        {
          id: 'group1',
          name: 'Group 1',
          type: ViewType.GROUP,
          viewIds: ['view1'],
          views: [
            {
              id: 'view1',
              name: 'View 1',
              workspaceId: 'ws1',
              type: 'grid',
              icon: 'grid',
              fields: [],
            },
          ],
        },
      ];

      const views = [
        {
          id: 'view1',
          name: 'View 1 Updated',
          workspaceId: 'ws1',
          type: 'grid',
          icon: 'grid',
          fields: [],
        },
        {
          id: 'view2',
          name: 'View 2',
          workspaceId: 'ws1',
          type: 'grid',
          icon: 'grid',
          fields: [],
        },
      ];

      const result = normalizeViewGroups(viewGroups as any, views as any);

      // Test that the result has the expected structure
      expect(result.viewGroups).toBeDefined();
      expect(result.viewGroups.length).toBe(1);
      expect(result.views).toBeDefined();
      // Check that views were updated
      expect(result.views?.find((v) => v.id === 'view1')?.name).toBe('View 1');
    });

    it('should return view groups without changes when no views are provided', () => {
      const viewGroups = [
        {
          id: 'group1',
          name: 'Group 1',
          type: ViewType.GROUP,
          viewIds: ['view1'],
          views: [
            {
              id: 'view1',
              name: 'View 1',
              workspaceId: 'ws1',
              type: 'grid',
              icon: 'grid',
              fields: [],
            },
          ],
        },
      ];

      const result = normalizeViewGroups(viewGroups as any);

      // The viewGroups should be normalized
      expect(result.viewGroups).toBeDefined();
      expect(result.viewGroups.length).toBe(1);
      expect(result.viewGroups[0].id).toBe('group1');
      expect(result.viewGroups[0].name).toBe('Group 1');
      expect(result.views).toBeUndefined();
    });
  });
});
