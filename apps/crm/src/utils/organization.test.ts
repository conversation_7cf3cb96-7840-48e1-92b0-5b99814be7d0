import { afterEach, beforeEach, describe, expect, it, vi } from 'vitest';
import { getOrganizationName } from './organization';

// Mock the AppConfig
vi.mock('@/configs', () => ({
  default: {
    DEFAULT_ORGANIZATION_NAME: 'default-org',
    EXCLUDED_BASE_PATH_DOMAINS: ['excluded-domain.com'],
    SKIP_ORG_NAME_DOMAINS: ['skip-org.deca-dev.com'],
  },
}));

describe('organization utils', () => {
  // Store the original location object
  const originalLocation = window.location;
  const defaultOrgName = 'default-org';

  // Helper function to set window.location.origin and host
  const setWindowLocation = (origin: string, host?: string) => {
    Object.defineProperty(window, 'location', {
      configurable: true,
      value: {
        origin,
        host: host || new URL(origin).host,
      },
      writable: true,
    });
  };

  beforeEach(() => {
    // Set a default test domain
    setWindowLocation('https://test-domain.com');
  });

  afterEach(() => {
    // Restore window.location
    Object.defineProperty(window, 'location', {
      configurable: true,
      value: originalLocation,
      writable: true,
    });
    vi.resetAllMocks();
  });

  describe('getOrganizationName', () => {
    // Test cases where default organization name should be returned
    const defaultOrgTestCases = [
      {
        name: 'when window is undefined',
        setup: () => {
          const originalWindow = global.window;
          // @ts-expect-error - Testing when window is undefined
          global.window = undefined;
          return originalWindow;
        },
        teardown: (originalWindow: typeof global.window) => {
          global.window = originalWindow;
        },
      },
      {
        name: 'for localhost',
        origin: 'http://localhost:3000',
      },
      {
        name: 'for ngrok',
        origin: 'https://12345.ngrok.io',
      },
      {
        name: 'for cloudworkstations',
        origin: 'https://workstation.cloudworkstations.dev',
      },
      {
        name: 'for excluded domains',
        origin: 'https://app.excluded-domain.com',
      },
    ];

    // Run tests for default organization name cases
    defaultOrgTestCases.forEach((testCase) => {
      it(`should return default organization name ${testCase.name}`, () => {
        if (testCase.setup) {
          const originalValue = testCase.setup();
          expect(getOrganizationName()).toBe(defaultOrgName);
          if (testCase.teardown) testCase.teardown(originalValue);
        } else if (testCase.origin) {
          setWindowLocation(testCase.origin);
          expect(getOrganizationName()).toBe(defaultOrgName);
        }
      });
    });

    it('should return undefined for domains in SKIP_ORG_NAME_DOMAINS', () => {
      setWindowLocation('https://skip-org.deca-dev.com');
      expect(getOrganizationName()).toBeUndefined();
    });

    it('should return organization name from subdomain', () => {
      setWindowLocation('https://myorg.deca-dev.com');
      expect(getOrganizationName()).toBe('myorg');
    });

    it('should handle errors and return default organization name', () => {
      // Mock the split method to throw an error
      const originalSplit = String.prototype.split;
      String.prototype.split = () => {
        throw new Error('Test error');
      };

      expect(getOrganizationName()).toBe(defaultOrgName);

      // Restore the original split method
      String.prototype.split = originalSplit;
    });
  });
});
