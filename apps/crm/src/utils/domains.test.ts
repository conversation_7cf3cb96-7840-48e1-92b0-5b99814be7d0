import { afterEach, beforeEach, describe, expect, it, vi } from 'vitest';
import {
  fixDomain,
  isAllowedDomains,
  isExcludedBasePathDomains,
  isIncludedBasePathDomains,
} from './domains';

// Mock the AppConfig
vi.mock('@/configs', () => ({
  default: {
    INCLUDED_BASE_PATH_DOMAINS: ['included-domain.com', 'another-included.com'],
    EXCLUDED_BASE_PATH_DOMAINS: ['excluded-domain.com', 'another-excluded.com'],
  },
}));

describe('domains utils', () => {
  // Store the original location object if it exists
  const originalLocation = typeof window !== 'undefined' ? window.location : undefined;

  beforeEach(() => {
    // Mock window.location using defineProperty if window exists
    if (typeof window !== 'undefined') {
      Object.defineProperty(window, 'location', {
        configurable: true,
        value: {
          origin: 'https://test-domain.com',
        },
        writable: true,
      });
    }
  });

  afterEach(() => {
    // Restore window.location if it exists and we have the original
    if (typeof window !== 'undefined' && originalLocation) {
      Object.defineProperty(window, 'location', {
        configurable: true,
        value: originalLocation,
        writable: true,
      });
    }
    vi.resetAllMocks();
  });

  describe('isAllowedDomains', () => {
    it('should return true when origin matches an included domain', () => {
      Object.defineProperty(window.location, 'origin', {
        configurable: true,
        value: 'https://included-domain.com',
      });
      expect(isAllowedDomains()).toBe(true);
    });

    it('should return false when origin does not match any included domain', () => {
      Object.defineProperty(window.location, 'origin', {
        configurable: true,
        value: 'https://not-included-domain.com',
      });
      expect(isAllowedDomains()).toBe(false);
    });

    it('should return false when window is undefined', () => {
      const originalWindow = global.window;
      // @ts-expect-error - Testing when window is undefined
      global.window = undefined;
      expect(isAllowedDomains()).toBe(false);
      global.window = originalWindow;
    });
  });

  describe('isIncludedBasePathDomains', () => {
    it('should return true when origin includes an included domain', () => {
      Object.defineProperty(window.location, 'origin', {
        configurable: true,
        value: 'https://subdomain.included-domain.com',
      });
      expect(isIncludedBasePathDomains()).toBe(true);
    });

    it('should return true when origin includes a partial match of an included domain', () => {
      Object.defineProperty(window.location, 'origin', {
        configurable: true,
        value: 'https://subdomain.not-included-domain.com',
      });
      expect(isIncludedBasePathDomains()).toBe(true);
    });
  });

  describe('isExcludedBasePathDomains', () => {
    it('should return true when origin includes an excluded domain', () => {
      Object.defineProperty(window.location, 'origin', {
        configurable: true,
        value: 'https://subdomain.excluded-domain.com',
      });
      expect(isExcludedBasePathDomains()).toBe(true);
    });

    it('should return true when origin includes a partial match of an excluded domain', () => {
      Object.defineProperty(window.location, 'origin', {
        configurable: true,
        value: 'https://subdomain.not-excluded-domain.com',
      });
      expect(isExcludedBasePathDomains()).toBe(true);
    });
  });

  describe('fixDomain', () => {
    it('should add subdomain to a domain with no subdomains', () => {
      const result = fixDomain('resola', 'https://deca-dev.com');
      expect(result).toBe('https://resola.deca-dev.com');
    });

    it('should not modify a domain with one subdomain', () => {
      const result = fixDomain('resola', 'https://app.deca-dev.com');
      expect(result).toBe('https://app.deca-dev.com');
    });

    it('should not modify a domain with multiple subdomains', () => {
      const result = fixDomain('resola', 'https://test.app.deca-dev.com');
      expect(result).toBe('https://test.app.deca-dev.com');
    });

    it('should return the original URL if it does not contain ://', () => {
      const result = fixDomain('resola', 'invalid-url');
      expect(result).toBe('invalid-url');
    });
  });
});
