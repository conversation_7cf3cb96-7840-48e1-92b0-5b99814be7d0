import { beforeEach, describe, expect, it, vi } from 'vitest';
import ComponentUtils from './component';

// Mock React
vi.mock('react', () => ({
  lazy: vi.fn((factory) => factory),
}));

describe('component utils', () => {
  // Mock localStorage
  const localStorageMock = (() => {
    let store: Record<string, string> = {};
    return {
      getItem: vi.fn((key: string) => store[key] || null),
      setItem: vi.fn((key: string, value: string) => {
        store[key] = value;
      }),
      clear: vi.fn(() => {
        store = {};
      }),
    };
  })();

  beforeEach(() => {
    // Setup mocks - safely check if window exists first
    if (typeof window !== 'undefined') {
      Object.defineProperty(window, 'localStorage', {
        value: localStorageMock,
      });
    }

    // Clear mocks before each test
    vi.clearAllMocks();
    localStorageMock.clear();
  });

  describe('lazy', () => {
    it('should exist as a function', () => {
      expect(typeof ComponentUtils.lazy).toBe('function');
    });
  });
});
