import AppConfig from '@/configs';
import { isExcludedBasePathDomains, isIncludedBasePathDomains } from './domains';

export const getRedirectUri = () => {
  if (typeof window === 'undefined') {
    return '';
  }
  if (isExcludedBasePathDomains()) {
    return `${window.location.origin}`;
  }
  if (isIncludedBasePathDomains()) {
    return `${window.location.origin}${AppConfig.BASE_PATH}`;
  }
  return `${window.location.origin}`;
};
