import { FieldTypes } from '@resola-ai/ui/components';
import { z } from 'zod';

export const createObjectFieldSchema = (t: (key: string) => string) =>
  z
    .object({
      name: z.string().min(1, { message: t('requiredField') }),
      type: z.string(),
      isProtected: z.boolean().optional(),
      options: z
        .object({
          objectId: z.string().optional(),
          fieldId: z.string().optional(),
        })
        .passthrough(),
    })
    .superRefine((data, ctx) => {
      if (data.type === FieldTypes.RELATIONSHIP) {
        if (!data.options.objectId) {
          ctx.addIssue({
            code: z.ZodIssueCode.custom,
            path: ['options', 'objectId'],
            message: t('requiredField'),
          });
        }
        if (!data.options.fieldId) {
          ctx.addIssue({
            code: z.ZodIssueCode.custom,
            path: ['options', 'fieldId'],
            message: t('requiredField'),
          });
        }
      }
    });

const URL_REGEX = /^(https?):\/\/[^\s/$.?#].[^\s]*$|^[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/;

const headerParametersSchema = z
  .object({
    enabled: z.boolean().optional(),
    type: z.enum(['json', 'manually']).optional(),
    json: z.string().optional(),
    parameters: z
      .array(
        z.object({
          name: z.string().optional(),
          value: z.string().optional(),
        })
      )
      .optional(),
  })
  .optional();

export const customActionSchemas = (t: (key: string) => string) => {
  const openLinkSchema = z
    .object({
      label: z.string({ required_error: t('requiredField') }),
      url: z
        .string({ required_error: t('requiredField') })
        .refine((val) => URL_REGEX.test(val), { message: t('urlInValid') }),
      color: z.string().optional(),
    })
    .passthrough();

  const webhookSchema = z
    .object({
      method: z.string({ required_error: t('requiredField') }),
      url: z
        .string({ required_error: t('requiredField') })
        .refine((val) => URL_REGEX.test(val), { message: t('urlInValid') }),
      sendHeaders: headerParametersSchema,
      sendBody: headerParametersSchema,
      icon: z.string().optional(),
    })
    .passthrough();

  return z.discriminatedUnion('type', [
    z.object({
      type: z.literal('openLink'),
      openLink: openLinkSchema,
    }),
    z.object({
      type: z.literal('webhook'),
      webhook: webhookSchema,
    }),
  ]);
};
