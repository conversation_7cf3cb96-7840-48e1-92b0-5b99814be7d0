applications:
  - appRoot: apps/livechat
    customHeaders:
      - pattern: /**/*.js
        headers:
          - key: Access-Control-Allow-Origin
            value: '*'
          - key: Access-Control-Allow-Headers
            value: '*'
          - key: Access-Control-Allow-Methods
            value: GET
      - pattern: /**/*.css
        headers:
          - key: Access-Control-Allow-Origin
            value: '*'
          - key: Access-Control-Allow-Headers
            value: '*'
          - key: Access-Control-Allow-Methods
            value: GET
  - appRoot: apps/widget-engine-app
    customHeaders:
      - pattern: /**/*.js
        headers:
          - key: Access-Control-Allow-Origin
            value: '*'
          - key: Access-Control-Allow-Headers
            value: '*'
          - key: Access-Control-Allow-Methods
            value: GET
      - pattern: /**/*.css
        headers:
          - key: Access-Control-Allow-Origin
            value: '*'
          - key: Access-Control-Allow-Headers
            value: '*'
          - key: Access-Control-Allow-Methods
            value: GET
  - appRoot: apps/chatbot
    customHeaders:
      - pattern: /**/*.js
        headers:
          - key: Access-Control-Allow-Origin
            value: '*'
          - key: Access-Control-Allow-Headers
            value: '*'
          - key: Access-Control-Allow-Methods
            value: GET
      - pattern: /**/*.css
        headers:
          - key: Access-Control-Allow-Origin
            value: '*'
          - key: Access-Control-Allow-Headers
            value: '*'
          - key: Access-Control-Allow-Methods
            value: GET
  - appRoot: apps/chatbox
    customHeaders:
      - pattern: /**/*.js
        headers:
          - key: Access-Control-Allow-Origin
            value: '*'
          - key: Access-Control-Allow-Headers
            value: '*'
          - key: Access-Control-Allow-Methods
            value: GET
      - pattern: /**/*.css
        headers:
          - key: Access-Control-Allow-Origin
            value: '*'
          - key: Access-Control-Allow-Headers
            value: '*'
          - key: Access-Control-Allow-Methods
            value: GET
  - appRoot: apps/account
    customHeaders:
      - pattern: /**/*.js
        headers:
          - key: Access-Control-Allow-Origin
            value: '*'
          - key: Access-Control-Allow-Headers
            value: '*'
          - key: Access-Control-Allow-Methods
            value: GET
      - pattern: /**/*.css
        headers:
          - key: Access-Control-Allow-Origin
            value: '*'
          - key: Access-Control-Allow-Headers
            value: '*'
          - key: Access-Control-Allow-Methods
            value: GET

  - appRoot: apps/management
    customHeaders:
      - pattern: /**/*.js
        headers:
          - key: Access-Control-Allow-Origin
            value: '*'
          - key: Access-Control-Allow-Headers
            value: '*'
          - key: Access-Control-Allow-Methods
            value: GET
      - pattern: /**/*.css
        headers:
          - key: Access-Control-Allow-Origin
            value: '*'
          - key: Access-Control-Allow-Headers
            value: '*'
          - key: Access-Control-Allow-Methods
            value: GET
  - appRoot: apps/crm
    customHeaders:
      - pattern: /**/*.js
        headers:
          - key: Access-Control-Allow-Origin
            value: '*'
          - key: Access-Control-Allow-Headers
            value: '*'
          - key: Access-Control-Allow-Methods
            value: GET
      - pattern: /**/*.css
        headers:
          - key: Access-Control-Allow-Origin
            value: '*'
          - key: Access-Control-Allow-Headers
            value: '*'
          - key: Access-Control-Allow-Methods
            value: GET
  - appRoot: apps/apex
    customHeaders:
      - pattern: /**/*.js
        headers:
          - key: Access-Control-Allow-Origin
            value: '*'
          - key: Access-Control-Allow-Headers
            value: '*'
          - key: Access-Control-Allow-Methods
            value: GET
      - pattern: /**/*.css
        headers:
          - key: Access-Control-Allow-Origin
            value: '*'
          - key: Access-Control-Allow-Headers
            value: '*'
          - key: Access-Control-Allow-Methods
            value: GET
  - appRoot: apps/demo
    customHeaders:
      - pattern: /**/*.js
        headers:
          - key: Access-Control-Allow-Origin
            value: '*'
          - key: Access-Control-Allow-Headers
            value: '*'
          - key: Access-Control-Allow-Methods
            value: GET
      - pattern: /**/*.css
        headers:
          - key: Access-Control-Allow-Origin
            value: '*'
          - key: Access-Control-Allow-Headers
            value: '*'
          - key: Access-Control-Allow-Methods
            value: GET
  - appRoot: apps/workshop
    customHeaders:
      - pattern: /**/*.js
        headers:
          - key: Access-Control-Allow-Origin
            value: '*'
          - key: Access-Control-Allow-Headers
            value: '*'
          - key: Access-Control-Allow-Methods
            value: GET
      - pattern: /**/*.css
        headers:
          - key: Access-Control-Allow-Origin
            value: '*'
          - key: Access-Control-Allow-Headers
            value: '*'
          - key: Access-Control-Allow-Methods
            value: GET
      - pattern: /**/*.woff2
        headers:
          - key: Access-Control-Allow-Origin
            value: '*'
          - key: Access-Control-Allow-Headers
            value: '*'
          - key: Access-Control-Allow-Methods
            value: GET
  - appRoot: apps/kb
    customHeaders:
      - pattern: /**/*.js
        headers:
          - key: Access-Control-Allow-Origin
            value: '*'
          - key: Access-Control-Allow-Headers
            value: '*'
          - key: Access-Control-Allow-Methods
            value: GET
      - pattern: /**/*.css
        headers:
          - key: Access-Control-Allow-Origin
            value: '*'
          - key: Access-Control-Allow-Headers
            value: '*'
          - key: Access-Control-Allow-Methods
            value: GET
      - pattern: /**/*.woff2
        headers:
          - key: Access-Control-Allow-Origin
            value: '*'
          - key: Access-Control-Allow-Headers
            value: '*'
          - key: Access-Control-Allow-Methods
            value: GET
  - appRoot: apps/form-admin
    customHeaders:
      - pattern: /**/*.js
        headers:
          - key: Access-Control-Allow-Origin
            value: '*'
          - key: Access-Control-Allow-Headers
            value: '*'
          - key: Access-Control-Allow-Methods
            value: GET
      - pattern: /**/*.css
        headers:
          - key: Access-Control-Allow-Origin
            value: '*'
          - key: Access-Control-Allow-Headers
            value: '*'
          - key: Access-Control-Allow-Methods
            value: GET
      - pattern: /**/*.woff2
        headers:
          - key: Access-Control-Allow-Origin
            value: '*'
          - key: Access-Control-Allow-Headers
            value: '*'
          - key: Access-Control-Allow-Methods
            value: GET
      - pattern: /**/*.svg
        headers:
          - key: Access-Control-Allow-Origin
            value: '*'
          - key: Access-Control-Allow-Headers
            value: '*'
          - key: Access-Control-Allow-Methods
            value: GET
  - appRoot: apps/form-client
    customHeaders:
      - pattern: /**/*.js
        headers:
          - key: Access-Control-Allow-Origin
            value: '*'
          - key: Access-Control-Allow-Headers
            value: '*'
          - key: Access-Control-Allow-Methods
            value: GET
      - pattern: /**/*.css
        headers:
          - key: Access-Control-Allow-Origin
            value: '*'
          - key: Access-Control-Allow-Headers
            value: '*'
          - key: Access-Control-Allow-Methods
            value: GET
      - pattern: /**/*.woff2
        headers:
          - key: Access-Control-Allow-Origin
            value: '*'
          - key: Access-Control-Allow-Headers
            value: '*'
          - key: Access-Control-Allow-Methods
            value: GET
      - pattern: /**/*.svg
        headers:
          - key: Access-Control-Allow-Origin
            value: '*'
          - key: Access-Control-Allow-Headers
            value: '*'
          - key: Access-Control-Allow-Methods
            value: GET
  - appRoot: apps/tables
    customHeaders:
      - pattern: /**/*.js
        headers:
          - key: Access-Control-Allow-Origin
            value: '*'
          - key: Access-Control-Allow-Headers
            value: '*'
          - key: Access-Control-Allow-Methods
            value: GET
      - pattern: /**/*.css
        headers:
          - key: Access-Control-Allow-Origin
            value: '*'
          - key: Access-Control-Allow-Headers
            value: '*'
          - key: Access-Control-Allow-Methods
            value: GET
      - pattern: /**/*.woff2
        headers:
          - key: Access-Control-Allow-Origin
            value: '*'
          - key: Access-Control-Allow-Headers
            value: '*'
          - key: Access-Control-Allow-Methods
            value: GET
      - pattern: /**/*.svg
        headers:
          - key: Access-Control-Allow-Origin
            value: '*'
          - key: Access-Control-Allow-Headers
            value: '*'
          - key: Access-Control-Allow-Methods
            value: GET
  - appRoot: apps/ai-widgets
    customHeaders:
      - pattern: /**/*.js
        headers:
          - key: Access-Control-Allow-Origin
            value: '*'
          - key: Access-Control-Allow-Headers
            value: '*'
          - key: Access-Control-Allow-Methods
            value: GET
      - pattern: /**/*.css
        headers:
          - key: Access-Control-Allow-Origin
            value: '*'
          - key: Access-Control-Allow-Headers
            value: '*'
          - key: Access-Control-Allow-Methods
            value: GET
      - pattern: /**/*.woff2
        headers:
          - key: Access-Control-Allow-Origin
            value: '*'
          - key: Access-Control-Allow-Headers
            value: '*'
          - key: Access-Control-Allow-Methods
            value: GET
      - pattern: /**/*.svg
        headers:
          - key: Access-Control-Allow-Origin
            value: '*'
          - key: Access-Control-Allow-Headers
            value: '*'
          - key: Access-Control-Allow-Methods
            value: GET
  - appRoot: apps/widget-builder
    customHeaders:
      - pattern: /**/*.js
        headers:
          - key: Access-Control-Allow-Origin
            value: '*'
          - key: Access-Control-Allow-Headers
            value: '*'
          - key: Access-Control-Allow-Methods
            value: GET
      - pattern: /**/*.css
        headers:
          - key: Access-Control-Allow-Origin
            value: '*'
          - key: Access-Control-Allow-Headers
            value: '*'
          - key: Access-Control-Allow-Methods
            value: GET
      - pattern: /**/*.woff2
        headers:
          - key: Access-Control-Allow-Origin
            value: '*'
          - key: Access-Control-Allow-Headers
            value: '*'
          - key: Access-Control-Allow-Methods
            value: GET
      - pattern: /**/*.svg
        headers:
          - key: Access-Control-Allow-Origin
            value: '*'
          - key: Access-Control-Allow-Headers
            value: '*'
          - key: Access-Control-Allow-Methods
            value: GET
  - appRoot: apps/page-admin
    customHeaders:
      - pattern: /**/*.js
        headers:
          - key: Access-Control-Allow-Origin
            value: '*'
          - key: Access-Control-Allow-Headers
            value: '*'
          - key: Access-Control-Allow-Methods
            value: GET
      - pattern: /**/*.css
        headers:
          - key: Access-Control-Allow-Origin
            value: '*'
          - key: Access-Control-Allow-Headers
            value: '*'
          - key: Access-Control-Allow-Methods
            value: GET
      - pattern: /**/*.woff2
        headers:
          - key: Access-Control-Allow-Origin
            value: '*'
          - key: Access-Control-Allow-Headers
            value: '*'
          - key: Access-Control-Allow-Methods
            value: GET
      - pattern: /**/*.svg
        headers:
          - key: Access-Control-Allow-Origin
            value: '*'
          - key: Access-Control-Allow-Headers
            value: '*'
          - key: Access-Control-Allow-Methods
            value: GET

  - appRoot: apps/page-builder
    customHeaders:
      - pattern: /**/*.js
        headers:
          - key: Access-Control-Allow-Origin
            value: '*'
          - key: Access-Control-Allow-Headers
            value: '*'
          - key: Access-Control-Allow-Methods
            value: GET
      - pattern: /**/*.css
        headers:
          - key: Access-Control-Allow-Origin
            value: '*'
          - key: Access-Control-Allow-Headers
            value: '*'
          - key: Access-Control-Allow-Methods
            value: GET
      - pattern: /**/*.woff2
        headers:
          - key: Access-Control-Allow-Origin
            value: '*'
          - key: Access-Control-Allow-Headers
            value: '*'
          - key: Access-Control-Allow-Methods
            value: GET
      - pattern: /**/*.svg
        headers:
          - key: Access-Control-Allow-Origin
            value: '*'
          - key: Access-Control-Allow-Headers
            value: '*'
          - key: Access-Control-Allow-Methods
            value: GET

  - appRoot: apps/ai-studio
    customHeaders:
      - pattern: /**/*.js
        headers:
          - key: Access-Control-Allow-Origin
            value: '*'
          - key: Access-Control-Allow-Headers
            value: '*'
          - key: Access-Control-Allow-Methods
            value: GET
      - pattern: /**/*.css
        headers:
          - key: Access-Control-Allow-Origin
            value: '*'
          - key: Access-Control-Allow-Headers
            value: '*'
          - key: Access-Control-Allow-Methods
            value: GET
      - pattern: /**/*.woff2
        headers:
          - key: Access-Control-Allow-Origin
            value: '*'
          - key: Access-Control-Allow-Headers
            value: '*'
          - key: Access-Control-Allow-Methods
            value: GET
      - pattern: /**/*.svg
        headers:
          - key: Access-Control-Allow-Origin
            value: '*'
          - key: Access-Control-Allow-Headers
            value: '*'
          - key: Access-Control-Allow-Methods
            value: GET
  - appRoot: apps/node-editor
    customHeaders:
      - pattern: /**/*.js
        headers:
          - key: Access-Control-Allow-Origin
            value: '*'
          - key: Access-Control-Allow-Headers
            value: '*'
          - key: Access-Control-Allow-Methods
            value: GET
      - pattern: /**/*.css
        headers:
          - key: Access-Control-Allow-Origin
            value: '*'
          - key: Access-Control-Allow-Headers
            value: '*'
          - key: Access-Control-Allow-Methods
            value: GET
      - pattern: /**/*.woff2
        headers:
          - key: Access-Control-Allow-Origin
            value: '*'
          - key: Access-Control-Allow-Headers
            value: '*'
          - key: Access-Control-Allow-Methods
            value: GET
      - pattern: /**/*.svg
        headers:
          - key: Access-Control-Allow-Origin
            value: '*'
          - key: Access-Control-Allow-Headers
            value: '*'
          - key: Access-Control-Allow-Methods
            value: GET
