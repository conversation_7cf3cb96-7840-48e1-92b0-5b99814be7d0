import axios, { AxiosInstance, AxiosError } from 'axios';
import logger from '../logger';
import NotificationService from '../notifications';
import { IFailResponse } from '@resola-ai/models';
import { HTTP_ERROR_STATUS } from '@resola-ai/shared-constants';

export class AxiosService {
  instance: AxiosInstance;
  private accessToken: string;
  private orgId: string;
  private disabledLoggerPathnames: string[] = ['/ai-widgets', '/crm'];

  constructor() {
    this.accessToken = '';
    this.orgId = '';
    this.instance = axios.create({
      baseURL: '',
      headers: {},
    });
  }

  init(apiServerUrl: string, t?: (key: string, options?: Record<string, any>) => string) {
    this.instance = axios.create({
      baseURL: apiServerUrl,
      headers: {
        'Content-Type': 'application/json',
      },
    });

    this.instance.interceptors.request.use(
      config => {
        const token = {
          accessToken: this.accessToken,
          refreshToken: 'refresh-token',
        };

        if (token?.accessToken) {
          config.headers.Authorization = `Bearer ${token.accessToken}`;
        }

        if (this.orgId?.length > 0) {
          config.headers['x-org-id'] = this.orgId;
        }

        return config;
      },
      error => {
        return Promise.reject(error);
      }
    );

    this.instance.interceptors.response.use(
      response => {
        return response;
      },
      (error: AxiosError<IFailResponse>) => {
        if (
          typeof window !== 'undefined' &&
          this.disabledLoggerPathnames.some(path => window?.location?.pathname?.includes(path))
        ) {
          throw error;
        }

        logger.error(error);
        const errorMessage = error?.response?.data?.message;
        const errorCode = error?.response?.data?.code;
        if (errorMessage && error.response?.status !== HTTP_ERROR_STATUS.UNAUTHORIZED) {
          NotificationService.sendNotification(
            t && errorCode ? t(`errors.${errorCode}`) : errorMessage,
            '',
            'error'
          );
        }
        return Promise.reject(error);
      }
    );
  }

  setAccessToken(accessToken: string) {
    this.accessToken = accessToken;
  }

  getAccessToken() {
    return this.accessToken;
  }

  setOrgId(orgId: string) {
    this.orgId = orgId;
  }

  getOrgId() {
    return this.orgId;
  }
}

const axiosService = new AxiosService();
export default axiosService;
