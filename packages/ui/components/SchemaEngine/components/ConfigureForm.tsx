import { useCallback, useEffect, useMemo } from 'react';
import { Schema, ObjectType, ConfigureFormInput, SchemaFormCallback } from '../types';
import { Stack } from '@mantine/core';
import { StepperActions, Step, Stepper } from './Stepper';
import { ScrollArea } from '@mantine/core';
import FormEngine from './FormEngine';
import { FormProvider, useForm } from 'react-hook-form';
import { useCallbackRef } from '@mantine/hooks';
import { ComboboxNode } from '../../FormChema/components/ComboboxSelectDataPoint';
import { useTranslate } from '@tolgee/react';
import { NODE_NAME } from '../../../constants';

interface ConfigureFormProps {
  schema: Schema;
  objectType: ObjectType;
  activeStep?: number;
  completedStep?: number;
  steps?: Step[];
  onStepChange?: (step: number) => void;
  form: ConfigureFormInput;
  onFormChange?: SchemaFormCallback;
  defaultToFirstObject?: boolean;
  previousNodes?: ComboboxNode[];
  onClose?: () => void;
}

const ConfigureForm = (props: ConfigureFormProps) => {
  const {
    schema,
    form = {},
    steps = [],
    activeStep = 0,
    completedStep = -1,
    objectType,
    onStepChange,
    onFormChange,
    defaultToFirstObject,
    previousNodes,
    onClose,
  } = props;
  const { t } = useTranslate();
  const itemObject = objectType === 'action' ? schema?.actions : schema?.triggers;
  const selectedObject = form[objectType];

  const defaultValues = {
    ...form.settings,
  };

  const methods = useForm({ defaultValues });
  const { watch, formState } = methods;
  const { isValid } = formState;

  const commonProperties = useMemo(() => {
    let properties = schema?.settings;
    if (!properties) return {};

    if (schema.name === NODE_NAME.PATH) {
      properties = properties?.paths?.items?.properties || {};
    }

    return Object.fromEntries(
      Object.entries(properties).filter(([_, v]: [string, any]) => v['type'] !== 'credential')
    );
  }, [schema.name, schema?.settings]);

  const selectedProperties = useMemo(() => {
    if (!selectedObject) {
      return {};
    }

    const selectedObjectKey = defaultToFirstObject ? Object.keys(itemObject)[0] : selectedObject;
    if (!selectedObjectKey) return {};
    return (
      itemObject[selectedObjectKey]?.properties ||
      itemObject[selectedObjectKey]?.data?.properties ||
      {}
    );
  }, [itemObject, selectedObject, defaultToFirstObject]);

  const formChangeCallback = useCallbackRef(onFormChange);

  const handleNext = useCallback(() => {
    onClose?.();
  }, [onClose]);

  const updateCompletedStep = useCallback(() => {
    if (!isValid || activeStep <= completedStep) return;
    const formValuesTmp = watch();
    formChangeCallback({ ...formValuesTmp, completedFormStep: activeStep }, 'completedFormStep');
  }, [activeStep, completedStep, formChangeCallback, isValid, watch]);

  useEffect(() => {
    updateCompletedStep();
  }, [updateCompletedStep]);

  useEffect(() => {
    const { unsubscribe } = watch((value, { name }) => {
      if (!name) return;
      formChangeCallback(value, name);
    });
    return () => unsubscribe();
  }, [watch, formChangeCallback, objectType]);

  return (
    <FormProvider {...methods}>
      <ScrollArea sx={{ flex: 1 }}>
        <Stack gap='lg'>
          <Stepper
            steps={steps}
            activeStep={activeStep}
            completedStep={completedStep}
            currentStepValid={isValid}
            onStepChange={onStepChange}
          />
          <FormEngine
            commonProperties={commonProperties}
            selectedProperties={selectedProperties}
            previousNodes={previousNodes}
          />
        </Stack>
      </ScrollArea>

      <StepperActions
        activeStep={activeStep}
        nextBtnLabel={isValid ? t('button.continue') : t('form.requiredFields')}
        disabledNextBtn={!isValid}
        showBackBtn={false}
        onNext={handleNext}
      />
    </FormProvider>
  );
};

export default ConfigureForm;
