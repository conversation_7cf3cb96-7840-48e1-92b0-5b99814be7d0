{"name": "deca-kb", "displayName": "DECA Knowledge Base", "icon": "📚", "group": "deca", "category": ["deca-cloud-tools"], "description": "Interact with DECA Knowledge Base API to manage folders, knowledge bases, articles, article templates, documents, and perform searches.", "version": "1.0.0", "type": "node", "settings": {"credential": {"name": "credential", "displayName": "Credential", "type": "credential", "credentialTypes": ["deca.kb.api_key"], "required": true, "description": "Credential to use for DECA KB API", "order": 1}}, "schemas": {"properties": {"articleContent": {"name": "articleContent", "displayName": "Article Content", "description": "Article content structure", "properties": {"title": {"name": "title", "displayName": "Title", "type": "string", "required": true, "description": "Article title"}, "content": {"name": "content", "displayName": "Content", "type": "string", "required": true, "description": "Article content (max 5000 chars)"}, "contentRaw": {"name": "contentRaw", "displayName": "Raw Content", "type": "string", "required": true, "description": "Article raw content (max 50000 chars)"}, "keywords": {"name": "keywords", "displayName": "Keywords", "type": "array", "description": "Article keywords"}}}, "customDataInput": {"name": "customDataInput", "displayName": "Custom Data Input", "description": "Custom data input structure", "properties": {"title": {"name": "title", "displayName": "Title", "type": "string", "required": true, "description": "Custom data title"}, "description": {"name": "description", "displayName": "Description", "type": "string", "description": "Custom data description"}, "dataType": {"name": "dataType", "displayName": "Data Type", "type": "string", "required": true, "description": "Type of custom data"}, "value": {"name": "value", "displayName": "Value", "type": "string", "description": "Value content"}}}, "articleResponse": {"name": "articleResponse", "displayName": "Article Response", "description": "Article response data", "properties": {"id": {"name": "id", "displayName": "Article ID", "type": "string", "description": "Unique identifier for the article"}, "title": {"name": "title", "displayName": "Title", "type": "string", "description": "Article title"}, "content": {"name": "content", "displayName": "Content", "type": "string", "description": "Article content"}, "status": {"name": "status", "displayName": "Status", "type": "string", "description": "Article status"}}}, "kbResponse": {"name": "kbResponse", "displayName": "Knowledge Base Response", "description": "Knowledge base response data", "properties": {"id": {"name": "id", "displayName": "Knowledge Base ID", "type": "string", "description": "Unique identifier for the knowledge base"}, "name": {"name": "name", "displayName": "Name", "type": "string", "description": "Knowledge base name"}, "accessLevel": {"name": "accessLevel", "displayName": "Access Level", "type": "string", "description": "Access level"}, "baseType": {"name": "baseType", "displayName": "Base Type", "type": "string", "description": "Type of knowledge base"}}}, "listResponse": {"name": "listResponse", "displayName": "List Response", "description": "Generic list response structure", "properties": {"data": {"name": "data", "displayName": "Data", "type": "array", "description": "List of items"}, "cursor": {"name": "cursor", "displayName": "<PERSON><PERSON><PERSON>", "type": "string", "description": "Pagination cursor"}, "hasMore": {"name": "hasMore", "displayName": "Has <PERSON>", "type": "boolean", "description": "Whether there are more items"}, "total": {"name": "total", "displayName": "Total", "type": "number", "description": "Total number of items"}}}, "folderResponse": {"name": "folderResponse", "displayName": "Folder Response", "description": "Folder response data", "properties": {"id": {"name": "id", "displayName": "Folder ID", "type": "string", "description": "Unique identifier for the folder"}, "name": {"name": "name", "displayName": "Name", "type": "string", "description": "Folder name"}, "parentDirId": {"name": "parentDirId", "displayName": "Parent Directory ID", "type": "string", "description": "Parent directory ID"}, "path": {"name": "path", "displayName": "Path", "type": "string", "description": "Folder path"}}}, "searchResponse": {"name": "searchResponse", "displayName": "Search Response", "description": "Search results data", "properties": {"results": {"name": "results", "displayName": "Results", "type": "array", "description": "Search results"}, "query": {"name": "query", "displayName": "Query", "type": "string", "description": "Search query"}, "total": {"name": "total", "displayName": "Total", "type": "number", "description": "Total number of results"}, "took": {"name": "took", "displayName": "Took", "type": "number", "description": "Time taken for search in milliseconds"}}}}}, "credentials": {"deca.kb.api_key": {"name": "deca.kb.api_key", "displayName": "DECA KB API Key", "description": "API Key for authenticating with DECA Knowledge Base", "properties": [{"name": "api_key", "displayName": "API Key", "description": "Your DECA Knowledge Base API key", "type": "password", "required": true, "order": 1}, {"name": "base_url", "displayName": "Base URL", "description": "The base URL for the DECA KB API", "type": "text", "required": false, "default": "https://api.deca-kb.com", "order": 2}], "order": 1}}, "triggers": {"article_created": {"name": "article_created", "displayName": "Article Created", "description": "Triggered when a new article is created in the knowledge base", "data": {"$ref": "#/schemas/articleResponse"}, "order": 1}, "article_updated": {"name": "article_updated", "displayName": "Article Updated", "description": "Triggered when an article is updated in the knowledge base", "data": {"$ref": "#/schemas/articleResponse"}, "order": 2}, "kb_created": {"name": "kb_created", "displayName": "Knowledge Base Created", "description": "Triggered when a new knowledge base is created", "data": {"$ref": "#/schemas/kbResponse"}, "order": 3}}, "actions": {"folder_list": {"name": "folder_list", "displayName": "List Folders", "description": "Retrieve a list of folders", "properties": {"parentDirId": {"name": "parentDirId", "displayName": "Parent Directory ID", "type": "text", "description": "Parent directory ID (default: /root)", "required": false, "order": 1}, "depth": {"name": "depth", "displayName": "De<PERSON><PERSON>", "type": "number", "description": "Maximum depth to traverse (max: 5, default: 2)", "required": false, "order": 2}, "take": {"name": "take", "displayName": "Take", "type": "number", "description": "Number of items to take (max: 200, default: 100)", "required": false, "order": 3}}, "data": {"$ref": "#/schemas/listResponse"}, "order": 1}, "folder_create": {"name": "folder_create", "displayName": "Create Folder", "description": "Create a new folder", "properties": {"name": {"name": "name", "displayName": "Name", "type": "text", "required": true, "description": "Folder name", "order": 1}, "parentDirId": {"name": "parentDirId", "displayName": "Parent Directory ID", "type": "text", "description": "Parent directory ID (default: /root)", "required": false, "order": 2}}, "data": {"$ref": "#/schemas/folderResponse"}, "order": 2}, "kb_list": {"name": "kb_list", "displayName": "List Knowledge Bases", "description": "Retrieve a list of knowledge bases", "properties": {"parentDirId": {"name": "parentDirId", "displayName": "Parent Directory ID", "type": "text", "description": "Parent directory ID (default: /root)", "required": false, "order": 1}, "cursor": {"name": "cursor", "displayName": "<PERSON><PERSON><PERSON>", "type": "text", "description": "Pagination cursor", "required": false, "order": 2}, "take": {"name": "take", "displayName": "Take", "type": "number", "description": "Number of items to take (max: 100, default: 10)", "required": false, "order": 3}}, "data": {"$ref": "#/schemas/listResponse"}, "order": 3}, "kb_create": {"name": "kb_create", "displayName": "Create Knowledge Base", "description": "Create a new knowledge base", "properties": {"name": {"name": "name", "displayName": "Name", "type": "text", "required": true, "description": "Knowledge base name", "order": 1}, "description": {"name": "description", "displayName": "Description", "type": "text", "description": "Knowledge base description", "required": false, "order": 2}, "accessLevel": {"name": "accessLevel", "displayName": "Access Level", "type": "options", "required": true, "options": [{"name": "Private", "value": "private"}, {"name": "Public", "value": "public"}], "description": "Access level", "order": 3}, "baseType": {"name": "baseType", "displayName": "Base Type", "type": "options", "required": true, "options": [{"name": "Document", "value": "document"}, {"name": "Article", "value": "article"}, {"name": "From Datasource", "value": "from_datasource"}], "description": "Type of knowledge base", "order": 4}}, "data": {"$ref": "#/schemas/kbResponse"}, "order": 4}, "article_list": {"name": "article_list", "displayName": "List Articles", "description": "Retrieve a list of articles", "properties": {"baseId": {"name": "baseId", "displayName": "Knowledge Base ID", "type": "text", "description": "Knowledge base ID to list articles from", "required": false, "order": 1}, "status": {"name": "status", "displayName": "Status", "type": "text", "description": "Article status filter", "required": false, "order": 2}, "query": {"name": "query", "displayName": "Search Query", "type": "text", "description": "Search query", "required": false, "order": 3}}, "data": {"$ref": "#/schemas/listResponse"}, "order": 5}, "article_create": {"name": "article_create", "displayName": "Create Article", "description": "Create a new article in a knowledge base", "properties": {"baseId": {"name": "baseId", "displayName": "Knowledge Base ID", "type": "text", "required": true, "description": "Knowledge base ID", "order": 1}, "title": {"name": "title", "displayName": "Title", "type": "text", "required": true, "description": "Article title", "order": 2}, "content": {"name": "content", "displayName": "Content", "type": "text", "required": true, "description": "Article content", "order": 3}, "contentRaw": {"name": "contentRaw", "displayName": "Raw Content", "type": "text", "required": true, "description": "Article raw content", "order": 4}, "keywords": {"name": "keywords", "displayName": "Keywords", "type": "array", "description": "Article keywords", "required": false, "order": 5}}, "data": {"$ref": "#/schemas/articleResponse"}, "order": 6}, "search": {"name": "search", "displayName": "General Search", "description": "Search across all entities", "properties": {"query": {"name": "query", "displayName": "Search Query", "type": "text", "required": true, "description": "Search query", "order": 1}, "entities": {"name": "entities", "displayName": "Entities", "type": "multiOptions", "options": [{"name": "Folder", "value": "folder"}, {"name": "Knowledge Base", "value": "kb"}, {"name": "Article", "value": "article"}, {"name": "Article Template", "value": "article_template"}], "description": "Entity types to search", "required": false, "order": 2}}, "data": {"$ref": "#/schemas/searchResponse"}, "order": 7}, "folder_retrieve": {"name": "folder_retrieve", "displayName": "Retrieve Folder", "description": "Retrieve a specific folder by ID", "properties": {"folderId": {"name": "folderId", "displayName": "Folder ID", "type": "text", "required": true, "description": "Folder ID to retrieve", "order": 1}}, "data": {"$ref": "#/schemas/folderResponse"}, "order": 8}, "folder_update": {"name": "folder_update", "displayName": "Update Folder", "description": "Update an existing folder", "properties": {"folderId": {"name": "folderId", "displayName": "Folder ID", "type": "text", "required": true, "description": "Folder ID to update", "order": 1}, "name": {"name": "name", "displayName": "Name", "type": "text", "required": false, "description": "New folder name", "order": 2}}, "data": {"$ref": "#/schemas/folderResponse"}, "order": 9}, "folder_delete": {"name": "folder_delete", "displayName": "Delete Folder", "description": "Delete a folder by ID", "properties": {"folderId": {"name": "folderId", "displayName": "Folder ID", "type": "text", "required": true, "description": "Folder ID to delete", "order": 1}}, "data": {"$ref": "#/schemas/folderResponse"}, "order": 10}, "kb_retrieve": {"name": "kb_retrieve", "displayName": "Retrieve Knowledge Base", "description": "Retrieve a specific knowledge base by ID", "properties": {"kbId": {"name": "kbId", "displayName": "Knowledge Base ID", "type": "text", "required": true, "description": "Knowledge base ID to retrieve", "order": 1}}, "data": {"$ref": "#/schemas/kbResponse"}, "order": 11}, "kb_update": {"name": "kb_update", "displayName": "Update Knowledge Base", "description": "Update an existing knowledge base", "properties": {"kbId": {"name": "kbId", "displayName": "Knowledge Base ID", "type": "text", "required": true, "description": "Knowledge base ID to update", "order": 1}, "name": {"name": "name", "displayName": "Name", "type": "text", "required": false, "description": "New knowledge base name", "order": 2}, "description": {"name": "description", "displayName": "Description", "type": "text", "required": false, "description": "New knowledge base description", "order": 3}}, "data": {"$ref": "#/schemas/kbResponse"}, "order": 12}, "kb_delete": {"name": "kb_delete", "displayName": "Delete Knowledge Base", "description": "Delete a knowledge base by ID", "properties": {"kbId": {"name": "kbId", "displayName": "Knowledge Base ID", "type": "text", "required": true, "description": "Knowledge base ID to delete", "order": 1}}, "data": {"$ref": "#/schemas/kbResponse"}, "order": 13}, "article_retrieve": {"name": "article_retrieve", "displayName": "Retrieve Article", "description": "Retrieve a specific article by ID", "properties": {"articleId": {"name": "articleId", "displayName": "Article ID", "type": "text", "required": true, "description": "Article ID to retrieve", "order": 1}}, "data": {"$ref": "#/schemas/articleResponse"}, "order": 14}, "article_update": {"name": "article_update", "displayName": "Update Article", "description": "Update an existing article", "properties": {"articleId": {"name": "articleId", "displayName": "Article ID", "type": "text", "required": true, "description": "Article ID to update", "order": 1}, "title": {"name": "title", "displayName": "Title", "type": "text", "required": false, "description": "New article title", "order": 2}, "content": {"name": "content", "displayName": "Content", "type": "text", "required": false, "description": "New article content", "order": 3}}, "data": {"$ref": "#/schemas/articleResponse"}, "order": 15}, "article_delete": {"name": "article_delete", "displayName": "Delete Article", "description": "Delete an article by ID", "properties": {"articleId": {"name": "articleId", "displayName": "Article ID", "type": "text", "required": true, "description": "Article ID to delete", "order": 1}}, "data": {"$ref": "#/schemas/articleResponse"}, "order": 16}, "search_articles": {"name": "search_articles", "displayName": "Search Articles", "description": "Search specifically for articles", "properties": {"query": {"name": "query", "displayName": "Search Query", "type": "text", "required": true, "description": "Search query for articles", "order": 1}, "baseId": {"name": "baseId", "displayName": "Knowledge Base ID", "type": "text", "required": false, "description": "Limit search to specific knowledge base", "order": 2}}, "data": {"$ref": "#/schemas/searchResponse"}, "order": 17}, "article_template_list": {"name": "article_template_list", "displayName": "List Article Templates", "description": "Retrieve a list of article templates", "properties": {"baseId": {"name": "baseId", "displayName": "Knowledge Base ID", "type": "text", "required": false, "description": "Knowledge base ID to list templates from", "order": 1}}, "data": {"$ref": "#/schemas/listResponse"}, "order": 18}, "article_template_create": {"name": "article_template_create", "displayName": "Create Article Template", "description": "Create a new article template", "properties": {"name": {"name": "name", "displayName": "Template Name", "type": "text", "required": true, "description": "Template name", "order": 1}, "content": {"name": "content", "displayName": "Template Content", "type": "text", "required": true, "description": "Template content", "order": 2}}, "data": {"$ref": "#/schemas/articleResponse"}, "order": 19}, "article_template_retrieve": {"name": "article_template_retrieve", "displayName": "Retrieve Article Template", "description": "Retrieve a specific article template by ID", "properties": {"templateId": {"name": "templateId", "displayName": "Template ID", "type": "text", "required": true, "description": "Template ID to retrieve", "order": 1}}, "data": {"$ref": "#/schemas/articleResponse"}, "order": 20}, "article_template_update": {"name": "article_template_update", "displayName": "Update Article Template", "description": "Update an existing article template", "properties": {"templateId": {"name": "templateId", "displayName": "Template ID", "type": "text", "required": true, "description": "Template ID to update", "order": 1}, "name": {"name": "name", "displayName": "Template Name", "type": "text", "required": false, "description": "New template name", "order": 2}, "content": {"name": "content", "displayName": "Template Content", "type": "text", "required": false, "description": "New template content", "order": 3}}, "data": {"$ref": "#/schemas/articleResponse"}, "order": 21}, "article_template_delete": {"name": "article_template_delete", "displayName": "Delete Article Template", "description": "Delete an article template by ID", "properties": {"templateId": {"name": "templateId", "displayName": "Template ID", "type": "text", "required": true, "description": "Template ID to delete", "order": 1}}, "data": {"$ref": "#/schemas/articleResponse"}, "order": 22}, "document_list": {"name": "document_list", "displayName": "List Documents", "description": "Retrieve a list of documents", "properties": {"baseId": {"name": "baseId", "displayName": "Knowledge Base ID", "type": "text", "required": false, "description": "Knowledge base ID to list documents from", "order": 1}}, "data": {"$ref": "#/schemas/listResponse"}, "order": 23}, "document_retrieve": {"name": "document_retrieve", "displayName": "Retrieve Document", "description": "Retrieve a specific document by ID", "properties": {"documentId": {"name": "documentId", "displayName": "Document ID", "type": "text", "required": true, "description": "Document ID to retrieve", "order": 1}}, "data": {"$ref": "#/schemas/articleResponse"}, "order": 24}, "document_upload": {"name": "document_upload", "displayName": "Upload Document", "description": "Upload a new document", "properties": {"file": {"name": "file", "displayName": "File", "type": "file", "required": true, "description": "File to upload", "order": 1}, "baseId": {"name": "baseId", "displayName": "Knowledge Base ID", "type": "text", "required": true, "description": "Knowledge base ID to upload to", "order": 2}}, "data": {"$ref": "#/schemas/articleResponse"}, "order": 25}, "document_update": {"name": "document_update", "displayName": "Update Document", "description": "Update an existing document", "properties": {"documentId": {"name": "documentId", "displayName": "Document ID", "type": "text", "required": true, "description": "Document ID to update", "order": 1}, "title": {"name": "title", "displayName": "Title", "type": "text", "required": false, "description": "New document title", "order": 2}}, "data": {"$ref": "#/schemas/articleResponse"}, "order": 26}, "document_delete": {"name": "document_delete", "displayName": "Delete Document", "description": "Delete a document by ID", "properties": {"documentId": {"name": "documentId", "displayName": "Document ID", "type": "text", "required": true, "description": "Document ID to delete", "order": 1}}, "data": {"$ref": "#/schemas/articleResponse"}, "order": 27}, "document_download_link": {"name": "document_download_link", "displayName": "Get Document Download Link", "description": "Get a download link for a document", "properties": {"documentId": {"name": "documentId", "displayName": "Document ID", "type": "text", "required": true, "description": "Document ID to get download link for", "order": 1}}, "data": {"$ref": "#/schemas/articleResponse"}, "order": 28}, "article_shortcut_create": {"name": "article_shortcut_create", "displayName": "Create Article Shortcut", "description": "Create a shortcut to an existing article", "properties": {"articleId": {"name": "articleId", "displayName": "Article ID", "type": "text", "required": true, "description": "Article ID to create shortcut for", "order": 1}, "targetBaseId": {"name": "targetBaseId", "displayName": "Target Knowledge Base ID", "type": "text", "required": true, "description": "Knowledge base ID to create shortcut in", "order": 2}}, "data": {"$ref": "#/schemas/articleResponse"}, "order": 29}}}