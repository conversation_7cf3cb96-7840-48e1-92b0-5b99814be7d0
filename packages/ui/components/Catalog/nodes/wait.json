{"name": "wait", "displayName": "Wait", "icon": "⏱️", "group": "core", "category": ["builtin-popular", "logic"], "description": "Pause workflow execution for a specified duration, until a specific time, or until an event occurs", "settings": {"timezone": {"name": "timezone", "displayName": "Timezone", "type": "options", "description": "The timezone to use for the wait", "options": [{"name": "UTC", "value": "UTC"}, {"name": "America/New_York", "value": "America/New_York"}, {"name": "Europe/London", "value": "Europe/London"}, {"name": "Asia/Tokyo", "value": "Asia/Tokyo"}, {"name": "Australia/Sydney", "value": "Australia/Sydney"}], "default": "Asia/Tokyo"}}, "schemas": {"waitForData": {"properties": {"waitDuration": {"name": "waitDuration", "displayName": "Wait Duration", "type": "number", "description": "The actual duration waited in milliseconds", "order": 1}, "startTime": {"name": "startTime", "displayName": "Start Time", "type": "string", "description": "The ISO timestamp when the wait started", "order": 2}, "endTime": {"name": "endTime", "displayName": "End Time", "type": "string", "description": "The ISO timestamp when the wait ended", "order": 3}}}, "waitUntilData": {"properties": {"targetTime": {"name": "targetTime", "displayName": "Target Time", "type": "string", "description": "The ISO timestamp of the target time waited for", "order": 1}, "startTime": {"name": "startTime", "displayName": "Start Time", "type": "string", "description": "The ISO timestamp when the wait started", "order": 2}, "endTime": {"name": "endTime", "displayName": "End Time", "type": "string", "description": "The ISO timestamp when the wait ended", "order": 3}, "waitDuration": {"name": "waitDuration", "displayName": "Wait Duration", "type": "number", "description": "The actual duration waited in milliseconds", "order": 4}, "wasInPast": {"name": "wasInPast", "displayName": "Was In Past", "type": "boolean", "description": "Whether the target time was in the past when executed", "order": 5}}}}, "credentials": {}, "triggers": {}, "actions": {"waitFor": {"name": "waitFor", "displayName": "Wait For", "description": "Pause execution for a specified duration", "order": 1, "properties": {"duration": {"name": "duration", "displayName": "Duration", "description": "How long to wait", "type": "number", "default": "5000", "order": 1}, "unit": {"name": "unit", "displayName": "Time Unit", "description": "The unit of time for the duration", "type": "options", "options": [{"name": "Seconds", "value": "seconds"}, {"name": "Minutes", "value": "minutes"}, {"name": "Hours", "value": "hours"}, {"name": "Days", "value": "days"}, {"name": "Weeks", "value": "weeks"}, {"name": "Months", "value": "months"}], "default": "seconds", "order": 2}}, "data": {"$ref": "#/schemas/waitForData"}}, "waitUntil": {"name": "waitUntil", "displayName": "Wait Until", "description": "Pause execution until a specific date and time", "order": 2, "properties": {"dateTime": {"name": "dateTime", "displayName": "Date and Time", "description": "The date and time to wait until (ISO format)", "type": "text", "default": "", "order": 1}, "useRelative": {"name": "useRelative", "displayName": "Use Relative Time", "description": "Use a relative time instead of absolute date/time", "type": "checkbox", "default": false, "order": 2}, "relativeTime": {"name": "relativeTime", "displayName": "Relative Time", "description": "Time from now (only used if Use Relative Time is checked)", "type": "number", "default": "30", "order": 3}, "relativeUnit": {"name": "relativeUnit", "displayName": "Relative Time Unit", "description": "The unit for relative time", "type": "options", "options": [{"name": "Minutes", "value": "minutes"}, {"name": "Hours", "value": "hours"}, {"name": "Days", "value": "days"}, {"name": "Weeks", "value": "weeks"}, {"name": "Months", "value": "months"}], "default": "minutes", "order": 4}, "pastDateHandling": {"name": "pastDateHandling", "displayName": "How should we handle dates in the past?", "description": "What to do if the target date/time is in the past", "type": "options", "options": [{"name": "Continue if it's up to one day", "value": "continueIfUpToOneDay"}, {"name": "Continue if it's up to 15 minutes", "value": "continueIfUpTo15Minutes"}, {"name": "Continue if it's up to one hour", "value": "continueIfUpToOneHour"}, {"name": "Continue if it's up to one week", "value": "continueIfUpToOneWeek"}, {"name": "Continue if it's up to one month", "value": "continueIfUpToOneMonth"}, {"name": "Always continue", "value": "alwaysContinue"}, {"name": "Stop and throw an error", "value": "stopAndError"}], "default": "continueIfUpToOneDay", "order": 5}}, "data": {"$ref": "#/schemas/waitUntilData"}}}}