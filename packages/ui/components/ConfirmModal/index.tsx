import { Box, Flex, Modal, ModalProps, rem, Text } from '@mantine/core';
import { createStyles } from '@mantine/emotion';
import React from 'react';
import { useTranslation } from 'react-i18next';

import { resources } from '../../i18n';
import { DecaButton, DecaButtonProps } from '../DecaButton';

type CommonTranslationKey = keyof (typeof resources)['ja']['common'];

const useStyles = createStyles(theme => ({
  inner: {
    left: 0,
    right: 0,
    cursor: 'initial',
  },
  content: {
    marginLeft: rem(-16),
    marginRight: rem(-16),
    padding: rem(16),
  },
  withSeparator: {
    borderTop: `1px solid ${theme.colors.decaLight[2]}`,
    borderBottom: `1px solid ${theme.colors.decaLight[2]}`,
  },
}));

export interface ConfirmModalProps extends ModalProps {
  message?: string;
  cancelButtonProps?: Omit<DecaButtonProps, 'children'>;
  cancelText?: CommonTranslationKey | (string & {});
  confirmButtonProps?: Omit<DecaButtonProps, 'children'>;
  confirmText?: CommonTranslationKey | (string & {});
  isRemoving?: boolean;
  noSeparator?: boolean;
  isCancel?: boolean;
  onConfirm?: () => void;
}

const ConfirmModal: React.FC<ConfirmModalProps> = props => {
  const {
    children,
    classNames,
    cancelButtonProps,
    cancelText = 'cancel',
    confirmButtonProps,
    confirmText = 'confirm',
    isRemoving = false,
    onClose,
    onConfirm,
    message,
    noSeparator = false,
    isCancel = true,
    ...rest
  } = props;
  const { classes, cx } = useStyles();
  const { t } = useTranslation('common');

  return (
    <Modal
      centered
      classNames={{
        inner: classes.inner,
        ...classNames,
      }}
      onClose={onClose}
      {...rest}>
      <Box className={cx(classes.content, !noSeparator && classes.withSeparator)}>
        {message ? <Text>{message}</Text> : null}
        {children}
      </Box>
      <Flex justify='end' pt={rem(16)} gap={rem(16)}>
        {isCancel && (
          <DecaButton
            data-testid='ui-confirm-modal-cancel-button'
            variant='neutral'
            {...cancelButtonProps}
            onClick={onClose}>
            {t(cancelText, { defaultValue: cancelText })}
          </DecaButton>
        )}
        <DecaButton
          data-testid='ui-confirm-modal-confirm-button'
          variant={isRemoving ? 'negative' : 'primary'}
          onClick={onConfirm}
          {...confirmButtonProps}>
          {t(confirmText, { defaultValue: confirmText })}
        </DecaButton>
      </Flex>
    </Modal>
  );
};

export default ConfirmModal;
