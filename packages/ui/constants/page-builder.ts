import { IconCurrencyQuetzal } from '@tabler/icons-react';
import { IconArticle } from '@tabler/icons-react';

export const ICON_CONFIG = {
  icon: {
    component: IconArticle,
    sizes: {
      S: 20,
      M: 32,
      L: 40,
    },
  },
  'question-indicator': {
    component: IconCurrencyQuetzal,
    sizes: {
      S: 32,
      M: 40,
      L: 48,
    },
  },
} as const;

export const ICON_SIZE_MAP = {
  20: 'S',
  32: 'M',
  40: 'L',
} as const;

export const DEFAULT_SEARCH_RESULTS = [
  { id: 1, title: 'Getting Started with Pages', type: 'article' },
  { id: 2, title: 'Top Reasons For Choosing Us', type: 'article' },
  { id: 3, title: 'Sign Up', type: 'page' },
  { id: 4, title: 'Sign In', type: 'page' },
  { id: 5, title: 'Forgot Account', type: 'page' },
  { id: 6, title: 'OTP', type: 'page' },
  { id: 7, title: 'Log Out', type: 'page' },
] as const;

export const BUILDER_CONTAINER_ELEMENT_ID = 'builderContainerElement' as const;
export const PREVIEW_BUILDER_CONTAINER_ELEMENT_ID = 'previewBuilderContainerElement' as const;

export const DEFAULT_BOX_VALUES = {
  padding: {
    desktop: {
      top: 16,
      right: 16,
      bottom: 16,
      left: 16,
    },
    tablet: {
      top: 16,
      right: 16,
      bottom: 16,
      left: 16,
    },
    mobile: {
      top: 16,
      right: 16,
      bottom: 16,
      left: 16,
    },
  },
  backgroundColor: '#FFFFFF',
  textColor: '#000000',
  showDivider: true,
};
